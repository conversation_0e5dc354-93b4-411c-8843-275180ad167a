/**
 * <PERSON><PERSON><PERSON> to add test staff users to admin database
 * Creates sample management, reception, and teacher users for testing
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

// Test staff users to create
const staffUsers = [
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    role: 'management',
    name: 'John <PERSON>',
    server_type: 'staff'
  },
  {
    email: '<EMAIL>',
    password: 'Reception123!',
    role: 'reception',
    name: '<PERSON>',
    server_type: 'staff'
  },
  {
    email: '<EMAIL>',
    password: 'Teacher123!',
    role: 'teacher',
    name: '<PERSON>',
    server_type: 'staff'
  }
];

async function hashPassword(password) {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

async function addStaffUsers() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔄 Adding test staff users to admin database...');
    
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected to admin database');
    
    for (const user of staffUsers) {
      try {
        // Check if user already exists
        const existingUser = await client.query(
          'SELECT id FROM users WHERE email = $1',
          [user.email]
        );
        
        if (existingUser.rows.length > 0) {
          console.log(`ℹ️  User ${user.email} already exists, skipping...`);
          continue;
        }
        
        // Hash password
        const passwordHash = await hashPassword(user.password);
        
        // Insert user
        const result = await client.query(
          `INSERT INTO users (email, password_hash, role, name, server_type, is_active)
           VALUES ($1, $2, $3, $4, $5, true)
           RETURNING id, email, role, name`,
          [user.email, passwordHash, user.role, user.name, user.server_type]
        );
        
        const newUser = result.rows[0];
        console.log(`✅ Created ${user.role} user: ${newUser.name} (${newUser.email})`);
        
      } catch (userError) {
        console.error(`❌ Failed to create user ${user.email}:`, userError.message);
      }
    }
    
    // Verify staff users were created
    console.log('\n📋 Verifying staff users in database...');
    const staffUsersResult = await client.query(
      `SELECT id, email, role, name, is_active, server_type, created_at
       FROM users 
       WHERE server_type = 'staff'
       ORDER BY role, name`
    );
    
    console.log(`Found ${staffUsersResult.rows.length} staff users:`);
    staffUsersResult.rows.forEach(user => {
      console.log(`  - ${user.role}: ${user.name} (${user.email}) - ${user.is_active ? 'Active' : 'Inactive'}`);
    });
    
    client.release();
    await pool.end();
    
    console.log('\n🎉 Staff users setup completed successfully!');
    console.log('\n📝 Test credentials:');
    staffUsers.forEach(user => {
      console.log(`  ${user.role}: ${user.email} / ${user.password}`);
    });
    console.log('\n✨ You can now test staff server authentication with these credentials!');
    
  } catch (error) {
    console.error('❌ Failed to add staff users:', error.message);
    console.error('Error details:', error);
    process.exit(1);
  }
}

// Run script if executed directly
if (require.main === module) {
  addStaffUsers();
}

module.exports = { addStaffUsers };

/**
 * Admin Service Integration for Staff Server
 * Handles communication with the Admin Server for cross-system operations
 */

import { ActivityAction, ResourceType } from '../../../shared/types/activity-log';

// Admin server configuration
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
const ADMIN_SERVER_API_KEY = process.env.ADMIN_SERVER_API_KEY || '';

// Request timeout configuration
const REQUEST_TIMEOUT = 10000; // 10 seconds

/**
 * Base admin service client
 */
class AdminServiceClient {
  private baseUrl: string;
  private apiKey: string;

  constructor() {
    this.baseUrl = ADMIN_SERVER_URL;
    this.apiKey = ADMIN_SERVER_API_KEY;
  }

  /**
   * Make authenticated request to admin server
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const headers = {
      'Content-Type': 'application/json',
      'X-API-Key': this.apiKey,
      'X-Source-Service': 'staff-server',
      ...options.headers,
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

    try {
      const response = await fetch(url, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Admin service error: ${response.status} - ${errorText}`);
      }

      const result = await response.json();
      return result.data || result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Admin service request timeout');
      }
      
      console.error('Admin service request failed:', error);
      throw error;
    }
  }

  /**
   * Test connection to admin server
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.makeRequest('/api/health');
      return true;
    } catch (error) {
      console.error('Admin server connection test failed:', error);
      return false;
    }
  }

  /**
   * Authenticate staff user via admin server
   */
  async authenticateStaffUser(credentials: {
    email: string;
    password: string;
  }): Promise<{
    success: boolean;
    user?: {
      id: string;
      email: string;
      role: string;
      name: string;
      isActive: boolean;
    };
    token?: string;
    refreshToken?: string;
    expiresIn?: number;
    error?: string;
  }> {
    try {
      const response = await this.makeRequest('/api/staff-integration/auth', {
        method: 'POST',
        body: JSON.stringify(credentials),
      });

      return {
        success: true,
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken,
        expiresIn: response.expiresIn,
      };
    } catch (error) {
      console.error('Staff authentication via admin server failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed',
      };
    }
  }

  /**
   * Sync activity log to admin server
   */
  async syncActivityLog(activityData: {
    userId: string;
    action: ActivityAction;
    resourceType: ResourceType;
    resourceId?: string;
    oldValues?: any;
    newValues?: any;
    description?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    try {
      console.log('🔄 AdminService: Syncing activity log to admin server:', {
        userId: activityData.userId,
        action: activityData.action,
        resourceType: activityData.resourceType,
        description: activityData.description
      });

      const requestData = {
        ...activityData,
        sourceService: 'staff-server',
        timestamp: new Date().toISOString(),
      };

      console.log('📤 AdminService: Request data:', requestData);

      const result = await this.makeRequest('/api/staff-integration/activity-sync', {
        method: 'POST',
        body: JSON.stringify(requestData),
      });

      console.log('✅ AdminService: Activity log sync successful:', result);
    } catch (error) {
      console.error('❌ AdminService: Failed to sync activity log to admin server:', error);
      // Don't throw error to avoid breaking main operations
      throw error; // Re-throw to let the caller handle it
    }
  }

  /**
   * Get financial data for a student from admin server
   */
  async getStudentFinancialData(studentId: string): Promise<{
    totalPaid: number;
    outstandingBalance: number;
    lastPaymentDate?: string;
    paymentHistory: Array<{
      id: string;
      amount: number;
      date: string;
      method: string;
      status: string;
    }>;
  } | null> {
    try {
      return await this.makeRequest(`/api/staff-integration/student-financial/${studentId}`);
    } catch (error) {
      console.error('Failed to get student financial data:', error);
      return null;
    }
  }

  /**
   * Get payment status for multiple students from admin server
   */
  async getStudentsPaymentStatus(studentIds: string[]): Promise<{
    [studentId: string]: {
      paymentStatus: 'paid' | 'unpaid' | 'debt';
      debtAmount?: number;
      lastPaymentDate?: string;
    };
  }> {
    try {
      const response = await this.makeRequest('/api/staff-integration/students-payment-status', {
        method: 'POST',
        body: JSON.stringify({ studentIds }),
      });
      return response || {};
    } catch (error) {
      console.error('Failed to get students payment status:', error);
      return {};
    }
  }

  /**
   * Notify admin server of student enrollment for payment setup
   */
  async notifyStudentEnrollment(studentData: {
    id: string;
    firstName: string;
    lastName: string;
    email?: string;
    phone?: string;
    enrollmentDate: string;
    groupId?: string;
    groupName?: string;
  }): Promise<void> {
    try {
      await this.makeRequest('/api/staff-integration/student-enrollment', {
        method: 'POST',
        body: JSON.stringify(studentData),
      });
    } catch (error) {
      console.error('Failed to notify admin server of student enrollment:', error);
      // Don't throw error to avoid breaking enrollment process
    }
  }

  /**
   * Get cabinet/classroom availability from admin server
   */
  async getCabinetAvailability(date: string, timeSlot?: string): Promise<Array<{
    id: string;
    name: string;
    capacity: number;
    isAvailable: boolean;
    currentBooking?: {
      id: string;
      groupName: string;
      teacherName: string;
      startTime: string;
      endTime: string;
    };
  }> | null> {
    try {
      const params = new URLSearchParams({ date });
      if (timeSlot) params.append('timeSlot', timeSlot);
      
      return await this.makeRequest(`/api/cabinets/availability?${params}`);
    } catch (error) {
      console.error('Failed to get cabinet availability:', error);
      return null;
    }
  }

  /**
   * Request cabinet booking from admin server
   */
  async requestCabinetBooking(bookingData: {
    cabinetId: string;
    groupId: string;
    groupName: string;
    teacherId: string;
    teacherName: string;
    date: string;
    startTime: string;
    endTime: string;
    requestedBy: string;
  }): Promise<{
    success: boolean;
    bookingId?: string;
    error?: string;
  }> {
    try {
      return await this.makeRequest('/api/cabinets/book-request', {
        method: 'POST',
        body: JSON.stringify(bookingData),
      });
    } catch (error) {
      console.error('Failed to request cabinet booking:', error);
      return { success: false, error: 'Failed to communicate with admin server' };
    }
  }

  /**
   * Get KPI data from admin server for reporting
   */
  async getKPIData(params: {
    startDate: string;
    endDate: string;
    type?: 'financial' | 'operational' | 'all';
  }): Promise<{
    financial: {
      totalRevenue: number;
      totalPayments: number;
      outstandingBalance: number;
      paymentsByMonth: Array<{ month: string; amount: number }>;
    };
    operational: {
      totalStudents: number;
      activeStudents: number;
      newEnrollments: number;
      cabinetUtilization: number;
    };
  } | null> {
    try {
      const queryParams = new URLSearchParams(params);
      return await this.makeRequest(`/api/staff-integration/kpis?${queryParams}`);
    } catch (error) {
      console.error('Failed to get KPI data from admin server:', error);
      return null;
    }
  }

  /**
   * Get cabinet availability from admin server
   */
  async getCabinetAvailability(params: {
    date?: string;
    timeSlot?: string;
  }): Promise<{
    date: string;
    timeSlot: string | null;
    cabinets: Array<{
      id: string;
      name: string;
      capacity: number;
      isAvailable: boolean;
      currentBookings: Array<{
        id: string;
        startTime: string;
        endTime: string;
        purpose: string;
        bookedBy: string;
        status: string;
      }>;
    }>;
  } | null> {
    try {
      const queryParams = new URLSearchParams(params);
      return await this.makeRequest(`/api/staff-integration/cabinet-availability?${queryParams}`);
    } catch (error) {
      console.error('Failed to get cabinet availability from admin server:', error);
      return null;
    }
  }

  /**
   * Request cabinet booking from admin server
   */
  async requestCabinetBooking(bookingData: {
    cabinetId: string;
    groupId: string;
    groupName: string;
    teacherId: string;
    teacherName: string;
    date: string;
    startTime: string;
    endTime: string;
    requestedBy: string;
  }): Promise<{
    success: boolean;
    bookingId: string;
    cabinet: { id: string; name: string };
    booking: {
      id: string;
      date: string;
      startTime: string;
      endTime: string;
      purpose: string;
      status: string;
      createdAt: string;
    };
    group: {
      id: string;
      name: string;
      teacherId: string;
      teacherName: string;
    };
  } | null> {
    try {
      return await this.makeRequest('/api/staff-integration/cabinet-booking', {
        method: 'POST',
        body: JSON.stringify(bookingData),
      });
    } catch (error) {
      console.error('Failed to request cabinet booking from admin server:', error);
      return null;
    }
  }

  /**
   * Get consolidated reports from admin server
   */
  async getConsolidatedReports(params: {
    startDate?: string;
    endDate?: string;
    type?: 'financial' | 'operational' | 'summary' | 'detailed';
  }): Promise<{
    reportType: string;
    dateRange: { startDate: string; endDate: string };
    generatedAt: string;
    data: {
      financial?: any;
      operational?: any;
      detailed?: any;
    };
  } | null> {
    try {
      const queryParams = new URLSearchParams(params);
      return await this.makeRequest(`/api/staff-integration/reports?${queryParams}`);
    } catch (error) {
      console.error('Failed to get consolidated reports from admin server:', error);
      return null;
    }
  }

  /**
   * Sync student status changes to admin server
   */
  async syncStudentStatusChange(studentData: {
    id: string;
    oldStatus: string;
    newStatus: string;
    firstName: string;
    lastName: string;
    email?: string;
    changedBy: string;
    reason?: string;
  }): Promise<void> {
    try {
      await this.makeRequest('/api/staff-integration/student-status', {
        method: 'POST',
        body: JSON.stringify(studentData),
      });
    } catch (error) {
      console.error('Failed to sync student status change:', error);
      // Don't throw error to avoid breaking status update
    }
  }

  /**
   * Get consolidated reporting data from admin server
   */
  async getConsolidatedReports(params: {
    startDate: string;
    endDate: string;
    reportType: 'financial' | 'operational' | 'combined';
  }): Promise<any> {
    try {
      const queryParams = new URLSearchParams(params);
      return await this.makeRequest(`/api/reports/consolidated?${queryParams}`);
    } catch (error) {
      console.error('Failed to get consolidated reports:', error);
      return null;
    }
  }
}

// Export singleton instance
export const adminService = new AdminServiceClient();

/**
 * Helper function to safely call admin service methods
 */
export async function safeAdminServiceCall<T>(
  operation: () => Promise<T>,
  fallbackValue: T
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error('Admin service call failed, using fallback:', error);
    return fallbackValue;
  }
}

/**
 * Check if admin server integration is enabled
 */
export function isAdminIntegrationEnabled(): boolean {
  return !!(ADMIN_SERVER_URL && ADMIN_SERVER_API_KEY);
}

/**
 * Get admin server status
 */
export async function getAdminServerStatus(): Promise<{
  isConnected: boolean;
  url: string;
  lastChecked: string;
  error?: string;
}> {
  const lastChecked = new Date().toISOString();
  
  try {
    const isConnected = await adminService.testConnection();
    return {
      isConnected,
      url: ADMIN_SERVER_URL,
      lastChecked,
    };
  } catch (error) {
    return {
      isConnected: false,
      url: ADMIN_SERVER_URL,
      lastChecked,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Token validation endpoint for staff server
 * Validates JWT tokens and returns user information
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { verifyToken } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return createErrorResponse('No token provided', 401);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Verify the token
    const payload = verifyToken(token);
    if (!payload) {
      return createErrorResponse('Invalid or expired token', 401);
    }

    // Get user information from database
    const userResult = await query(
      `SELECT id, email, role, name, is_active, server_type, created_at, updated_at
       FROM users 
       WHERE id = $1 AND is_active = true`,
      [payload.userId]
    );

    if (userResult.rows.length === 0) {
      return createErrorResponse('User not found or inactive', 401);
    }

    const user = userResult.rows[0];

    // For staff server, ensure user has staff server access
    if (user.server_type !== 'staff') {
      return createErrorResponse('Access denied - staff access required', 403);
    }

    // Return user information
    return createResponse({
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        serverType: user.server_type,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      },
      tokenValid: true
    }, true, 'Token validated successfully');

  } catch (error) {
    console.error('Token validation error:', error);
    return createErrorResponse('Token validation failed', 500);
  }
}

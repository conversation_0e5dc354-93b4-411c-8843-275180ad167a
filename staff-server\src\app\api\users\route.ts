/**
 * Staff user management endpoints
 * Handles CRUD operations for staff users (management, reception, teacher)
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, hashPassword } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidEmail
} from '@/lib/utils';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// GET /api/users - List staff users with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only management can view all users
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      if (filters.search) {
        // Search in name and email
        const searchResult = await query<{total: string}>(
          `SELECT COUNT(*) as total FROM users
           WHERE (name ILIKE $1 OR email ILIKE $1)`,
          [`%${filters.search}%`]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const usersResult = await query<User>(
          `SELECT id, email, role, name, is_active, created_at, updated_at 
           FROM users 
           WHERE (name ILIKE $1 OR email ILIKE $1)
           ORDER BY created_at DESC 
           LIMIT $2 OFFSET $3`,
          [`%${filters.search}%`, pagination.limit, offset]
        );

        return createResponse({
          users: usersResult.rows,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Users retrieved successfully');
      }

      // Add role filter
      if (filters.role && ['management', 'reception', 'teacher'].includes(filters.role)) {
        conditions.role = filters.role;
      }

      // Add active status filter
      if (filters.isActive !== undefined) {
        conditions.is_active = filters.isActive === 'true';
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const offset = (pagination.page - 1) * pagination.limit;
      const dataSql = `
        SELECT id, email, role, name, is_active, created_at, updated_at 
        FROM users ${whereClause} 
        ORDER BY created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const usersResult = await query<User>(dataSql, [...params, pagination.limit, offset]);

      return createResponse({
        users: usersResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Users retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving staff users:', dbError);
      return createErrorResponse('Failed to retrieve users', 500);
    }

  } catch (error) {
    console.error('Get staff users error:', error);
    return createErrorResponse('Failed to retrieve users', 500);
  }
}

// POST /api/users - Create new staff user (redirects to admin server)
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only management can create users
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    // Staff users should be created through the admin server
    // This endpoint provides information about the correct process
    return createErrorResponse(
      'Staff users must be created through the admin server. Please contact an administrator to create new staff accounts.',
      400,
      {
        redirectTo: 'admin-server',
        endpoint: '/api/staff-integration/users',
        message: 'Staff user management is handled by the admin server for centralized control and proper authentication setup.'
      }
    );

  } catch (error) {
    console.error('Create staff user error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

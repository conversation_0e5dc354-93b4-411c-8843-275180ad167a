/**
 * Financial Reports API endpoint
 * Provides comprehensive financial reporting combining payments and invoices
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logReportOperation, getRequestContext } from '@/lib/activity-logger';

interface FinancialReport {
  summary: {
    totalRevenue: number;
    totalInvoiced: number;
    totalPaid: number;
    totalPending: number;
    totalOverdue: number;
    collectionRate: number; // percentage of invoices paid
  };
  monthlyTrends: Array<{
    month: string;
    revenue: number;
    invoiced: number;
    paid: number;
    paymentCount: number;
    invoiceCount: number;
  }>;
  paymentBreakdown: {
    byType: Record<string, number>;
    byMethod: Record<string, number>;
  };
  agingReport: Array<{
    ageRange: string;
    invoiceCount: number;
    totalAmount: number;
  }>;
  topStudents: Array<{
    studentId: string;
    totalPaid: number;
    totalInvoiced: number;
    paymentCount: number;
    invoiceCount: number;
  }>;
}

// GET /api/reports/financial - Get comprehensive financial report
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions (only admin and accountant can view financial reports)
    if (!hasPermission(authResult.user.role, 'reports', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const format = searchParams.get('format'); // 'json' or 'csv'

    // Build date filter
    const dateConditions: string[] = [];
    const dateParams: any[] = [];
    let paramIndex = 1;

    if (dateFrom) {
      dateConditions.push(`$${paramIndex}`);
      dateParams.push(dateFrom);
      paramIndex++;
    }

    if (dateTo) {
      dateConditions.push(`$${paramIndex}`);
      dateParams.push(dateTo + ' 23:59:59');
      paramIndex++;
    }

    // Get summary statistics
    const summarySql = `
      WITH payment_summary AS (
        SELECT 
          COALESCE(SUM(amount), 0) as total_revenue,
          COUNT(*) as payment_count
        FROM payments
        WHERE status = 'completed'
          ${dateFrom ? 'AND start_date >= $1' : ''}
          ${dateTo ? `AND start_date <= $${dateParams.length}` : ''}
      ),
      invoice_summary AS (
        SELECT 
          COALESCE(SUM(amount), 0) as total_invoiced,
          COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0) as total_paid,
          COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0) as total_pending,
          COALESCE(SUM(CASE WHEN status = 'pending' AND due_date < CURRENT_DATE THEN amount ELSE 0 END), 0) as total_overdue,
          COUNT(*) as invoice_count,
          COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count
        FROM invoices 
        WHERE 1=1
          ${dateFrom ? 'AND created_at >= $1' : ''}
          ${dateTo ? `AND created_at <= $${dateParams.length}` : ''}
      )
      SELECT 
        p.total_revenue,
        i.total_invoiced,
        i.total_paid,
        i.total_pending,
        i.total_overdue,
        CASE 
          WHEN i.invoice_count > 0 THEN (i.paid_count::float / i.invoice_count * 100)
          ELSE 0 
        END as collection_rate
      FROM payment_summary p, invoice_summary i
    `;

    const summaryResult = await query(summarySql, dateParams);
    const summary = summaryResult.rows[0];

    // Get monthly trends
    const trendsSql = `
      WITH monthly_payments AS (
        SELECT
          TO_CHAR(DATE_TRUNC('month', start_date), 'YYYY-MM') as month,
          SUM(amount) as revenue,
          COUNT(*) as payment_count
        FROM payments
        WHERE status = 'completed'
          ${dateFrom ? 'AND start_date >= $1' : 'AND start_date >= CURRENT_DATE - INTERVAL \'12 months\''}
          ${dateTo ? `AND start_date <= $${dateParams.length}` : ''}
        GROUP BY DATE_TRUNC('month', start_date)
      ),
      monthly_invoices AS (
        SELECT 
          TO_CHAR(DATE_TRUNC('month', created_at), 'YYYY-MM') as month,
          SUM(amount) as invoiced,
          SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as paid,
          COUNT(*) as invoice_count
        FROM invoices 
        WHERE 1=1
          ${dateFrom ? 'AND created_at >= $1' : 'AND created_at >= CURRENT_DATE - INTERVAL \'12 months\''}
          ${dateTo ? `AND created_at <= $${dateParams.length}` : ''}
        GROUP BY DATE_TRUNC('month', created_at)
      )
      SELECT 
        COALESCE(p.month, i.month) as month,
        COALESCE(p.revenue, 0) as revenue,
        COALESCE(i.invoiced, 0) as invoiced,
        COALESCE(i.paid, 0) as paid,
        COALESCE(p.payment_count, 0) as payment_count,
        COALESCE(i.invoice_count, 0) as invoice_count
      FROM monthly_payments p
      FULL OUTER JOIN monthly_invoices i ON p.month = i.month
      ORDER BY month DESC
      LIMIT 12
    `;

    const trendsResult = await query(trendsSql, dateParams);
    const monthlyTrends = trendsResult.rows.map(row => ({
      month: row.month,
      revenue: parseFloat(row.revenue || 0),
      invoiced: parseFloat(row.invoiced || 0),
      paid: parseFloat(row.paid || 0),
      paymentCount: parseInt(row.payment_count || 0),
      invoiceCount: parseInt(row.invoice_count || 0)
    }));

    // Get payment breakdown
    const paymentBreakdownSql = `
      SELECT 
        payment_type,
        payment_method,
        SUM(amount) as amount
      FROM payments
      WHERE status = 'completed'
        ${dateFrom ? 'AND start_date >= $1' : ''}
        ${dateTo ? `AND start_date <= $${dateParams.length}` : ''}
      GROUP BY payment_type, payment_method
    `;

    const breakdownResult = await query(paymentBreakdownSql, dateParams);
    const byType: Record<string, number> = {};
    const byMethod: Record<string, number> = {};

    breakdownResult.rows.forEach(row => {
      const amount = parseFloat(row.amount);
      byType[row.payment_type] = (byType[row.payment_type] || 0) + amount;
      byMethod[row.payment_method] = (byMethod[row.payment_method] || 0) + amount;
    });

    // Get aging report
    const agingSql = `
      SELECT 
        CASE 
          WHEN due_date >= CURRENT_DATE THEN 'Current'
          WHEN due_date >= CURRENT_DATE - INTERVAL '30 days' THEN '1-30 days'
          WHEN due_date >= CURRENT_DATE - INTERVAL '60 days' THEN '31-60 days'
          WHEN due_date >= CURRENT_DATE - INTERVAL '90 days' THEN '61-90 days'
          ELSE '90+ days'
        END as age_range,
        COUNT(*) as invoice_count,
        SUM(amount) as total_amount
      FROM invoices 
      WHERE status = 'pending'
      GROUP BY 
        CASE 
          WHEN due_date >= CURRENT_DATE THEN 'Current'
          WHEN due_date >= CURRENT_DATE - INTERVAL '30 days' THEN '1-30 days'
          WHEN due_date >= CURRENT_DATE - INTERVAL '60 days' THEN '31-60 days'
          WHEN due_date >= CURRENT_DATE - INTERVAL '90 days' THEN '61-90 days'
          ELSE '90+ days'
        END
      ORDER BY 
        CASE 
          WHEN age_range = 'Current' THEN 1
          WHEN age_range = '1-30 days' THEN 2
          WHEN age_range = '31-60 days' THEN 3
          WHEN age_range = '61-90 days' THEN 4
          ELSE 5
        END
    `;

    const agingResult = await query(agingSql);
    const agingReport = agingResult.rows.map(row => ({
      ageRange: row.age_range,
      invoiceCount: parseInt(row.invoice_count),
      totalAmount: parseFloat(row.total_amount)
    }));

    // Get top students by financial activity
    const topStudentsSql = `
      WITH student_payments AS (
        SELECT 
          student_id,
          SUM(amount) as total_paid,
          COUNT(*) as payment_count
        FROM payments
        WHERE status = 'completed'
          ${dateFrom ? 'AND start_date >= $1' : ''}
          ${dateTo ? `AND start_date <= $${dateParams.length}` : ''}
        GROUP BY student_id
      ),
      student_invoices AS (
        SELECT 
          student_id,
          SUM(amount) as total_invoiced,
          COUNT(*) as invoice_count
        FROM invoices 
        WHERE 1=1
          ${dateFrom ? 'AND created_at >= $1' : ''}
          ${dateTo ? `AND created_at <= $${dateParams.length}` : ''}
        GROUP BY student_id
      )
      SELECT 
        COALESCE(p.student_id, i.student_id) as student_id,
        COALESCE(p.total_paid, 0) as total_paid,
        COALESCE(i.total_invoiced, 0) as total_invoiced,
        COALESCE(p.payment_count, 0) as payment_count,
        COALESCE(i.invoice_count, 0) as invoice_count
      FROM student_payments p
      FULL OUTER JOIN student_invoices i ON p.student_id = i.student_id
      ORDER BY COALESCE(p.total_paid, 0) + COALESCE(i.total_invoiced, 0) DESC
      LIMIT 10
    `;

    const topStudentsResult = await query(topStudentsSql, dateParams);
    const topStudents = topStudentsResult.rows.map(row => ({
      studentId: row.student_id,
      totalPaid: parseFloat(row.total_paid || 0),
      totalInvoiced: parseFloat(row.total_invoiced || 0),
      paymentCount: parseInt(row.payment_count || 0),
      invoiceCount: parseInt(row.invoice_count || 0)
    }));

    const report: FinancialReport = {
      summary: {
        totalRevenue: parseFloat(summary.total_revenue),
        totalInvoiced: parseFloat(summary.total_invoiced),
        totalPaid: parseFloat(summary.total_paid),
        totalPending: parseFloat(summary.total_pending),
        totalOverdue: parseFloat(summary.total_overdue),
        collectionRate: parseFloat(summary.collection_rate)
      },
      monthlyTrends,
      paymentBreakdown: { byType, byMethod },
      agingReport,
      topStudents
    };

    // Log report access
    const context = getRequestContext(request.headers);
    await logReportOperation(
      'VIEW',
      authResult.user.id,
      'financial-report',
      context
    );

    return createResponse(report, true, 'Financial report generated successfully');

  } catch (error) {
    console.error('Error generating financial report:', error);
    return createErrorResponse('Failed to generate financial report', 500);
  }
}

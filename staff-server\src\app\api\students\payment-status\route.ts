/**
 * Students Payment Status API
 * Fetches payment status for students from admin server
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { adminService } from '@/lib/admin-service';

interface PaymentStatusRequest {
  studentIds: string[];
}

// POST /api/students/payment-status
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'students', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body: PaymentStatusRequest = await request.json();

    if (!body.studentIds || !Array.isArray(body.studentIds)) {
      return createErrorResponse('studentIds array is required', 400);
    }

    // Get payment status from admin server
    const paymentStatusMap = await adminService.getStudentsPaymentStatus(body.studentIds);

    return createResponse(
      paymentStatusMap,
      true,
      'Payment status retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching payment status:', error);
    return createErrorResponse('Failed to fetch payment status', 500);
  }
}

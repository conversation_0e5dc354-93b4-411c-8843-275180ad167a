# 🏗️ Innovative Centre Platform - System Architecture

## 📋 Overview

The Innovative Centre Platform is a comprehensive Customer Relationship Management (CRM) system designed specifically for educational institutions. It features a dual-server architecture that separates administrative functions from operational tasks while maintaining seamless data synchronization.

## 🎯 Architecture Principles

### 1. Separation of Concerns
- **Admin Server**: Centralized management, reporting, and oversight
- **Staff Server**: Operational tasks, student management, daily activities

### 2. Microservices Approach
- Independent deployments and scaling
- Technology stack flexibility
- Fault isolation and resilience

### 3. Real-time Data Synchronization
- Immediate activity logging across servers
- Consistent user management
- Audit trail maintenance

## 🏛️ System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Innovative Centre Platform               │
├─────────────────────────┬───────────────────────────────────┤
│      Admin Server       │         Staff Server              │
│     (Port 3000)         │        (Port 3003)                │
├─────────────────────────┼───────────────────────────────────┤
│ • User Management       │ • Student Enrollment              │
│ • Activity Monitoring   │ • Daily Operations                │
│ • Reports & Analytics   │ • Academic Management             │
│ • System Administration │ • Reception Tasks                 │
│ • Financial Oversight   │ • Teaching Activities             │
└─────────────────────────┴───────────────────────────────────┘
           │                              │
           └──────────── API ─────────────┘
                    Communication
```

## 🔧 Technology Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Custom component library
- **State Management**: React Context + Hooks

### Backend
- **Runtime**: Node.js
- **Framework**: Next.js API Routes
- **Language**: TypeScript
- **Authentication**: JWT tokens
- **API Design**: RESTful architecture

### Database
- **Primary**: PostgreSQL
- **Admin DB**: Neon PostgreSQL (Cloud)
- **Staff DB**: PostgreSQL (Local/Cloud)
- **ORM**: Custom query builder
- **Migrations**: SQL scripts

### Infrastructure
- **Development**: Local development servers
- **Production**: Containerized deployment ready
- **Monitoring**: Custom logging and health checks
- **Security**: API key authentication, JWT tokens

## 🔐 Security Architecture

### Authentication Flow
```
1. User Login Request → Server
2. Credentials Validation → Database
3. JWT Token Generation → Client
4. Token-based API Access → Protected Routes
5. Token Refresh → Maintain Session
```

### Authorization Levels
- **Admin Roles**: admin, cashier, accountant
- **Staff Roles**: management, reception, teacher
- **Server-specific**: Users assigned to specific servers
- **Cross-server**: Admin can manage staff users

### Security Measures
- Password hashing (bcrypt)
- JWT token expiration
- API key authentication for interserver communication
- Role-based access control (RBAC)
- Input validation and sanitization

## 🔄 Interserver Communication

### Communication Protocol
```
Staff Server                    Admin Server
     │                               │
     ├─ Activity Log ──────────────→ │ Activity Sync Endpoint
     ├─ User Validation ────────────→ │ User Management API
     ├─ Health Check ───────────────→ │ System Status
     └─ Error Reporting ────────────→ │ Debug Logging
```

### API Endpoints

#### Admin Server Endpoints
- `POST /api/staff-integration/activity-sync` - Receive activity logs
- `GET /api/staff-integration/users` - Provide user data
- `GET /api/staff-integration/health` - System health status
- `POST /api/staff-integration/error-report` - Error reporting

#### Staff Server Endpoints
- `POST /api/admin-sync/activity` - Send activity logs
- `GET /api/admin-sync/user-validation` - Validate user access
- `GET /api/health` - Health check endpoint

### Data Synchronization

#### Activity Logging Flow
```
1. User Action (Staff Server)
2. Local Activity Creation
3. Admin Server Sync Request
4. Activity Log Storage (Admin DB)
5. Confirmation Response
6. Local Backup (if sync fails)
```

#### User Management Flow
```
1. User Creation (Admin Server)
2. User Data Storage (Admin DB)
3. Staff Server Authentication
4. Cross-server User Validation
5. Role-based Access Control
```

## 📊 Database Schema

### Admin Server Database

#### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  server_type VARCHAR(20) DEFAULT 'admin',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Activity Logs Table
```sql
CREATE TABLE activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  action VARCHAR(50) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id UUID,
  old_values JSONB,
  new_values JSONB,
  description TEXT,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Staff Server Database

#### Students Table
```sql
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  date_of_birth DATE,
  enrollment_date DATE,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔍 Monitoring and Logging

### Debug Logging System
- **Levels**: ERROR, WARN, INFO, DEBUG, TRACE
- **Categories**: AUTH, DATABASE, INTERSERVER, ACTIVITY, REQUEST
- **Storage**: In-memory with export capabilities
- **Access**: Debug API endpoints for real-time monitoring

### Health Monitoring
- **Server Health**: CPU, memory, response times
- **Database Health**: Connection status, query performance
- **Interserver Health**: Communication latency, error rates
- **User Activity**: Login patterns, operation frequencies

### Error Handling
- **Graceful Degradation**: Continue operation during partial failures
- **Error Propagation**: Proper error reporting without exposing internals
- **Recovery Mechanisms**: Automatic retry for transient failures
- **Alerting**: Log-based monitoring for critical issues

## 🚀 Deployment Architecture

### Development Environment
```
Developer Machine
├── Admin Server (localhost:3000)
├── Staff Server (localhost:3003)
├── Admin Database (Neon Cloud)
└── Staff Database (Local PostgreSQL)
```

### Production Environment (Recommended)
```
Load Balancer
├── Admin Server Cluster
│   ├── Instance 1 (admin-1.domain.com)
│   ├── Instance 2 (admin-2.domain.com)
│   └── Database (Primary + Replica)
└── Staff Server Cluster
    ├── Instance 1 (staff-1.domain.com)
    ├── Instance 2 (staff-2.domain.com)
    └── Database (Primary + Replica)
```

## 📈 Scalability Considerations

### Horizontal Scaling
- **Load Balancing**: Multiple server instances
- **Database Sharding**: Partition data by institution
- **Caching**: Redis for session and frequently accessed data
- **CDN**: Static asset delivery optimization

### Performance Optimization
- **Database Indexing**: Optimized queries for common operations
- **Connection Pooling**: Efficient database connection management
- **Lazy Loading**: On-demand data fetching
- **Compression**: Response compression for large datasets

## 🔧 Configuration Management

### Environment Variables
```bash
# Admin Server
DATABASE_URL=postgresql://...
STAFF_SERVER_API_KEY=...
JWT_SECRET=...
NODE_ENV=production

# Staff Server
DATABASE_URL=postgresql://...
ADMIN_SERVER_URL=http://admin-server:3000
ADMIN_SERVER_API_KEY=...
ENABLE_ADMIN_INTEGRATION=true
```

### Feature Flags
- `ENABLE_ADMIN_INTEGRATION`: Toggle interserver communication
- `DEBUG_LOGGING`: Enable detailed logging
- `MAINTENANCE_MODE`: Graceful shutdown for updates

## 🛡️ Disaster Recovery

### Backup Strategy
- **Database Backups**: Daily automated backups
- **Configuration Backups**: Environment and deployment configs
- **Code Backups**: Version control with Git

### Recovery Procedures
- **Database Recovery**: Point-in-time recovery from backups
- **Server Recovery**: Container-based rapid deployment
- **Data Synchronization**: Automatic sync after recovery

This architecture provides a robust, scalable, and maintainable foundation for educational institution management while ensuring data integrity and operational efficiency.

## 📚 API Documentation

### Authentication Endpoints

#### POST /api/auth/login
**Purpose**: User authentication
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "admin"
    }
  }
}
```

#### POST /api/auth/logout
**Purpose**: User logout and token invalidation
**Headers**: `Authorization: Bearer <token>`
**Response**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Student Management Endpoints (Staff Server)

#### GET /api/students
**Purpose**: Retrieve student list with pagination
**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `search`: Search term for name/email
- `status`: Filter by status (active/inactive)

**Response**:
```json
{
  "success": true,
  "data": {
    "students": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

#### POST /api/students
**Purpose**: Create new student
**Headers**: `Authorization: Bearer <token>`
**Request Body**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+998901234567",
  "dateOfBirth": "2000-01-01",
  "enrollmentDate": "2024-01-15",
  "status": "active"
}
```

### Activity Logging Endpoints (Admin Server)

#### GET /api/activity-logs
**Purpose**: Retrieve activity logs with filtering
**Query Parameters**:
- `page`: Page number
- `limit`: Items per page
- `userId`: Filter by user ID
- `action`: Filter by action type
- `resourceType`: Filter by resource type
- `startDate`: Start date filter
- `endDate`: End date filter

#### POST /api/staff-integration/activity-sync
**Purpose**: Receive activity logs from staff server
**Headers**:
- `X-API-Key`: Staff server API key
- `X-Source-Service`: staff-server
**Request Body**:
```json
{
  "userId": "uuid",
  "action": "CREATE",
  "resourceType": "STUDENT",
  "resourceId": "uuid",
  "description": "Created new student",
  "timestamp": "2024-01-15T10:30:00Z",
  "sourceService": "staff-server"
}
```

## 🔧 Development Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- npm or yarn package manager

### Installation Steps

1. **Clone Repository**
```bash
git clone <repository-url>
cd innovative-platform
```

2. **Install Dependencies**
```bash
# Admin Server
cd admin-server
npm install

# Staff Server
cd ../staff-server
npm install
```

3. **Database Setup**
```bash
# Create databases
createdb innovative_admin
createdb innovative_staff

# Run migrations
cd admin-server
npm run migrate

cd ../staff-server
npm run migrate
```

4. **Environment Configuration**
```bash
# Admin Server
cp admin-server/.env.example admin-server/.env.local
# Edit with your database URLs and secrets

# Staff Server
cp staff-server/.env.example staff-server/.env.local
# Edit with your database URLs and admin server URL
```

5. **Start Development Servers**
```bash
# Terminal 1 - Admin Server
cd admin-server
npm run dev

# Terminal 2 - Staff Server
cd staff-server
npm run dev
```

### Testing
```bash
# Run comprehensive tests
cd testing
node comprehensive-test-scenarios.js

# Prepare demo data
cd presentation
node demo-preparation.js
```

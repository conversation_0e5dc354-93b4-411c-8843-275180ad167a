# 🚀 Vercel Deployment Guide - Innovative Centre Platform

This guide provides step-by-step instructions for deploying the Innovative Centre Platform to Vercel.

## 📋 Overview

The Innovative Centre Platform consists of two Next.js applications that need to be deployed separately on Vercel:

- **Admin Server**: Financial management, cabinet booking, user administration
- **Staff Server**: Student enrollment, lead management, group management

Both servers communicate with each other via secure API calls and share database resources.

## 🏗️ Architecture on Vercel

```
┌─────────────────┐    API Calls    ┌─────────────────┐
│   Admin Server  │ ←──────────────→ │   Staff Server  │
│ (Vercel Project)│                 │ (Vercel Project)│
└─────────────────┘                 └─────────────────┘
         │                                   │
         │                                   │
         ▼                                   ▼
┌─────────────────┐                 ┌─────────────────┐
│  Admin Database │                 │  Staff Database │
│     (Neon)      │                 │     (Neon)      │
└─────────────────┘                 └─────────────────┘
```

## 📋 Prerequisites

### 1. Vercel Account
- Sign up at [vercel.com](https://vercel.com)
- Install Vercel CLI: `npm i -g vercel`

### 2. Database Setup
- **Admin Database**: Already configured with Neon
- **Staff Database**: Already configured with Neon
- Both databases should be accessible from Vercel's infrastructure

### 3. Domain Configuration (Optional)
- Custom domains for production deployment
- SSL certificates (handled automatically by Vercel)

## 🚀 Deployment Steps

### Step 1: Prepare the Repository

1. **Clone the repository** (if not already done):
   ```bash
   git clone https://github.com/MrFarrukhT/Innovative-Platform.git
   cd Innovative-Platform
   ```

2. **Install dependencies**:
   ```bash
   npm install
   cd admin-server && npm install && cd ..
   cd staff-server && npm install && cd ..
   ```

### Step 2: Deploy Admin Server

1. **Navigate to admin server directory**:
   ```bash
   cd admin-server
   ```

2. **Deploy to Vercel**:
   ```bash
   vercel
   ```

3. **Follow the prompts**:
   - Set up and deploy? `Y`
   - Which scope? Choose your account/team
   - Link to existing project? `N`
   - Project name: `innovative-centre-admin`
   - Directory: `./` (current directory)
   - Override settings? `N`

4. **Configure environment variables** in Vercel dashboard:
   - Go to your project settings
   - Navigate to "Environment Variables"
   - Add the following variables:

   ```env
   DATABASE_URL=postgresql://admin_owner:<EMAIL>/admin?sslmode=require
   JWT_SECRET=your-secure-jwt-secret-here
   NEXTAUTH_SECRET=your-secure-nextauth-secret-here
   NEXT_PUBLIC_APP_URL=https://your-admin-domain.vercel.app
   NODE_ENV=production
   ENABLE_ACTIVITY_LOGGING=true
   LOG_RETENTION_DAYS=365
   BCRYPT_ROUNDS=12
   JWT_EXPIRES_IN=24h
   REFRESH_TOKEN_EXPIRES_IN=7d
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   MAX_FILE_SIZE=********
   ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
   STAFF_SERVER_API_KEY=your-secure-api-key-here
   AUTO_CREATE_INVOICE=true
   DEFAULT_MONTHLY_FEE=100
   ```

   **Note**: `STAFF_SERVER_URL` will be set after deploying the staff server.

### Step 3: Deploy Staff Server

1. **Navigate to staff server directory**:
   ```bash
   cd ../staff-server
   ```

2. **Deploy to Vercel**:
   ```bash
   vercel
   ```

3. **Follow the prompts**:
   - Set up and deploy? `Y`
   - Which scope? Choose your account/team
   - Link to existing project? `N`
   - Project name: `innovative-centre-staff`
   - Directory: `./` (current directory)
   - Override settings? `N`

4. **Configure environment variables** in Vercel dashboard:
   ```env
   DATABASE_URL=postgresql://staff_owner:<EMAIL>/staff?sslmode=require
   JWT_SECRET=your-secure-jwt-secret-here
   NEXTAUTH_SECRET=your-secure-nextauth-secret-here
   NEXT_PUBLIC_APP_URL=https://your-staff-domain.vercel.app
   NODE_ENV=production
   ENABLE_ACTIVITY_LOGGING=true
   LOG_RETENTION_DAYS=365
   BCRYPT_ROUNDS=12
   JWT_EXPIRES_IN=24h
   REFRESH_TOKEN_EXPIRES_IN=7d
   RATE_LIMIT_WINDOW_MS=900000
   RATE_LIMIT_MAX_REQUESTS=100
   MAX_FILE_SIZE=********
   ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf
   ADMIN_SERVER_API_KEY=your-secure-api-key-here
   ENABLE_ADMIN_INTEGRATION=true
   SYNC_ACTIVITY_LOGS=true
   SYNC_STUDENT_STATUS=true
   ```

   **Note**: `ADMIN_SERVER_URL` will be set after getting the admin server URL.

### Step 4: Configure Inter-Service Communication

1. **Get deployment URLs**:
   - Admin Server: `https://innovative-centre-admin.vercel.app`
   - Staff Server: `https://innovative-centre-staff.vercel.app`

2. **Update Admin Server environment variables**:
   - Add: `STAFF_SERVER_URL=https://innovative-centre-staff.vercel.app`

3. **Update Staff Server environment variables**:
   - Add: `ADMIN_SERVER_URL=https://innovative-centre-admin.vercel.app`

4. **Redeploy both applications** to apply the new environment variables:
   ```bash
   # In admin-server directory
   vercel --prod
   
   # In staff-server directory
   vercel --prod
   ```

## 🔧 Environment Variables Reference

### Required for Both Servers
- `DATABASE_URL`: PostgreSQL connection string
- `JWT_SECRET`: Secret for JWT token signing (must match between servers)
- `NEXTAUTH_SECRET`: NextAuth.js secret
- `NEXT_PUBLIC_APP_URL`: Public URL of the application
- `NODE_ENV`: Set to `production`

### Admin Server Specific
- `STAFF_SERVER_URL`: URL of the staff server
- `STAFF_SERVER_API_KEY`: API key for staff server communication

### Staff Server Specific
- `ADMIN_SERVER_URL`: URL of the admin server
- `ADMIN_SERVER_API_KEY`: API key for admin server communication (must match admin server)
- `ENABLE_ADMIN_INTEGRATION`: Set to `true`

## 🔐 Security Configuration

### 1. Generate Secure API Keys
```bash
# Generate a secure API key
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### 2. JWT Secrets
- Use different JWT secrets for development and production
- Ensure both servers use the same JWT secret for token verification

### 3. Database Security
- Use SSL connections (already configured in connection strings)
- Ensure database credentials are secure
- Consider using Vercel's environment variable encryption

## 📊 Monitoring and Health Checks

### Health Check Endpoints
- Admin Server: `https://your-admin-domain.vercel.app/api/health`
- Staff Server: `https://your-staff-domain.vercel.app/api/health`
- Integration Status: `https://your-staff-domain.vercel.app/api/admin-integration/status`

### Vercel Analytics
- Enable Vercel Analytics in project settings
- Monitor function execution times
- Track error rates and performance metrics

## 🔄 Continuous Deployment

### Automatic Deployments
Vercel automatically deploys when you push to your Git repository:

1. **Connect Git Repository**:
   - Go to Vercel dashboard
   - Connect your GitHub repository
   - Configure branch settings

2. **Branch Configuration**:
   - Production: `main` or `master` branch
   - Preview: Feature branches

### Manual Deployments
```bash
# Deploy to production
vercel --prod

# Deploy preview
vercel
```

## 🛠️ Troubleshooting

### Common Issues

#### 1. Environment Variables Not Loading
- Ensure variables are set in Vercel dashboard
- Redeploy after adding new variables
- Check variable names for typos

#### 2. Inter-Service Communication Fails
- Verify API keys match between servers
- Check CORS configuration
- Ensure URLs are correct and accessible

#### 3. Database Connection Issues
- Verify connection strings are correct
- Check if database allows connections from Vercel IPs
- Ensure SSL is properly configured

#### 4. Build Failures
- Check build logs in Vercel dashboard
- Verify all dependencies are installed
- Ensure TypeScript compilation succeeds

### Debug Commands
```bash
# Check deployment logs
vercel logs

# Test local build
npm run build

# Verify environment variables
vercel env ls
```

## 📈 Performance Optimization

### 1. Function Configuration
- Functions are automatically optimized for Vercel
- 30-second timeout configured in `vercel.json`
- Serverless functions scale automatically

### 2. Database Optimization
- Connection pooling optimized for serverless
- Shorter connection timeouts for production
- Automatic connection cleanup

### 3. Caching
- Static assets cached by Vercel CDN
- API responses can be cached using Vercel's caching headers

## 🔄 Updates and Maintenance

### Updating the Application
1. Make changes to your code
2. Commit and push to Git repository
3. Vercel automatically deploys the changes
4. Monitor deployment status in dashboard

### Database Migrations
- Run migrations manually when needed
- Consider using Vercel's build hooks for automated migrations
- Always backup database before major changes

## 📞 Support and Resources

### Vercel Resources
- [Vercel Documentation](https://vercel.com/docs)
- [Next.js on Vercel](https://vercel.com/docs/frameworks/nextjs)
- [Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)

### Project Resources
- [Project Repository](https://github.com/MrFarrukhT/Innovative-Platform)
- [Admin Server Health Check](https://your-admin-domain.vercel.app/api/health)
- [Staff Server Health Check](https://your-staff-domain.vercel.app/api/health)

## ✅ Post-Deployment Checklist

- [ ] Both servers deployed successfully
- [ ] Environment variables configured
- [ ] Inter-service communication working
- [ ] Database connections established
- [ ] Health checks passing
- [ ] User authentication working
- [ ] API endpoints responding
- [ ] CORS configured properly
- [ ] SSL certificates active
- [ ] Monitoring enabled

## 🧪 Deployment Verification Script

A verification script is available to test your deployment:

```bash
# Run the verification script
node scripts/verify-vercel-deployment.js
```

This script will:
- Test both server health endpoints
- Verify inter-service communication
- Check database connections
- Validate environment configuration
- Test authentication flows

Your Innovative Centre Platform is now ready for production use on Vercel! 🎉

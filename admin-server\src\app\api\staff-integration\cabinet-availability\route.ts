/**
 * Cabinet availability endpoint for staff server integration
 * Provides cabinet availability information to the staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function GET(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);

    const date = filters.date || new Date().toISOString().split('T')[0];
    const timeSlot = filters.timeSlot;

    try {
      // Get all cabinets with their current bookings for the specified date
      let sql = `
        SELECT 
          c.id,
          c.name,
          c.capacity,
          c.is_available,
          COALESCE(
            json_agg(
              CASE 
                WHEN b.id IS NOT NULL THEN
                  json_build_object(
                    'id', b.id,
                    'startTime', b.start_time,
                    'endTime', b.end_time,
                    'purpose', b.purpose,
                    'bookedBy', u.name,
                    'status', b.status
                  )
                ELSE NULL
              END
            ) FILTER (WHERE b.id IS NOT NULL),
            '[]'::json
          ) as bookings
        FROM cabinets c
        LEFT JOIN cabinet_bookings b ON c.id = b.cabinet_id 
          AND b.date = $1 
          AND b.status IN ('confirmed', 'pending')
        LEFT JOIN users u ON b.booked_by = u.id
        WHERE c.is_available = true
        GROUP BY c.id, c.name, c.capacity, c.is_available
        ORDER BY c.name
      `;

      const params = [date];

      // If timeSlot is specified, filter for cabinets available during that time
      if (timeSlot) {
        const [startTime, endTime] = timeSlot.split('-');
        if (startTime && endTime) {
          sql = `
            SELECT 
              c.id,
              c.name,
              c.capacity,
              c.is_available,
              CASE 
                WHEN COUNT(b.id) = 0 THEN true
                ELSE false
              END as is_available_at_time,
              COALESCE(
                json_agg(
                  CASE 
                    WHEN b.id IS NOT NULL THEN
                      json_build_object(
                        'id', b.id,
                        'startTime', b.start_time,
                        'endTime', b.end_time,
                        'purpose', b.purpose,
                        'bookedBy', u.name,
                        'status', b.status
                      )
                    ELSE NULL
                  END
                ) FILTER (WHERE b.id IS NOT NULL),
                '[]'::json
              ) as bookings
            FROM cabinets c
            LEFT JOIN cabinet_bookings b ON c.id = b.cabinet_id 
              AND b.date = $1 
              AND b.status IN ('confirmed', 'pending')
              AND (
                (b.start_time <= $2 AND b.end_time > $2) OR
                (b.start_time < $3 AND b.end_time >= $3) OR
                (b.start_time >= $2 AND b.end_time <= $3)
              )
            LEFT JOIN users u ON b.booked_by = u.id
            WHERE c.is_available = true
            GROUP BY c.id, c.name, c.capacity, c.is_available
            ORDER BY c.name
          `;
          params.push(startTime, endTime);
        }
      }

      const result = await query(sql, params);

      const cabinets = result.rows.map(cabinet => ({
        id: cabinet.id,
        name: cabinet.name,
        capacity: cabinet.capacity,
        isAvailable: timeSlot ? cabinet.is_available_at_time : cabinet.is_available,
        currentBookings: cabinet.bookings || []
      }));

      return createResponse({
        date,
        timeSlot: timeSlot || null,
        cabinets
      }, true, 'Cabinet availability retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving cabinet availability:', dbError);
      return createErrorResponse('Failed to retrieve cabinet availability', 500);
    }

  } catch (error) {
    console.error('Cabinet availability error:', error);
    return createErrorResponse('Failed to retrieve cabinet availability', 500);
  }
}

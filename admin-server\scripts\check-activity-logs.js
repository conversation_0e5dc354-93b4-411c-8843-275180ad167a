/**
 * Check admin server activity logs table and recent activities
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function checkActivityLogs() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('🔍 Checking Admin Server Activity Logs...');
    
    await client.connect();
    console.log('📡 Database connected successfully');

    // Check activity_logs table structure
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'activity_logs'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Activity logs table structure:');
    tableStructure.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check foreign key constraints
    const constraints = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'activity_logs'
    `);

    console.log('\n🔗 Foreign key constraints:');
    constraints.rows.forEach(constraint => {
      console.log(`  ${constraint.column_name} -> ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
    });

    // Check users in admin server
    const usersCount = await client.query('SELECT COUNT(*) FROM users');
    console.log('\n👥 Admin server users count:', usersCount.rows[0].count);

    // Check recent activity logs
    const recentLogs = await client.query(`
      SELECT 
        id, user_id, action, resource_type, description, timestamp
      FROM activity_logs 
      ORDER BY timestamp DESC 
      LIMIT 10
    `);

    console.log('\n📊 Recent activity logs:');
    recentLogs.rows.forEach((log, index) => {
      console.log(`  ${index + 1}. [${log.timestamp}] ${log.action} ${log.resource_type} - ${log.description}`);
    });

    // Test inserting a sample activity log with a valid user ID
    const sampleUser = await client.query('SELECT id FROM users LIMIT 1');
    if (sampleUser.rows.length > 0) {
      const userId = sampleUser.rows[0].id;
      console.log('\n🧪 Testing activity log insertion with user ID:', userId);
      
      try {
        const testResult = await client.query(`
          INSERT INTO activity_logs (
            user_id, action, resource_type, description, timestamp
          ) VALUES ($1, $2, $3, $4, $5)
          RETURNING id
        `, [userId, 'TEST', 'SYSTEM', '[Staff Server] Test activity from staff server', new Date().toISOString()]);
        
        console.log('✅ Test activity log created:', testResult.rows[0].id);
        
        // Clean up
        await client.query('DELETE FROM activity_logs WHERE id = $1', [testResult.rows[0].id]);
        console.log('🧹 Test log cleaned up');
        
      } catch (testError) {
        console.error('❌ Failed to create test activity log:', testError.message);
      }
    }

    console.log('\n🎉 Activity logs check completed!');

  } catch (error) {
    console.error('❌ Activity logs check failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

// Run the check
checkActivityLogs();

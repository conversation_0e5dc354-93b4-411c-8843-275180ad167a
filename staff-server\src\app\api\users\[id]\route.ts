/**
 * Individual staff user management endpoints
 * Handles GET, PUT, DELETE operations for specific staff users
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, hashPassword } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidEmail,
  isValidUUID
} from '@/lib/utils';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface User {
  id: string;
  email: string;
  password_hash?: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

// GET /api/users/[id] - Get specific staff user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    // Users can view their own profile, management can view all
    if (authResult.user.role !== UserRole.MANAGEMENT && authResult.user.id !== id) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get user details
      const userResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      return createResponse({
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name,
        isActive: user.is_active,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }, true, 'User retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving staff user:', dbError);
      return createErrorResponse('Failed to retrieve user', 500);
    }

  } catch (error) {
    console.error('Get staff user error:', error);
    return createErrorResponse('Failed to retrieve user', 500);
  }
}

// PUT /api/users/[id] - Update specific staff user
// Note: This allows limited profile updates. For full user management (role changes, etc.),
// use the admin server's staff-integration endpoints
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    // Users can update their own profile (limited fields), management can update all
    const isOwnProfile = authResult.user.id === id;
    const isManagement = authResult.user.role === UserRole.MANAGEMENT;

    if (!isOwnProfile && !isManagement) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { email, password, role, name, isActive } = body;

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate role if provided
    if (role && !['management', 'reception', 'teacher'].includes(role)) {
      return createErrorResponse('Invalid role. Must be management, reception, or teacher', 400);
    }

    // Only management can change role and active status
    if ((role !== undefined || isActive !== undefined) && !isManagement) {
      return createErrorResponse('Insufficient permissions to change role or active status', 403);
    }

    // Validate password strength if provided
    if (password && password.length < 8) {
      return createErrorResponse('Password must be at least 8 characters long', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current user data
      const currentUserResult = await query<User>(
        'SELECT id, email, role, name, is_active, created_at, updated_at FROM users WHERE id = $1',
        [id]
      );

      if (currentUserResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const currentUser = currentUserResult.rows[0];

      // Check if email is being changed and if it already exists
      if (email && email.toLowerCase() !== currentUser.email) {
        const existingUserResult = await query(
          'SELECT id FROM users WHERE email = $1 AND id != $2',
          [email.toLowerCase(), id]
        );

        if (existingUserResult.rows.length > 0) {
          return createErrorResponse('User with this email already exists', 409);
        }
      }

      // Build update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (email !== undefined) {
        updateFields.push(`email = $${paramIndex++}`);
        updateValues.push(email.toLowerCase());
      }

      if (password !== undefined) {
        const passwordHash = await hashPassword(password);
        updateFields.push(`password_hash = $${paramIndex++}`);
        updateValues.push(passwordHash);
      }

      if (role !== undefined) {
        updateFields.push(`role = $${paramIndex++}`);
        updateValues.push(role);
      }

      if (name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(name);
      }

      if (isActive !== undefined) {
        updateFields.push(`is_active = $${paramIndex++}`);
        updateValues.push(isActive);
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const updateSql = `
        UPDATE users 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, email, role, name, is_active, created_at, updated_at
      `;

      const updatedUserResult = await query<User>(updateSql, updateValues);
      const updatedUser = updatedUserResult.rows[0];

      // Log user update
      await logUserOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedUser,
        currentUser,
        context
      );

      return createResponse({
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
        name: updatedUser.name,
        isActive: updatedUser.is_active,
        createdAt: updatedUser.created_at,
        updatedAt: updatedUser.updated_at
      }, true, 'User updated successfully');

    } catch (dbError) {
      console.error('Database error updating staff user:', dbError);
      return createErrorResponse('Failed to update user', 500);
    }

  } catch (error) {
    console.error('Update staff user error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

// DELETE /api/users/[id] - Deactivate specific staff user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    // Only management can deactivate users
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    // Prevent self-deactivation
    if (authResult.user.id === id) {
      return createErrorResponse('Cannot deactivate your own account', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current user data
      const currentUserResult = await query<User>(
        'SELECT id, email, role, name, is_active FROM users WHERE id = $1',
        [id]
      );

      if (currentUserResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const currentUser = currentUserResult.rows[0];

      if (!currentUser.is_active) {
        return createErrorResponse('User is already deactivated', 400);
      }

      // Deactivate user (soft delete)
      const deactivatedUserResult = await query<User>(
        `UPDATE users 
         SET is_active = false, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING id, email, role, name, is_active, created_at, updated_at`,
        [id]
      );

      const deactivatedUser = deactivatedUserResult.rows[0];

      // Log user deactivation
      await logUserOperation(
        'DELETE' as any,
        authResult.user.id,
        deactivatedUser,
        currentUser,
        context
      );

      return createResponse({
        id: deactivatedUser.id,
        email: deactivatedUser.email,
        role: deactivatedUser.role,
        name: deactivatedUser.name,
        isActive: deactivatedUser.is_active,
        createdAt: deactivatedUser.created_at,
        updatedAt: deactivatedUser.updated_at
      }, true, 'User deactivated successfully');

    } catch (dbError) {
      console.error('Database error deactivating staff user:', dbError);
      return createErrorResponse('Failed to deactivate user', 500);
    }

  } catch (error) {
    console.error('Deactivate staff user error:', error);
    return createErrorResponse('Failed to deactivate user', 500);
  }
}

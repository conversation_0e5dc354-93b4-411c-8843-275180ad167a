/**
 * Student search endpoint for payment form
 * Fetches student data from staff server for payment processing
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { staffService, isStaffIntegrationEnabled } from '@/lib/staff-service';

interface StaffServerStudent {
  id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  status: string;
}

interface StudentWithPaymentInfo {
  id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  paymentStatus: 'paid' | 'unpaid' | 'debt';
  debtAmount: number;
  lastPaymentDate?: string;
}

// GET /api/students/search - Search students from staff server for payment form
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions - only admin, cashier, and accountant can access
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50);

    try {
      // Check if staff server integration is enabled
      if (!isStaffIntegrationEnabled()) {
        return createErrorResponse('Staff server integration not configured', 500);
      }

      // Fetch students from staff server using the service
      const staffData = await staffService.searchStudents({
        search,
        page,
        limit
      });

      const staffStudents: StaffServerStudent[] = staffData.students || [];

      // Get payment information for each student
      const studentsWithPaymentInfo: StudentWithPaymentInfo[] = [];

      for (const student of staffStudents) {
        try {
          // Check if student exists in our records
          const studentRecordResult = await query(`
            SELECT 
              sr.total_debt,
              sr.last_payment_date,
              COALESCE(
                CASE 
                  WHEN sr.total_debt > 0 THEN 'debt'
                  WHEN sr.last_payment_date IS NOT NULL AND sr.last_payment_date >= CURRENT_DATE - INTERVAL '30 days' THEN 'paid'
                  ELSE 'unpaid'
                END, 
                'unpaid'
              ) as payment_status
            FROM student_records sr 
            WHERE sr.staff_student_id = $1
          `, [student.id]);

          let paymentStatus: 'paid' | 'unpaid' | 'debt' = 'unpaid';
          let debtAmount = 0;
          let lastPaymentDate: string | undefined;

          if (studentRecordResult.rows.length > 0) {
            const record = studentRecordResult.rows[0];
            paymentStatus = record.payment_status;
            debtAmount = parseFloat(record.total_debt || 0);
            lastPaymentDate = record.last_payment_date;
          }

          studentsWithPaymentInfo.push({
            id: student.id,
            firstName: student.firstName,
            lastName: student.lastName,
            phone: student.phone,
            paymentStatus,
            debtAmount,
            lastPaymentDate
          });
        } catch (error) {
          console.error(`Error getting payment info for student ${student.id}:`, error);
          // Add student with default payment info if query fails
          studentsWithPaymentInfo.push({
            id: student.id,
            firstName: student.firstName,
            lastName: student.lastName,
            phone: student.phone,
            paymentStatus: 'unpaid',
            debtAmount: 0
          });
        }
      }

      return createResponse({
        students: studentsWithPaymentInfo,
        pagination: staffData.pagination || {
          page,
          limit,
          total: studentsWithPaymentInfo.length,
          totalPages: Math.ceil(studentsWithPaymentInfo.length / limit),
          hasNext: false,
          hasPrev: false
        }
      }, true, 'Students retrieved successfully');

    } catch (error) {
      console.error('Error fetching students:', error);
      return createErrorResponse('Failed to fetch students', 500);
    }

  } catch (error) {
    console.error('Student search error:', error);
    return createErrorResponse('Internal server error', 500);
  }
}

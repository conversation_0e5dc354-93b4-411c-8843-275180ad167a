/**
 * Individual student management endpoints
 * Handles GET, PUT, DELETE operations for specific students
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidEmail,
  isValidUUID,
  formatPhoneNumber
} from '@/lib/utils';
import { logStudentOperation, getRequestContext } from '@/lib/activity-logger';
import { adminService, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole, StudentStatus } from '@/shared/types/common';

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  date_of_birth?: Date;
  enrollment_date: Date;
  status: StudentStatus;
  created_at: Date;
  updated_at: Date;
}

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const sourceService = request.headers.get('X-Source-Service');
  const expectedKey = process.env.ADMIN_SERVER_API_KEY;

  if (!expectedKey) {
    return false;
  }

  return apiKey === expectedKey && sourceService === 'admin-server';
}

// GET /api/students/[id] - Get specific student
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check for API key authentication (for admin server integration)
    const isApiKeyAuth = validateApiKey(request);

    if (!isApiKeyAuth) {
      // Authenticate user with JWT
      const authResult = await getUserFromRequest(request);
      if (!authResult.success || !authResult.user) {
        return createErrorResponse('Authentication required', 401);
      }

      // Check permissions - all staff roles can view students
      if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
        return createErrorResponse('Insufficient permissions', 403);
      }
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    try {
      // Get student details
      const studentResult = await query<Student>(
        `SELECT id, first_name, last_name, email, phone, date_of_birth, 
                enrollment_date, status, created_at, updated_at 
         FROM students WHERE id = $1`,
        [id]
      );

      if (studentResult.rows.length === 0) {
        return createErrorResponse('Student not found', 404);
      }

      const student = studentResult.rows[0];

      return createResponse({
        id: student.id,
        firstName: student.first_name,
        lastName: student.last_name,
        email: student.email,
        phone: student.phone,
        dateOfBirth: student.date_of_birth,
        enrollmentDate: student.enrollment_date,
        status: student.status,
        createdAt: student.created_at,
        updatedAt: student.updated_at
      }, true, 'Student retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving student:', dbError);
      return createErrorResponse('Failed to retrieve student', 500);
    }

  } catch (error) {
    console.error('Get student error:', error);
    return createErrorResponse('Failed to retrieve student', 500);
  }
}

// PUT /api/students/[id] - Update specific student
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    // Check permissions - management and reception can update students, teachers can update limited fields
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { firstName, lastName, email, phone, dateOfBirth, enrollmentDate, status } = body;

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate status if provided
    if (status && !['active', 'inactive', 'graduated', 'dropped'].includes(status)) {
      return createErrorResponse('Invalid status. Must be active, inactive, graduated, or dropped', 400);
    }

    // Teachers can only update limited fields
    if (authResult.user.role === 'teacher' && (enrollmentDate !== undefined || status !== undefined)) {
      return createErrorResponse('Teachers cannot modify enrollment date or status', 403);
    }

    // Validate dates
    if (dateOfBirth && new Date(dateOfBirth) > new Date()) {
      return createErrorResponse('Date of birth cannot be in the future', 400);
    }

    if (enrollmentDate && new Date(enrollmentDate) > new Date()) {
      return createErrorResponse('Enrollment date cannot be in the future', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current student data
      const currentStudentResult = await query<Student>(
        `SELECT id, first_name, last_name, email, phone, date_of_birth, 
                enrollment_date, status, created_at, updated_at 
         FROM students WHERE id = $1`,
        [id]
      );

      if (currentStudentResult.rows.length === 0) {
        return createErrorResponse('Student not found', 404);
      }

      const currentStudent = currentStudentResult.rows[0];

      // Check if email is being changed and if it already exists
      if (email && email.toLowerCase() !== currentStudent.email) {
        const existingStudentResult = await query(
          'SELECT id FROM students WHERE email = $1 AND id != $2',
          [email.toLowerCase(), id]
        );

        if (existingStudentResult.rows.length > 0) {
          return createErrorResponse('Student with this email already exists', 409);
        }
      }

      // Build update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (firstName !== undefined) {
        updateFields.push(`first_name = $${paramIndex++}`);
        updateValues.push(firstName.trim());
      }

      if (lastName !== undefined) {
        updateFields.push(`last_name = $${paramIndex++}`);
        updateValues.push(lastName.trim());
      }

      if (email !== undefined) {
        updateFields.push(`email = $${paramIndex++}`);
        updateValues.push(email ? email.toLowerCase() : null);
      }

      if (phone !== undefined) {
        updateFields.push(`phone = $${paramIndex++}`);
        updateValues.push(phone ? formatPhoneNumber(phone) : null);
      }

      if (dateOfBirth !== undefined) {
        updateFields.push(`date_of_birth = $${paramIndex++}`);
        updateValues.push(dateOfBirth || null);
      }

      if (enrollmentDate !== undefined) {
        updateFields.push(`enrollment_date = $${paramIndex++}`);
        updateValues.push(enrollmentDate);
      }

      if (status !== undefined) {
        updateFields.push(`status = $${paramIndex++}`);
        updateValues.push(status);
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const updateSql = `
        UPDATE students 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, first_name, last_name, email, phone, date_of_birth, 
                  enrollment_date, status, created_at, updated_at
      `;

      const updatedStudentResult = await query<Student>(updateSql, updateValues);
      const updatedStudent = updatedStudentResult.rows[0];

      // Log student update
      await logStudentOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedStudent,
        currentStudent,
        context
      );

      // Sync status change with admin server if status was updated
      if (status !== undefined && status !== currentStudent.status && isAdminIntegrationEnabled()) {
        await adminService.syncStudentStatusChange({
          id: updatedStudent.id,
          oldStatus: currentStudent.status,
          newStatus: updatedStudent.status,
          firstName: updatedStudent.first_name,
          lastName: updatedStudent.last_name,
          email: updatedStudent.email,
          changedBy: authResult.user.id,
        });
      }

      return createResponse({
        id: updatedStudent.id,
        firstName: updatedStudent.first_name,
        lastName: updatedStudent.last_name,
        email: updatedStudent.email,
        phone: updatedStudent.phone,
        dateOfBirth: updatedStudent.date_of_birth,
        enrollmentDate: updatedStudent.enrollment_date,
        status: updatedStudent.status,
        createdAt: updatedStudent.created_at,
        updatedAt: updatedStudent.updated_at
      }, true, 'Student updated successfully');

    } catch (dbError) {
      console.error('Database error updating student:', dbError);
      return createErrorResponse('Failed to update student', 500);
    }

  } catch (error) {
    console.error('Update student error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

// DELETE /api/students/[id] - Deactivate specific student
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    // Only management and reception can deactivate students
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current student data
      const currentStudentResult = await query<Student>(
        `SELECT id, first_name, last_name, email, phone, date_of_birth, 
                enrollment_date, status, created_at, updated_at 
         FROM students WHERE id = $1`,
        [id]
      );

      if (currentStudentResult.rows.length === 0) {
        return createErrorResponse('Student not found', 404);
      }

      const currentStudent = currentStudentResult.rows[0];

      if (currentStudent.status === 'inactive') {
        return createErrorResponse('Student is already inactive', 400);
      }

      // Deactivate student (soft delete)
      const deactivatedStudentResult = await query<Student>(
        `UPDATE students 
         SET status = 'inactive', updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING id, first_name, last_name, email, phone, date_of_birth, 
                   enrollment_date, status, created_at, updated_at`,
        [id]
      );

      const deactivatedStudent = deactivatedStudentResult.rows[0];

      // Log student deactivation
      await logStudentOperation(
        'DELETE' as any,
        authResult.user.id,
        deactivatedStudent,
        currentStudent,
        context
      );

      return createResponse({
        id: deactivatedStudent.id,
        firstName: deactivatedStudent.first_name,
        lastName: deactivatedStudent.last_name,
        email: deactivatedStudent.email,
        phone: deactivatedStudent.phone,
        dateOfBirth: deactivatedStudent.date_of_birth,
        enrollmentDate: deactivatedStudent.enrollment_date,
        status: deactivatedStudent.status,
        createdAt: deactivatedStudent.created_at,
        updatedAt: deactivatedStudent.updated_at
      }, true, 'Student deactivated successfully');

    } catch (dbError) {
      console.error('Database error deactivating student:', dbError);
      return createErrorResponse('Failed to deactivate student', 500);
    }

  } catch (error) {
    console.error('Deactivate student error:', error);
    return createErrorResponse('Failed to deactivate student', 500);
  }
}

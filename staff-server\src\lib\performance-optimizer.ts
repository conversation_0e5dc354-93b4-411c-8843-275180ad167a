/**
 * Performance Optimization Utilities for Staff Server
 * Implements caching, query optimization, and response compression
 */

import { debugLogger } from './debug-logger';

// Simple in-memory cache implementation
class MemoryCache {
  private cache = new Map<string, { data: any; expiry: number }>();
  private maxSize = 1000;

  set(key: string, data: any, ttlSeconds: number = 300) {
    // Clean up expired entries if cache is getting full
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
    
    debugLogger.debug('CACHE', `Cache SET: ${key} (TTL: ${ttlSeconds}s)`);
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      debugLogger.debug('CACHE', `Cache MISS: ${key}`);
      return null;
    }

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      debugLogger.debug('CACHE', `Cache EXPIRED: ${key}`);
      return null;
    }

    debugLogger.debug('CACHE', `Cache HIT: ${key}`);
    return entry.data;
  }

  delete(key: string) {
    const deleted = this.cache.delete(key);
    if (deleted) {
      debugLogger.debug('CACHE', `Cache DELETE: ${key}`);
    }
    return deleted;
  }

  clear() {
    const size = this.cache.size;
    this.cache.clear();
    debugLogger.info('CACHE', `Cache cleared: ${size} entries removed`);
  }

  private cleanup() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    debugLogger.debug('CACHE', `Cache cleanup: ${cleaned} expired entries removed`);
  }

  getStats() {
    const now = Date.now();
    let expired = 0;
    
    for (const entry of this.cache.values()) {
      if (now > entry.expiry) {
        expired++;
      }
    }

    return {
      total: this.cache.size,
      expired,
      active: this.cache.size - expired,
      maxSize: this.maxSize
    };
  }
}

// Global cache instance
export const cache = new MemoryCache();

// Student data caching utilities
export class StudentCache {
  private static CACHE_TTL = 300; // 5 minutes

  static getCacheKey(operation: string, params?: any): string {
    if (params) {
      return `students:${operation}:${JSON.stringify(params)}`;
    }
    return `students:${operation}`;
  }

  static cacheStudentList(students: any[], filters: any = {}) {
    const key = this.getCacheKey('list', filters);
    cache.set(key, students, this.CACHE_TTL);
  }

  static getCachedStudentList(filters: any = {}): any[] | null {
    const key = this.getCacheKey('list', filters);
    return cache.get(key);
  }

  static cacheStudent(student: any) {
    const key = this.getCacheKey('detail', { id: student.id });
    cache.set(key, student, this.CACHE_TTL);
  }

  static getCachedStudent(id: string): any | null {
    const key = this.getCacheKey('detail', { id });
    return cache.get(key);
  }

  static invalidateStudentCache(id?: string) {
    if (id) {
      // Invalidate specific student
      cache.delete(this.getCacheKey('detail', { id }));
    }
    
    // Invalidate all student lists (they might contain the updated student)
    // In a real implementation, you'd use cache tags or patterns
    cache.clear(); // Simplified approach
    
    debugLogger.info('CACHE', `Student cache invalidated${id ? ` for ID: ${id}` : ' (all)'}`);
  }
}

// Database query optimization utilities
export class QueryOptimizer {
  static buildStudentSearchQuery(searchTerm: string, filters: any = {}) {
    let query = `
      SELECT s.*, 
             COUNT(*) OVER() as total_count
      FROM students s
    `;
    
    const conditions: string[] = [];
    const params: any[] = [];

    // Search term
    if (searchTerm) {
      conditions.push(`(
        s.first_name ILIKE $${params.length + 1} OR 
        s.last_name ILIKE $${params.length + 1} OR 
        s.email ILIKE $${params.length + 1}
      )`);
      params.push(`%${searchTerm}%`);
    }

    // Status filter
    if (filters.status) {
      conditions.push(`s.status = $${params.length + 1}`);
      params.push(filters.status);
    }

    // Date range filter
    if (filters.startDate) {
      conditions.push(`s.enrollment_date >= $${params.length + 1}`);
      params.push(filters.startDate);
    }

    if (filters.endDate) {
      conditions.push(`s.enrollment_date <= $${params.length + 1}`);
      params.push(filters.endDate);
    }

    // Add WHERE clause if conditions exist
    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    // Add sorting
    const sortBy = filters.sortBy || 'created_at';
    const sortOrder = filters.sortOrder || 'DESC';
    query += ` ORDER BY s.${sortBy} ${sortOrder}`;

    // Add pagination
    const limit = filters.limit || 10;
    const offset = ((filters.page || 1) - 1) * limit;
    query += ` LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);

    return { query, params };
  }

  static buildActivityLogQuery(userId: string, limit: number = 50) {
    const query = `
      SELECT 
        action,
        resource_type,
        description,
        timestamp,
        new_values
      FROM activity_logs 
      WHERE user_id = $1 
      ORDER BY timestamp DESC 
      LIMIT $2
    `;
    
    return { query, params: [userId, limit] };
  }
}

// Response optimization for student data
export class StudentResponseOptimizer {
  static optimizeStudentData(student: any): any {
    // Remove sensitive or unnecessary fields for API responses
    const optimized = {
      id: student.id,
      firstName: student.first_name,
      lastName: student.last_name,
      email: student.email,
      phone: student.phone,
      dateOfBirth: student.date_of_birth,
      enrollmentDate: student.enrollment_date,
      status: student.status,
      createdAt: student.created_at,
      updatedAt: student.updated_at
    };

    // Remove null/undefined values
    Object.keys(optimized).forEach(key => {
      if (optimized[key] === null || optimized[key] === undefined) {
        delete optimized[key];
      }
    });

    return optimized;
  }

  static optimizeStudentList(students: any[]): any[] {
    return students.map(student => this.optimizeStudentData(student));
  }

  static createPaginatedResponse(students: any[], totalCount: number, page: number, limit: number) {
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      students: this.optimizeStudentList(students),
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }
}

// Performance monitoring for staff operations
export class StaffPerformanceMonitor {
  private static metrics = new Map<string, { count: number; totalTime: number; avgTime: number }>();

  static startTimer(operation: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
      debugLogger.debug('PERFORMANCE', `${operation} completed in ${duration}ms`);
    };
  }

  private static recordMetric(operation: string, duration: number) {
    const existing = this.metrics.get(operation) || { count: 0, totalTime: 0, avgTime: 0 };
    
    existing.count++;
    existing.totalTime += duration;
    existing.avgTime = existing.totalTime / existing.count;
    
    this.metrics.set(operation, existing);
  }

  static getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((metric, operation) => {
      result[operation] = {
        count: metric.count,
        totalTime: metric.totalTime,
        avgTime: Math.round(metric.avgTime * 100) / 100
      };
    });

    return result;
  }

  static getTopSlowOperations(limit: number = 5): Array<{ operation: string; avgTime: number }> {
    const operations = Array.from(this.metrics.entries())
      .map(([operation, metric]) => ({ operation, avgTime: metric.avgTime }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, limit);

    return operations;
  }
}

// Batch operations for better performance
export class BatchOperations {
  static async batchCreateStudents(studentsData: any[], batchSize: number = 10): Promise<any[]> {
    const results: any[] = [];
    const stopTimer = StaffPerformanceMonitor.startTimer('batch_create_students');

    try {
      for (let i = 0; i < studentsData.length; i += batchSize) {
        const batch = studentsData.slice(i, i + batchSize);
        
        // Process batch (simplified - in real implementation, use database batch insert)
        const batchResults = await Promise.all(
          batch.map(async (studentData) => {
            // Individual student creation logic here
            return { ...studentData, id: `generated-id-${Date.now()}-${Math.random()}` };
          })
        );

        results.push(...batchResults);
        
        // Small delay between batches to avoid overwhelming the system
        if (i + batchSize < studentsData.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      debugLogger.info('BATCH', `Successfully created ${results.length} students in batches`);
      return results;
    } finally {
      stopTimer();
    }
  }

  static async batchUpdateStudents(updates: Array<{ id: string; data: any }>, batchSize: number = 10): Promise<any[]> {
    const results: any[] = [];
    const stopTimer = StaffPerformanceMonitor.startTimer('batch_update_students');

    try {
      for (let i = 0; i < updates.length; i += batchSize) {
        const batch = updates.slice(i, i + batchSize);
        
        const batchResults = await Promise.all(
          batch.map(async (update) => {
            // Individual student update logic here
            return { id: update.id, ...update.data, updated: true };
          })
        );

        results.push(...batchResults);
        
        // Invalidate cache for updated students
        batch.forEach(update => {
          StudentCache.invalidateStudentCache(update.id);
        });
      }

      debugLogger.info('BATCH', `Successfully updated ${results.length} students in batches`);
      return results;
    } finally {
      stopTimer();
    }
  }
}

// Middleware for performance monitoring
export function createPerformanceMiddleware() {
  return (req: any, res: any, next: any) => {
    const stopTimer = StaffPerformanceMonitor.startTimer(`${req.method} ${req.path}`);
    
    const originalSend = res.send;
    res.send = function(data: any) {
      stopTimer();
      return originalSend.call(this, data);
    };

    next();
  };
}

// Export all utilities
export default {
  cache,
  StudentCache,
  QueryOptimizer,
  StudentResponseOptimizer,
  StaffPerformanceMonitor,
  BatchOperations,
  createPerformanceMiddleware
};

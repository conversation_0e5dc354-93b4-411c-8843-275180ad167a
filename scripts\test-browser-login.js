/**
 * Test staff login through browser simulation
 */

const { default: fetch } = require('node-fetch');

const STAFF_SERVER_URL = 'http://localhost:3001';

async function testStaffLogin() {
  console.log('🔍 Testing staff login through browser simulation...');
  
  const testUsers = [
    { email: '<EMAIL>', password: 'Manager123!', role: 'management' },
    { email: '<EMAIL>', password: 'Reception123!', role: 'reception' },
    { email: '<EMAIL>', password: 'Teacher123!', role: 'teacher' }
  ];
  
  for (const user of testUsers) {
    console.log(`\n🔍 Testing ${user.role} login: ${user.email}`);
    
    try {
      const response = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: user.email,
          password: user.password
        })
      });
      
      console.log(`Response status: ${response.status}`);
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log('✅ Login successful');
        console.log(`  - User: ${data.data.user.name}`);
        console.log(`  - Role: ${data.data.user.role}`);
        console.log(`  - Token: ${data.data.token ? 'Received' : 'Missing'}`);
        
        // Test if we can access a protected endpoint with the token
        const dashboardResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${data.data.token}`
          }
        });
        
        if (dashboardResponse.ok) {
          console.log('✅ Token validation successful');
        } else {
          console.log('❌ Token validation failed');
        }
      } else {
        console.log('❌ Login failed');
        console.log(`  - Error: ${data.error}`);
        console.log(`  - Full response:`, JSON.stringify(data, null, 2));
      }
    } catch (error) {
      console.log('❌ Request failed');
      console.log(`  - Error: ${error.message}`);
    }
  }
}

async function testInvalidLogin() {
  console.log('\n🔍 Testing invalid login...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    });
    
    const data = await response.json();
    
    if (!response.ok && !data.success) {
      console.log('✅ Invalid login properly rejected');
      console.log(`  - Status: ${response.status}`);
      console.log(`  - Error: ${data.error}`);
    } else {
      console.log('❌ Invalid login was accepted (security issue!)');
    }
  } catch (error) {
    console.log('❌ Invalid login test failed');
    console.log(`  - Error: ${error.message}`);
  }
}

async function runTest() {
  console.log('🚀 Staff Login Browser Simulation Test');
  console.log('='.repeat(50));
  
  await testStaffLogin();
  await testInvalidLogin();
  
  console.log('\n' + '='.repeat(50));
  console.log('✨ Test completed! Check results above.');
  console.log('If all tests passed, the login should work in the browser.');
  console.log('='.repeat(50));
}

runTest().catch(error => {
  console.error('❌ Test failed:', error);
});

/**
 * Verify Real Interserver Communication
 * Comprehensive test to verify that mock data has been removed and real interserver communication is working
 */

// Use built-in fetch for Node.js 18+
const fetch = globalThis.fetch || require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration from environment
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
const STAFF_SERVER_URL = process.env.STAFF_SERVER_URL || 'http://localhost:3003';
const STAFF_SERVER_API_KEY = process.env.STAFF_SERVER_API_KEY || 'staff-server-api-key-innovative-centre-2024';

console.log('🔍 Verifying Real Interserver Communication Setup...\n');

/**
 * Check if mock data has been removed from key files
 */
function verifyMockDataRemoval() {
  console.log('🧹 Checking Mock Data Removal...');
  
  const filesToCheck = [
    'admin-server/tests/utils/test-helpers.ts',
    'admin-server/jest.setup.js',
    'staff-server/jest.setup.js',
    'admin-server/tests/integration/payments.test.ts'
  ];
  
  const mockPatterns = [
    /mockUser\s*\(/,
    /mockPayment\s*\(/,
    /mockDatabaseQuery\s*\(/,
    /jest\.mock\s*\(/,
    /mockQuery\s*\./,
    /mockGetUserFromRequest/,
    /mockToken\s*:/
  ];
  
  let mockDataFound = false;
  
  filesToCheck.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);
    
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      mockPatterns.forEach(pattern => {
        if (pattern.test(content)) {
          console.log(`⚠️  Mock pattern found in ${filePath}: ${pattern}`);
          mockDataFound = true;
        }
      });
    }
  });
  
  if (!mockDataFound) {
    console.log('✅ No mock data patterns found in key files');
  }
  
  return !mockDataFound;
}

/**
 * Verify real database configuration
 */
function verifyDatabaseConfiguration() {
  console.log('\n🗄️  Checking Database Configuration...');
  
  const envFiles = [
    'admin-server/.env.local',
    'staff-server/.env.local'
  ];
  
  let realDbConfigured = true;
  
  envFiles.forEach(envFile => {
    const fullPath = path.join(process.cwd(), envFile);
    
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      if (content.includes('postgresql://')) {
        console.log(`✅ Real PostgreSQL database configured in ${envFile}`);
      } else {
        console.log(`❌ No real database configuration found in ${envFile}`);
        realDbConfigured = false;
      }
    } else {
      console.log(`❌ Environment file not found: ${envFile}`);
      realDbConfigured = false;
    }
  });
  
  return realDbConfigured;
}

/**
 * Verify API key configuration
 */
function verifyApiKeyConfiguration() {
  console.log('\n🔑 Checking API Key Configuration...');
  
  const adminEnvPath = path.join(process.cwd(), 'admin-server/.env.local');
  const staffEnvPath = path.join(process.cwd(), 'staff-server/.env.local');
  
  let apiKeysConfigured = true;
  
  if (fs.existsSync(adminEnvPath)) {
    const adminContent = fs.readFileSync(adminEnvPath, 'utf8');
    if (adminContent.includes('STAFF_SERVER_API_KEY=')) {
      console.log('✅ Admin server has staff server API key configured');
    } else {
      console.log('❌ Admin server missing staff server API key');
      apiKeysConfigured = false;
    }
  }
  
  if (fs.existsSync(staffEnvPath)) {
    const staffContent = fs.readFileSync(staffEnvPath, 'utf8');
    if (staffContent.includes('ADMIN_SERVER_API_KEY=')) {
      console.log('✅ Staff server has admin server API key configured');
    } else {
      console.log('❌ Staff server missing admin server API key');
      apiKeysConfigured = false;
    }
  }
  
  return apiKeysConfigured;
}

/**
 * Verify staff integration endpoints exist
 */
function verifyStaffIntegrationEndpoints() {
  console.log('\n🔗 Checking Staff Integration Endpoints...');
  
  const staffIntegrationDir = path.join(process.cwd(), 'admin-server/src/app/api/staff-integration');
  
  if (!fs.existsSync(staffIntegrationDir)) {
    console.log('❌ Staff integration directory not found');
    return false;
  }
  
  const expectedEndpoints = [
    'auth',
    'activity-sync',
    'student-financial',
    'student-status',
    'kpis'
  ];
  
  let allEndpointsExist = true;
  
  expectedEndpoints.forEach(endpoint => {
    const endpointPath = path.join(staffIntegrationDir, endpoint, 'route.ts');
    const endpointDirPath = path.join(staffIntegrationDir, endpoint);

    if (fs.existsSync(endpointPath)) {
      console.log(`✅ Staff integration endpoint exists: ${endpoint}`);
    } else if (fs.existsSync(endpointDirPath)) {
      // Check if it has subdirectories with route.ts (like [id]/route.ts)
      const subDirs = fs.readdirSync(endpointDirPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      const hasSubRoutes = subDirs.some(subDir => {
        const subRoutePath = path.join(endpointDirPath, subDir, 'route.ts');
        return fs.existsSync(subRoutePath);
      });

      if (hasSubRoutes) {
        console.log(`✅ Staff integration endpoint exists: ${endpoint} (with sub-routes)`);
      } else {
        console.log(`❌ Staff integration endpoint missing: ${endpoint}`);
        allEndpointsExist = false;
      }
    } else {
      console.log(`❌ Staff integration endpoint missing: ${endpoint}`);
      allEndpointsExist = false;
    }
  });
  
  return allEndpointsExist;
}

/**
 * Test real data creation functions
 */
function verifyRealDataFunctions() {
  console.log('\n🏗️  Checking Real Data Creation Functions...');
  
  const testHelpersPath = path.join(process.cwd(), 'admin-server/tests/utils/test-helpers.ts');
  
  if (!fs.existsSync(testHelpersPath)) {
    console.log('❌ Test helpers file not found');
    return false;
  }
  
  const content = fs.readFileSync(testHelpersPath, 'utf8');
  
  const expectedFunctions = [
    'createTestUser',
    'createTestPayment',
    'cleanupTestData'
  ];
  
  let allFunctionsExist = true;
  
  expectedFunctions.forEach(func => {
    if (content.includes(`export const ${func}`)) {
      console.log(`✅ Real data function exists: ${func}`);
    } else {
      console.log(`❌ Real data function missing: ${func}`);
      allFunctionsExist = false;
    }
  });
  
  return allFunctionsExist;
}

/**
 * Main verification function
 */
async function runVerification() {
  console.log('🚀 Starting Real Interserver Communication Verification...\n');
  
  const results = {
    mockDataRemoved: verifyMockDataRemoval(),
    databaseConfigured: verifyDatabaseConfiguration(),
    apiKeysConfigured: verifyApiKeyConfiguration(),
    staffEndpointsExist: verifyStaffIntegrationEndpoints(),
    realDataFunctions: verifyRealDataFunctions()
  };
  
  console.log('\n📊 Verification Results Summary:');
  console.log(`Mock Data Removed: ${results.mockDataRemoved ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Database Configured: ${results.databaseConfigured ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API Keys Configured: ${results.apiKeysConfigured ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Staff Endpoints Exist: ${results.staffEndpointsExist ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Real Data Functions: ${results.realDataFunctions ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 All verifications passed!');
    console.log('✅ Mock data has been successfully removed');
    console.log('✅ Real database connections are configured');
    console.log('✅ Interserver communication infrastructure is ready');
    console.log('\n💡 To test real interserver communication:');
    console.log('   1. Start both servers:');
    console.log('      Terminal 1: npm run dev:admin');
    console.log('      Terminal 2: npm run dev:staff');
    console.log('   2. Run integration tests:');
    console.log('      node scripts/test-integration.js');
  } else {
    console.log('\n⚠️  Some verifications failed. Please address the issues above.');
  }
  
  return allPassed;
}

// Run the verification
runVerification()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });

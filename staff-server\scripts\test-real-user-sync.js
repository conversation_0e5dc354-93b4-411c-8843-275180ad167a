/**
 * Test activity sync with real staff user ID
 */

require('dotenv').config({ path: '.env.local' });

async function testRealUserSync() {
  console.log('🔍 Testing Activity Sync with Real Staff User...');
  
  const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
  const apiKey = process.env.ADMIN_SERVER_API_KEY;
  
  console.log('🔗 Admin Server URL:', adminServerUrl);
  console.log('🔑 API Key configured:', !!apiKey);
  
  if (!apiKey) {
    console.error('❌ ADMIN_SERVER_API_KEY not configured');
    return;
  }

  // Use a real staff user ID from the admin server
  // Using <EMAIL> (ID: ef8ad1f1-3c13-46d5-a75b-df41589da456)
  const realStaffUserId = 'ef8ad1f1-3c13-46d5-a75b-df41589da456';

  // Test data with real staff user ID
  const testActivityData = {
    userId: realStaffUserId,
    action: 'CREATE',
    resourceType: 'STUDENT',
    resourceId: '12345678-1234-1234-1234-123456789012',
    description: 'Test student creation from staff server with real user ID',
    sourceService: 'staff-server',
    timestamp: new Date().toISOString(),
    newValues: {
      firstName: 'Test',
      lastName: 'Student',
      email: '<EMAIL>'
    }
  };

  try {
    console.log('\n📤 Sending test activity with real staff user ID...');
    console.log('👤 Using staff user ID:', realStaffUserId);
    
    const response = await fetch(`${adminServerUrl}/api/staff-integration/activity-sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
        'X-Source-Service': 'staff-server',
      },
      body: JSON.stringify(testActivityData),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response status text:', response.statusText);

    const responseText = await response.text();
    console.log('📡 Response body:', responseText);

    if (response.ok) {
      console.log('✅ Activity sync with real staff user ID successful!');
      
      try {
        const responseData = JSON.parse(responseText);
        console.log('📋 Parsed response:', responseData);
      } catch (parseError) {
        console.log('⚠️  Response is not JSON');
      }

      // Check if the activity appears in admin server logs
      console.log('\n🔍 Checking if activity appears in admin server logs...');
      
      // Wait a moment for the activity to be processed
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      try {
        const logsResponse = await fetch(`${adminServerUrl}/api/activity-logs?limit=5&action=CREATE&resourceType=STUDENT`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (logsResponse.ok) {
          const logsData = await logsResponse.json();
          console.log('📊 Recent student creation logs:');
          if (logsData.success && logsData.data && logsData.data.logs) {
            logsData.data.logs.forEach((log, index) => {
              console.log(`  ${index + 1}. [${log.timestamp}] ${log.description}`);
              console.log(`      User: ${log.user?.name || log.user?.email || log.userId}`);
            });
          } else {
            console.log('  No logs found or invalid response format');
          }
        } else {
          console.log('⚠️  Could not fetch activity logs for verification');
        }
      } catch (logsError) {
        console.log('⚠️  Error fetching logs for verification:', logsError.message);
      }

    } else {
      console.log('❌ Activity sync with real staff user ID failed');
      
      // Try to parse error response
      try {
        const errorData = JSON.parse(responseText);
        console.log('❌ Error details:', errorData);
      } catch (parseError) {
        console.log('❌ Raw error response:', responseText);
      }
    }

  } catch (error) {
    console.error('❌ Error testing real user sync:', error.message);
    console.error('Error details:', error);
  }

  console.log('\n🎉 Real user sync test completed!');
}

// Run the test
testRealUserSync();

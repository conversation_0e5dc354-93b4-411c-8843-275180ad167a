/**
 * Student status synchronization endpoint
 * Receives status change notifications from staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const body = await request.json();
    const {
      id,
      oldStatus,
      newStatus,
      firstName,
      lastName,
      email,
      changedBy,
      reason
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['id', 'oldStatus', 'newStatus', 'firstName', 'lastName', 'changedBy']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    try {
      // Update student record status
      const updateResult = await query(`
        UPDATE student_records 
        SET status = $2, updated_at = CURRENT_TIMESTAMP
        WHERE staff_student_id = $1
        RETURNING id
      `, [id, newStatus]);

      if (updateResult.rows.length === 0) {
        // Student record doesn't exist, create it
        await query(`
          INSERT INTO student_records (
            staff_student_id, first_name, last_name, email, status
          ) VALUES ($1, $2, $3, $4, $5)
        `, [id, firstName, lastName, email, newStatus]);
      }

      // Handle status-specific actions
      if (newStatus === 'inactive' || newStatus === 'dropped') {
        // Cancel pending invoices for inactive/dropped students
        await query(`
          UPDATE invoices 
          SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
          WHERE student_id = (
            SELECT id FROM student_records WHERE staff_student_id = $1
          ) AND status = 'pending'
        `, [id]);

        console.log(`Cancelled pending invoices for student: ${firstName} ${lastName} (${id})`);
      } else if (newStatus === 'active' && (oldStatus === 'inactive' || oldStatus === 'dropped')) {
        // Reactivate student - might need to create new invoices
        console.log(`Reactivated student: ${firstName} ${lastName} (${id})`);
      }

      // Log the status change
      await query(`
        INSERT INTO activity_logs (
          user_id, action, resource_type, resource_id,
          old_values, new_values, description, source_service
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        changedBy,
        'UPDATE',
        'STUDENT_STATUS',
        id,
        JSON.stringify({ status: oldStatus }),
        JSON.stringify({ status: newStatus }),
        `Student status changed from ${oldStatus} to ${newStatus}${reason ? ` - ${reason}` : ''}`,
        'staff-server'
      ]);

      return createResponse({
        staffStudentId: id,
        oldStatus,
        newStatus,
        processed: true,
        timestamp: new Date().toISOString()
      }, true, 'Student status synchronized successfully');

    } catch (dbError) {
      console.error('Database error syncing student status:', dbError);
      return createErrorResponse('Failed to sync student status', 500);
    }

  } catch (error) {
    console.error('Student status sync error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

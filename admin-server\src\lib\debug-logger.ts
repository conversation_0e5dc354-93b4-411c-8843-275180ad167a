/**
 * Enhanced Debug Logger for Admin Server
 * Provides comprehensive logging and debugging capabilities
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  userId?: string;
  requestId?: string;
  source: 'admin-server';
}

class DebugLogger {
  private logLevel: LogLevel;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;

  constructor() {
    this.logLevel = process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG;
  }

  private createLogEntry(level: LogLevel, category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      userId: context?.userId,
      requestId: context?.requestId,
      source: 'admin-server'
    };
  }

  private addLog(entry: LogEntry) {
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console output with colors
    const levelNames = ['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE'];
    const colors = ['\x1b[31m', '\x1b[33m', '\x1b[36m', '\x1b[32m', '\x1b[37m'];
    const reset = '\x1b[0m';

    if (entry.level <= this.logLevel) {
      const color = colors[entry.level];
      const levelName = levelNames[entry.level];
      console.log(`${color}[${entry.timestamp}] ${levelName} [${entry.category}]${reset} ${entry.message}`);
      
      if (entry.data) {
        console.log(`${color}  Data:${reset}`, entry.data);
      }
      
      if (entry.userId) {
        console.log(`${color}  User:${reset} ${entry.userId}`);
      }
      
      if (entry.requestId) {
        console.log(`${color}  Request:${reset} ${entry.requestId}`);
      }
    }
  }

  error(category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }) {
    this.addLog(this.createLogEntry(LogLevel.ERROR, category, message, data, context));
  }

  warn(category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }) {
    this.addLog(this.createLogEntry(LogLevel.WARN, category, message, data, context));
  }

  info(category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }) {
    this.addLog(this.createLogEntry(LogLevel.INFO, category, message, data, context));
  }

  debug(category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }) {
    this.addLog(this.createLogEntry(LogLevel.DEBUG, category, message, data, context));
  }

  trace(category: string, message: string, data?: any, context?: { userId?: string; requestId?: string }) {
    this.addLog(this.createLogEntry(LogLevel.TRACE, category, message, data, context));
  }

  // Activity logging specific methods
  logActivity(action: string, resourceType: string, userId: string, details?: any, requestId?: string) {
    this.info('ACTIVITY', `${action} ${resourceType}`, details, { userId, requestId });
  }

  logInterserverRequest(endpoint: string, sourceService: string, data?: any, requestId?: string) {
    this.info('INTERSERVER', `Request from ${sourceService} to ${endpoint}`, data, { requestId });
  }

  logInterserverResponse(endpoint: string, sourceService: string, success: boolean, data?: any, requestId?: string) {
    const level = success ? LogLevel.INFO : LogLevel.ERROR;
    const message = `Response to ${sourceService} for ${endpoint}: ${success ? 'SUCCESS' : 'FAILED'}`;
    this.addLog(this.createLogEntry(level, 'INTERSERVER', message, data, { requestId }));
  }

  logAuthentication(email: string, success: boolean, reason?: string, requestId?: string) {
    const level = success ? LogLevel.INFO : LogLevel.WARN;
    const message = `Authentication ${success ? 'successful' : 'failed'} for ${email}${reason ? `: ${reason}` : ''}`;
    this.addLog(this.createLogEntry(level, 'AUTH', message, undefined, { requestId }));
  }

  logDatabaseOperation(operation: string, table: string, success: boolean, error?: any, requestId?: string) {
    const level = success ? LogLevel.DEBUG : LogLevel.ERROR;
    const message = `Database ${operation} on ${table}: ${success ? 'SUCCESS' : 'FAILED'}`;
    this.addLog(this.createLogEntry(level, 'DATABASE', message, error, { requestId }));
  }

  // Get recent logs for debugging
  getRecentLogs(count: number = 50, level?: LogLevel, category?: string): LogEntry[] {
    let filteredLogs = this.logs;

    if (level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level <= level);
    }

    if (category) {
      filteredLogs = filteredLogs.filter(log => log.category === category);
    }

    return filteredLogs.slice(-count);
  }

  // Export logs for analysis
  exportLogs(filters?: { level?: LogLevel; category?: string; userId?: string; timeRange?: { start: string; end: string } }): LogEntry[] {
    let filteredLogs = this.logs;

    if (filters?.level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level <= filters.level);
    }

    if (filters?.category) {
      filteredLogs = filteredLogs.filter(log => log.category === filters.category);
    }

    if (filters?.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
    }

    if (filters?.timeRange) {
      const start = new Date(filters.timeRange.start);
      const end = new Date(filters.timeRange.end);
      filteredLogs = filteredLogs.filter(log => {
        const logTime = new Date(log.timestamp);
        return logTime >= start && logTime <= end;
      });
    }

    return filteredLogs;
  }

  // Health check method
  getSystemHealth(): { status: string; logCount: number; errorCount: number; lastError?: LogEntry } {
    const errorLogs = this.logs.filter(log => log.level === LogLevel.ERROR);
    const recentErrors = errorLogs.slice(-10);
    
    return {
      status: recentErrors.length > 5 ? 'unhealthy' : 'healthy',
      logCount: this.logs.length,
      errorCount: errorLogs.length,
      lastError: errorLogs[errorLogs.length - 1]
    };
  }
}

// Create singleton instance
export const debugLogger = new DebugLogger();

// Middleware for request logging
export function createRequestLogger() {
  return (req: any, res: any, next: any) => {
    const requestId = Math.random().toString(36).substring(7);
    req.requestId = requestId;
    
    debugLogger.debug('REQUEST', `${req.method} ${req.path}`, {
      headers: req.headers,
      query: req.query,
      body: req.method !== 'GET' ? req.body : undefined
    }, { requestId });

    const originalSend = res.send;
    res.send = function(data: any) {
      debugLogger.debug('RESPONSE', `${req.method} ${req.path} - ${res.statusCode}`, {
        statusCode: res.statusCode,
        responseSize: data ? data.length : 0
      }, { requestId });
      
      return originalSend.call(this, data);
    };

    next();
  };
}

export default debugLogger;

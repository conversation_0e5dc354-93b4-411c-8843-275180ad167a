import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Vercel optimization
  output: 'standalone',

  // External packages for server components
  serverExternalPackages: ['pg'],

  experimental: {
    typedRoutes: false,
  },

  env: {
    DATABASE_URL: process.env.DATABASE_URL,
    JWT_SECRET: process.env.JWT_SECRET,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    ADMIN_SERVER_URL: process.env.ADMIN_SERVER_URL,
    ADMIN_SERVER_API_KEY: process.env.ADMIN_SERVER_API_KEY,
    ENABLE_ADMIN_INTEGRATION: process.env.ENABLE_ADMIN_INTEGRATION,
  },

  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: process.env.ADMIN_SERVER_URL || '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, X-API-Key, X-Source-Service' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'X-Frame-Options', value: 'DENY' },
          { key: 'X-XSS-Protection', value: '1; mode=block' },
        ]
      }
    ];
  },
};

export default nextConfig;

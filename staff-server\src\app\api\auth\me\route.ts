/**
 * Get current authenticated staff user endpoint
 * Returns user information for the authenticated user
 */

import { NextRequest } from 'next/server';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { UserRole } from '@/shared/types/common';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user from token (validated by admin server)
    const authResult = await getUserFromRequest(request);

    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Since staff users are authenticated through admin server,
    // we can trust the token data and return user information directly
    const user = authResult.user;

    return createResponse({
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        name: user.name || 'Staff User', // Fallback name if not in token
        isActive: true, // If token is valid, user is active
        createdAt: new Date().toISOString(), // Placeholder since we don't have this in token
        updatedAt: new Date().toISOString()  // Placeholder since we don't have this in token
      }
    }, true, 'User information retrieved successfully');

  } catch (error) {
    console.error('Get staff user error:', error);
    return createErrorResponse('Failed to retrieve user information', 500);
  }
}

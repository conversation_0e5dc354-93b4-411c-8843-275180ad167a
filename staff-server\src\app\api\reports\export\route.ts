/**
 * Report export endpoint for staff server
 * Exports reports in various formats (CSV, JSON)
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';
import { logReportOperation, getRequestContext } from '@/lib/activity-logger';
import { adminService, safeAdminServiceCall, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - all staff roles can export reports
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);
    const context = getRequestContext(request.headers);

    const exportType = filters.type || 'students'; // students, leads, groups, activity
    const format = filters.format || 'csv'; // csv, json
    const startDate = filters.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const endDate = filters.endDate || new Date().toISOString().split('T')[0];

    try {
      let data: any[] = [];
      let filename = '';

      switch (exportType) {
        case 'students':
          data = await exportStudentsData(startDate, endDate, filters);
          filename = `students_export_${startDate}_to_${endDate}`;
          break;
        case 'leads':
          data = await exportLeadsData(startDate, endDate, filters);
          filename = `leads_export_${startDate}_to_${endDate}`;
          break;
        case 'groups':
          data = await exportGroupsData(filters);
          filename = `groups_export_${new Date().toISOString().split('T')[0]}`;
          break;
        case 'activity':
          data = await exportActivityData(startDate, endDate, filters);
          filename = `activity_export_${startDate}_to_${endDate}`;
          break;
        default:
          return createErrorResponse('Invalid export type', 400);
      }

      // Log export operation
      await logReportOperation(
        'EXPORT',
        authResult.user.id,
        `${exportType} export (${format}) - ${startDate} to ${endDate}`,
        context
      );

      if (format === 'csv') {
        const csv = convertToCSV(data);
        return new Response(csv, {
          headers: {
            'Content-Type': 'text/csv',
            'Content-Disposition': `attachment; filename="${filename}.csv"`,
          },
        });
      } else {
        return createResponse({
          exportType,
          format,
          dateRange: { startDate, endDate },
          data,
          exportedAt: new Date().toISOString(),
          recordCount: data.length
        }, true, 'Data exported successfully');
      }

    } catch (dbError) {
      console.error('Database error during export:', dbError);
      return createErrorResponse('Failed to export data', 500);
    }

  } catch (error) {
    console.error('Export error:', error);
    return createErrorResponse('Failed to export data', 500);
  }
}

/**
 * Export students data
 */
async function exportStudentsData(startDate: string, endDate: string, filters: any) {
  let whereClause = 'WHERE enrollment_date >= $1 AND enrollment_date <= $2';
  const params = [startDate, endDate];
  let paramIndex = 3;

  if (filters.status) {
    whereClause += ` AND status = $${paramIndex}`;
    params.push(filters.status);
    paramIndex++;
  }

  const result = await query(`
    SELECT 
      id,
      first_name as "First Name",
      last_name as "Last Name",
      email as "Email",
      phone as "Phone",
      TO_CHAR(date_of_birth, 'YYYY-MM-DD') as "Date of Birth",
      TO_CHAR(enrollment_date, 'YYYY-MM-DD') as "Enrollment Date",
      status as "Status",
      TO_CHAR(created_at, 'YYYY-MM-DD HH24:MI:SS') as "Created At"
    FROM students 
    ${whereClause}
    ORDER BY enrollment_date DESC
  `, params);

  return result.rows;
}

/**
 * Export leads data
 */
async function exportLeadsData(startDate: string, endDate: string, filters: any) {
  let whereClause = 'WHERE l.created_at >= $1 AND l.created_at <= $2';
  const params = [startDate, endDate];
  let paramIndex = 3;

  if (filters.status) {
    whereClause += ` AND l.status = $${paramIndex}`;
    params.push(filters.status);
    paramIndex++;
  }

  if (filters.source) {
    whereClause += ` AND l.source = $${paramIndex}`;
    params.push(filters.source);
    paramIndex++;
  }

  const result = await query(`
    SELECT 
      l.id,
      l.first_name as "First Name",
      l.last_name as "Last Name",
      l.email as "Email",
      l.phone as "Phone",
      l.source as "Source",
      l.status as "Status",
      u.name as "Assigned To",
      TO_CHAR(l.created_at, 'YYYY-MM-DD HH24:MI:SS') as "Created At",
      TO_CHAR(l.updated_at, 'YYYY-MM-DD HH24:MI:SS') as "Last Updated"
    FROM leads l
    LEFT JOIN users u ON l.assigned_to = u.id
    ${whereClause}
    ORDER BY l.created_at DESC
  `, params);

  return result.rows;
}

/**
 * Export groups data
 */
async function exportGroupsData(filters: any) {
  let whereClause = 'WHERE 1=1';
  const params: any[] = [];
  let paramIndex = 1;

  if (filters.isActive !== undefined) {
    whereClause += ` AND g.is_active = $${paramIndex}`;
    params.push(filters.isActive === 'true');
    paramIndex++;
  }

  if (filters.level) {
    whereClause += ` AND g.level = $${paramIndex}`;
    params.push(filters.level);
    paramIndex++;
  }

  const result = await query(`
    SELECT 
      g.id,
      g.name as "Group Name",
      g.level as "Level",
      u.name as "Teacher",
      g.max_students as "Max Students",
      COUNT(sg.student_id) as "Current Students",
      ROUND(COUNT(sg.student_id)::decimal / g.max_students * 100, 2) as "Utilization %",
      g.is_active as "Active",
      TO_CHAR(g.created_at, 'YYYY-MM-DD HH24:MI:SS') as "Created At"
    FROM groups g
    LEFT JOIN users u ON g.teacher_id = u.id
    LEFT JOIN student_groups sg ON g.id = sg.group_id
    ${whereClause}
    GROUP BY g.id, g.name, g.level, u.name, g.max_students, g.is_active, g.created_at
    ORDER BY g.name
  `, params);

  return result.rows;
}

/**
 * Export activity data
 */
async function exportActivityData(startDate: string, endDate: string, filters: any) {
  let whereClause = 'WHERE al.timestamp >= $1 AND al.timestamp <= $2';
  const params = [startDate, endDate];
  let paramIndex = 3;

  if (filters.action) {
    whereClause += ` AND al.action = $${paramIndex}`;
    params.push(filters.action);
    paramIndex++;
  }

  if (filters.resourceType) {
    whereClause += ` AND al.resource_type = $${paramIndex}`;
    params.push(filters.resourceType);
    paramIndex++;
  }

  if (filters.userId) {
    whereClause += ` AND al.user_id = $${paramIndex}`;
    params.push(filters.userId);
    paramIndex++;
  }

  const result = await query(`
    SELECT 
      al.id,
      u.name as "User",
      u.role as "User Role",
      al.action as "Action",
      al.resource_type as "Resource Type",
      al.resource_id as "Resource ID",
      al.description as "Description",
      al.ip_address as "IP Address",
      TO_CHAR(al.timestamp, 'YYYY-MM-DD HH24:MI:SS') as "Timestamp"
    FROM activity_logs al
    LEFT JOIN users u ON al.user_id = u.id
    ${whereClause}
    ORDER BY al.timestamp DESC
  `, params);

  return result.rows;
}

/**
 * Convert data array to CSV format
 */
function convertToCSV(data: any[]): string {
  if (data.length === 0) {
    return '';
  }

  const headers = Object.keys(data[0]);
  const csvHeaders = headers.join(',');
  
  const csvRows = data.map(row => {
    return headers.map(header => {
      const value = row[header];
      // Escape quotes and wrap in quotes if contains comma or quote
      if (value === null || value === undefined) {
        return '';
      }
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  });

  return [csvHeaders, ...csvRows].join('\n');
}

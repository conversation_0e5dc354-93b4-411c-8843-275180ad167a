/**
 * Staff dashboard endpoint
 * Provides overview statistics and metrics for staff operations
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - all staff roles can view dashboard
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get overview statistics
      const overviewSql = `
        WITH student_stats AS (
          SELECT 
            COUNT(*) as total_students,
            COUNT(*) FILTER (WHERE status = 'active') as active_students,
            COUNT(*) FILTER (WHERE enrollment_date >= CURRENT_DATE - INTERVAL '30 days') as new_students_month
          FROM students
        ),
        lead_stats AS (
          SELECT 
            COUNT(*) as total_leads,
            COUNT(*) FILTER (WHERE status = 'new') as new_leads,
            COUNT(*) FILTER (WHERE status = 'interested') as interested_leads,
            COUNT(*) FILTER (WHERE status = 'enrolled') as converted_leads
          FROM leads
        ),
        group_stats AS (
          SELECT 
            COUNT(*) as total_groups,
            COUNT(*) FILTER (WHERE is_active = true) as active_groups
          FROM groups
        ),
        activity_stats AS (
          SELECT COUNT(*) as recent_activity 
          FROM activity_logs 
          WHERE timestamp >= CURRENT_DATE - INTERVAL '7 days'
        )
        SELECT 
          s.total_students, s.active_students, s.new_students_month,
          l.total_leads, l.new_leads, l.interested_leads, l.converted_leads,
          g.total_groups, g.active_groups,
          a.recent_activity
        FROM student_stats s, lead_stats l, group_stats g, activity_stats a
      `;

      const overviewResult = await query(overviewSql);
      const overview = overviewResult.rows[0];

      // Get recent students (last 10)
      const recentStudentsResult = await query(
        `SELECT id, first_name, last_name, email, enrollment_date, status
         FROM students 
         ORDER BY created_at DESC 
         LIMIT 10`
      );

      // Get recent leads (last 10)
      const recentLeadsResult = await query(
        `SELECT l.id, l.first_name, l.last_name, l.email, l.status, l.created_at,
                u.name as assigned_to_name
         FROM leads l
         LEFT JOIN users u ON l.assigned_to = u.id
         ORDER BY l.created_at DESC
         LIMIT 10`
      );

      // Get lead conversion stats by month (last 6 months)
      const conversionStatsResult = await query(
        `SELECT 
           DATE_TRUNC('month', created_at) as month,
           COUNT(*) as total_leads,
           COUNT(*) FILTER (WHERE status = 'enrolled') as converted_leads
         FROM leads 
         WHERE created_at >= CURRENT_DATE - INTERVAL '6 months'
         GROUP BY DATE_TRUNC('month', created_at)
         ORDER BY month DESC`
      );

      // Get student enrollment by month (last 6 months)
      const enrollmentStatsResult = await query(
        `SELECT 
           DATE_TRUNC('month', enrollment_date) as month,
           COUNT(*) as enrollments
         FROM students 
         WHERE enrollment_date >= CURRENT_DATE - INTERVAL '6 months'
         GROUP BY DATE_TRUNC('month', enrollment_date)
         ORDER BY month DESC`
      );

      // Get groups with placeholder student counts (student_groups table not implemented yet)
      const groupStatsResult = await query(
        `SELECT g.id, g.name, g.level, g.max_students,
                u.name as teacher_name,
                0 as current_students
         FROM groups g
         LEFT JOIN users u ON g.teacher_id = u.id
         WHERE g.is_active = true
         ORDER BY g.name
         LIMIT 10`
      );

      return createResponse({
        overview: {
          totalStudents: parseInt(overview.total_students),
          activeStudents: parseInt(overview.active_students),
          newStudentsThisMonth: parseInt(overview.new_students_month),
          totalLeads: parseInt(overview.total_leads),
          newLeads: parseInt(overview.new_leads),
          interestedLeads: parseInt(overview.interested_leads),
          convertedLeads: parseInt(overview.converted_leads),
          totalGroups: parseInt(overview.total_groups),
          activeGroups: parseInt(overview.active_groups),
          recentActivity: parseInt(overview.recent_activity)
        },
        recentStudents: recentStudentsResult.rows,
        recentLeads: recentLeadsResult.rows,
        conversionStats: conversionStatsResult.rows,
        enrollmentStats: enrollmentStatsResult.rows,
        groupStats: groupStatsResult.rows
      }, true, 'Dashboard data retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving dashboard data:', dbError);
      return createErrorResponse('Failed to retrieve dashboard data', 500);
    }

  } catch (error) {
    console.error('Dashboard error:', error);
    return createErrorResponse('Failed to retrieve dashboard data', 500);
  }
}

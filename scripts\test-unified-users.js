/**
 * Test script for unified user management
 * Tests both admin and staff user creation through the unified API
 */

const { default: fetch } = require('node-fetch');

// Configuration
const ADMIN_SERVER_URL = 'http://localhost:3000';

// Test admin login first to get token
async function getAdminToken() {
  console.log('🔍 Getting admin token...');
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Admin login successful');
      return data.data.token;
    } else {
      console.error('❌ Admin login failed:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Admin login request failed:', error.message);
    return null;
  }
}

async function testFetchAllUsers(token) {
  console.log('\n🔍 Testing unified user fetch...');
  
  try {
    // Test admin users endpoint
    const adminResponse = await fetch(`${ADMIN_SERVER_URL}/api/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    
    const adminData = await adminResponse.json();
    console.log('Admin users response:', adminData.success ? 'Success' : 'Failed');
    if (adminData.success) {
      console.log(`  - Found ${adminData.data.users?.length || 0} admin users`);
    }
    
    // Test staff users endpoint
    const staffResponse = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    
    const staffData = await staffResponse.json();
    console.log('Staff users response:', staffData.success ? 'Success' : 'Failed');
    if (staffData.success) {
      console.log(`  - Found ${staffData.data.users?.length || 0} staff users`);
    }
    
    return { adminData, staffData };
  } catch (error) {
    console.error('❌ Fetch users failed:', error.message);
    return null;
  }
}

async function testCreateStaffUser(token) {
  console.log('\n🔍 Testing staff user creation...');
  
  const testUser = {
    email: '<EMAIL>',
    password: 'TestStaff123!',
    role: 'reception',
    name: 'Test Staff User',
    isActive: true,
    server_type: 'staff'
  };
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Staff user created successfully');
      console.log('  - User ID:', data.data.id);
      console.log('  - Name:', data.data.name);
      console.log('  - Role:', data.data.role);
      return data.data;
    } else {
      console.error('❌ Staff user creation failed:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Staff user creation request failed:', error.message);
    return null;
  }
}

async function testCreateAdminUser(token) {
  console.log('\n🔍 Testing admin user creation...');
  
  const testUser = {
    email: '<EMAIL>',
    password: 'TestAdmin123!',
    role: 'cashier',
    name: 'Test Admin User',
    isActive: true
  };
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testUser)
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Admin user created successfully');
      console.log('  - User ID:', data.data.id);
      console.log('  - Name:', data.data.name);
      console.log('  - Role:', data.data.role);
      return data.data;
    } else {
      console.error('❌ Admin user creation failed:', data.error);
      return null;
    }
  } catch (error) {
    console.error('❌ Admin user creation request failed:', error.message);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Unified User Management Tests');
  console.log('='.repeat(50));
  
  // Get admin token
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ Cannot proceed without admin token');
    return;
  }
  
  // Test fetching users
  const fetchResult = await testFetchAllUsers(token);
  
  // Test creating staff user
  const staffUser = await testCreateStaffUser(token);
  
  // Test creating admin user
  const adminUser = await testCreateAdminUser(token);
  
  // Final fetch to see all users
  console.log('\n🔍 Final user count check...');
  const finalFetch = await testFetchAllUsers(token);
  
  console.log('\n' + '='.repeat(50));
  if (staffUser && adminUser) {
    console.log('🎉 All tests passed! Unified user management is working.');
    console.log('✨ Both admin and staff users can be created and managed.');
  } else {
    console.log('❌ Some tests failed. Check the issues above.');
  }
  console.log('='.repeat(50));
}

runTests().catch(error => {
  console.error('❌ Test execution failed:', error);
});

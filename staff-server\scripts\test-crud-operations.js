/**
 * Comprehensive CRUD operations test script
 * Tests all CRUD operations for students, groups, leads, and users
 */

const fetch = require('node-fetch');
require('dotenv').config({ path: '.env.local' });

const STAFF_SERVER_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3003';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'testpassword123';

let authToken = null;
let testData = {
  students: [],
  groups: [],
  leads: [],
  users: []
};

// Helper function to make authenticated requests
async function makeRequest(endpoint, options = {}) {
  const url = `${STAFF_SERVER_URL}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers
  };

  if (authToken) {
    headers.Authorization = `Bearer ${authToken}`;
  }

  const response = await fetch(url, {
    ...options,
    headers
  });

  const data = await response.json();
  return { response, data };
}

// Test authentication
async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    const { response, data } = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: TEST_EMAIL,
        password: TEST_PASSWORD
      })
    });

    if (response.ok && data.success) {
      authToken = data.data.accessToken;
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.log('❌ Authentication failed:', data.message);
      return false;
    }
  } catch (error) {
    console.log('❌ Authentication error:', error.message);
    return false;
  }
}

// Test Students CRUD
async function testStudentsCRUD() {
  console.log('\n👨‍🎓 Testing Students CRUD...');

  // CREATE Student
  try {
    const { response, data } = await makeRequest('/api/students', {
      method: 'POST',
      body: JSON.stringify({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        dateOfBirth: '1995-01-15',
        address: '123 Main St',
        emergencyContact: 'Jane Doe - +1234567891',
        enrollmentDate: new Date().toISOString().split('T')[0]
      })
    });

    if (response.ok && data.success) {
      testData.students.push(data.data);
      console.log('✅ Student created successfully');
    } else {
      console.log('❌ Student creation failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Student creation error:', error.message);
  }

  // READ Students
  try {
    const { response, data } = await makeRequest('/api/students');
    
    if (response.ok && data.success) {
      console.log(`✅ Students list retrieved (${data.data.students.length} students)`);
    } else {
      console.log('❌ Students list retrieval failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Students list error:', error.message);
  }

  // UPDATE Student
  if (testData.students.length > 0) {
    try {
      const student = testData.students[0];
      const { response, data } = await makeRequest(`/api/students/${student.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          firstName: 'John Updated',
          phone: '+1234567899'
        })
      });

      if (response.ok && data.success) {
        console.log('✅ Student updated successfully');
      } else {
        console.log('❌ Student update failed:', data.message);
      }
    } catch (error) {
      console.log('❌ Student update error:', error.message);
    }
  }

  // Test Student-Group enrollment
  if (testData.students.length > 0 && testData.groups.length > 0) {
    try {
      const student = testData.students[0];
      const group = testData.groups[0];
      
      const { response, data } = await makeRequest(`/api/students/${student.id}/groups`, {
        method: 'POST',
        body: JSON.stringify({
          groupId: group.id
        })
      });

      if (response.ok && data.success) {
        console.log('✅ Student enrolled in group successfully');
      } else {
        console.log('❌ Student group enrollment failed:', data.message);
      }
    } catch (error) {
      console.log('❌ Student group enrollment error:', error.message);
    }
  }
}

// Test Groups CRUD
async function testGroupsCRUD() {
  console.log('\n👥 Testing Groups CRUD...');

  // CREATE Group
  try {
    const { response, data } = await makeRequest('/api/groups', {
      method: 'POST',
      body: JSON.stringify({
        name: 'Beginner English',
        level: 'A1',
        maxStudents: 10,
        schedule: 'Mon, Wed, Fri 10:00-11:30',
        isActive: true
      })
    });

    if (response.ok && data.success) {
      testData.groups.push(data.data);
      console.log('✅ Group created successfully');
    } else {
      console.log('❌ Group creation failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Group creation error:', error.message);
  }

  // READ Groups
  try {
    const { response, data } = await makeRequest('/api/groups');
    
    if (response.ok && data.success) {
      console.log(`✅ Groups list retrieved (${data.data.groups.length} groups)`);
    } else {
      console.log('❌ Groups list retrieval failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Groups list error:', error.message);
  }

  // UPDATE Group
  if (testData.groups.length > 0) {
    try {
      const group = testData.groups[0];
      const { response, data } = await makeRequest(`/api/groups/${group.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          name: 'Beginner English Updated',
          maxStudents: 12
        })
      });

      if (response.ok && data.success) {
        console.log('✅ Group updated successfully');
      } else {
        console.log('❌ Group update failed:', data.message);
      }
    } catch (error) {
      console.log('❌ Group update error:', error.message);
    }
  }
}

// Test Leads CRUD
async function testLeadsCRUD() {
  console.log('\n🎯 Testing Leads CRUD...');

  // CREATE Lead
  try {
    const { response, data } = await makeRequest('/api/leads', {
      method: 'POST',
      body: JSON.stringify({
        firstName: 'Alice',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '+1234567892',
        source: 'website',
        status: 'new',
        notes: 'Interested in English courses'
      })
    });

    if (response.ok && data.success) {
      testData.leads.push(data.data);
      console.log('✅ Lead created successfully');
    } else {
      console.log('❌ Lead creation failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Lead creation error:', error.message);
  }

  // READ Leads
  try {
    const { response, data } = await makeRequest('/api/leads');
    
    if (response.ok && data.success) {
      console.log(`✅ Leads list retrieved (${data.data.leads.length} leads)`);
    } else {
      console.log('❌ Leads list retrieval failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Leads list error:', error.message);
  }

  // UPDATE Lead
  if (testData.leads.length > 0) {
    try {
      const lead = testData.leads[0];
      const { response, data } = await makeRequest(`/api/leads/${lead.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          status: 'contacted',
          notes: 'Called and scheduled a meeting'
        })
      });

      if (response.ok && data.success) {
        console.log('✅ Lead updated successfully');
      } else {
        console.log('❌ Lead update failed:', data.message);
      }
    } catch (error) {
      console.log('❌ Lead update error:', error.message);
    }
  }

  // Test Lead Conversion
  if (testData.leads.length > 0) {
    try {
      const lead = testData.leads[0];
      const { response, data } = await makeRequest(`/api/leads/${lead.id}/convert`, {
        method: 'POST',
        body: JSON.stringify({
          enrollmentDate: new Date().toISOString().split('T')[0],
          address: '456 Oak St',
          emergencyContact: 'Bob Smith - +1234567893'
        })
      });

      if (response.ok && data.success) {
        console.log('✅ Lead converted to student successfully');
      } else {
        console.log('❌ Lead conversion failed:', data.message);
      }
    } catch (error) {
      console.log('❌ Lead conversion error:', error.message);
    }
  }
}

// Test Users CRUD
async function testUsersCRUD() {
  console.log('\n👤 Testing Users CRUD...');

  // READ Users
  try {
    const { response, data } = await makeRequest('/api/users');
    
    if (response.ok && data.success) {
      console.log(`✅ Users list retrieved (${data.data.users.length} users)`);
    } else {
      console.log('❌ Users list retrieval failed:', data.message);
    }
  } catch (error) {
    console.log('❌ Users list error:', error.message);
  }

  // Test User Creation (should redirect to admin server)
  try {
    const { response, data } = await makeRequest('/api/users', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        role: 'teacher',
        name: 'New Teacher'
      })
    });

    if (response.status === 400 && data.message.includes('admin server')) {
      console.log('✅ User creation correctly redirects to admin server');
    } else {
      console.log('❌ User creation should redirect to admin server');
    }
  } catch (error) {
    console.log('❌ User creation test error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🚀 Starting CRUD Operations Test Suite...');
  console.log(`Testing against: ${STAFF_SERVER_URL}`);

  // Test authentication first
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    console.log('\n❌ Cannot proceed without authentication');
    return;
  }

  // Run all CRUD tests
  await testGroupsCRUD();  // Create groups first for student enrollment tests
  await testStudentsCRUD();
  await testLeadsCRUD();
  await testUsersCRUD();

  console.log('\n🎉 CRUD Operations Test Suite Completed!');
  console.log('\nTest Data Created:');
  console.log(`- Students: ${testData.students.length}`);
  console.log(`- Groups: ${testData.groups.length}`);
  console.log(`- Leads: ${testData.leads.length}`);
  console.log(`- Users: ${testData.users.length}`);
}

// Run the tests
runTests().catch(console.error);

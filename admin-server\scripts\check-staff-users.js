/**
 * Check staff users in admin server database
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function checkStaffUsers() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('🔍 Checking Staff Users in Admin Server...');
    
    await client.connect();
    console.log('📡 Database connected successfully');

    // Check all users
    const allUsers = await client.query(`
      SELECT id, email, role, name, is_active, server_type, created_at
      FROM users 
      ORDER BY server_type, role, created_at DESC
    `);

    console.log('\n👥 All users in admin server:');
    allUsers.rows.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.role}) - ${user.server_type} - Active: ${user.is_active}`);
      console.log(`      ID: ${user.id}`);
    });

    // Check specifically staff users
    const staffUsers = await client.query(`
      SELECT id, email, role, name, is_active, server_type, created_at
      FROM users 
      WHERE server_type = 'staff'
      ORDER BY created_at DESC
    `);

    console.log('\n🏢 Staff users specifically:');
    if (staffUsers.rows.length === 0) {
      console.log('  ⚠️  No staff users found!');
    } else {
      staffUsers.rows.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.email} (${user.role}) - Active: ${user.is_active}`);
        console.log(`      ID: ${user.id}`);
        console.log(`      Name: ${user.name}`);
        console.log(`      Created: ${user.created_at}`);
      });
    }

    // Check admin users
    const adminUsers = await client.query(`
      SELECT id, email, role, name, is_active, server_type, created_at
      FROM users 
      WHERE server_type = 'admin' OR server_type IS NULL
      ORDER BY created_at DESC
    `);

    console.log('\n🔧 Admin users:');
    adminUsers.rows.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email} (${user.role}) - Active: ${user.is_active}`);
      console.log(`      ID: ${user.id}`);
    });

    console.log('\n📊 Summary:');
    console.log(`  Total users: ${allUsers.rows.length}`);
    console.log(`  Staff users: ${staffUsers.rows.length}`);
    console.log(`  Admin users: ${adminUsers.rows.length}`);

    console.log('\n🎉 Staff users check completed!');

  } catch (error) {
    console.error('❌ Staff users check failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

// Run the check
checkStaffUsers();

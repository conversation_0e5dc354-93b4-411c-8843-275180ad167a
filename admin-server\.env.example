# ===========================================
# ADMIN SERVER ENVIRONMENT CONFIGURATION
# ===========================================
# Copy this file to .env.local for local development
# For Vercel deployment, set these in the Vercel dashboard

# Database Configuration
# Use your production PostgreSQL database URL
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Authentication
# Generate secure random strings for production
JWT_SECRET=your-secure-jwt-secret-here
NEXTAUTH_SECRET=your-secure-nextauth-secret-here

# App Configuration
# Set to your production domain
NEXT_PUBLIC_APP_URL=https://your-admin-domain.vercel.app
NODE_ENV=production

# Activity Logging
ENABLE_ACTIVITY_LOGGING=true
LOG_RETENTION_DAYS=365

# Security
BCRYPT_ROUNDS=12
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Staff Server Integration
# Must match the API key in staff server
STAFF_SERVER_API_KEY=your-secure-api-key-here
# Set to your staff server production URL
STAFF_SERVER_URL=https://your-staff-domain.vercel.app
AUTO_CREATE_INVOICE=true
DEFAULT_MONTHLY_FEE=100

# Optional: Payment Integration
STRIPE_SECRET_KEY=sk_live_...
STRIPE_PUBLISHABLE_KEY=pk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Optional: Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

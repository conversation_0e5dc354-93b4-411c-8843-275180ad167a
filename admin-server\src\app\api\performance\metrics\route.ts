/**
 * Performance Metrics API Endpoint for Admin Server
 * Provides system performance data and optimization insights
 */

import { NextRequest, NextResponse } from 'next/server';
import { PerformanceMonitor, cache, ConnectionPool, RateLimiter } from '@/lib/performance-optimizer';
import { debugLogger } from '@/lib/debug-logger';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const reset = searchParams.get('reset') === 'true';

    // Reset metrics if requested
    if (reset) {
      PerformanceMonitor.clearMetrics();
      cache.clear();
      debugLogger.info('PERFORMANCE', 'Performance metrics and cache reset');
    }

    // Get basic performance metrics
    const performanceMetrics = PerformanceMonitor.getMetrics();
    const cacheStats = cache.getStats();
    const connectionStats = ConnectionPool.getConnectionStats();

    // System health indicators
    const systemHealth = {
      status: 'healthy',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      timestamp: new Date().toISOString()
    };

    // Basic response
    const response: any = {
      success: true,
      data: {
        performance: performanceMetrics,
        cache: cacheStats,
        connections: connectionStats,
        system: systemHealth
      },
      timestamp: new Date().toISOString()
    };

    // Add detailed metrics if requested
    if (detailed) {
      response.data.detailed = {
        slowestOperations: Object.entries(performanceMetrics)
          .sort(([,a], [,b]) => (b as any).avgTime - (a as any).avgTime)
          .slice(0, 10)
          .map(([operation, metrics]) => ({ operation, metrics })),
        
        mostFrequentOperations: Object.entries(performanceMetrics)
          .sort(([,a], [,b]) => (b as any).count - (a as any).count)
          .slice(0, 10)
          .map(([operation, metrics]) => ({ operation, metrics })),

        systemResources: {
          memoryUsage: {
            rss: Math.round(systemHealth.memory.rss / 1024 / 1024 * 100) / 100, // MB
            heapTotal: Math.round(systemHealth.memory.heapTotal / 1024 / 1024 * 100) / 100, // MB
            heapUsed: Math.round(systemHealth.memory.heapUsed / 1024 / 1024 * 100) / 100, // MB
            external: Math.round(systemHealth.memory.external / 1024 / 1024 * 100) / 100 // MB
          },
          cpuUsage: {
            user: systemHealth.cpu.user,
            system: systemHealth.cpu.system
          },
          uptime: {
            seconds: Math.round(systemHealth.uptime),
            formatted: this.formatUptime(systemHealth.uptime)
          }
        },

        recommendations: this.generateRecommendations(performanceMetrics, cacheStats, systemHealth)
      };
    }

    return NextResponse.json(response);

  } catch (error) {
    debugLogger.error('PERFORMANCE_API', 'Failed to retrieve performance metrics', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve performance metrics',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }

  private static formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${secs}s`;
  }

  private static generateRecommendations(
    performanceMetrics: any, 
    cacheStats: any, 
    systemHealth: any
  ): string[] {
    const recommendations: string[] = [];

    // Check for slow operations
    const slowOperations = Object.entries(performanceMetrics)
      .filter(([, metrics]: [string, any]) => metrics.avgTime > 1000);
    
    if (slowOperations.length > 0) {
      recommendations.push(`${slowOperations.length} operations are taking over 1 second on average. Consider optimization.`);
    }

    // Check cache efficiency
    const cacheHitRate = cacheStats.active / (cacheStats.total || 1);
    if (cacheHitRate < 0.7) {
      recommendations.push('Cache hit rate is below 70%. Consider increasing cache TTL or reviewing cache strategy.');
    }

    // Check memory usage
    const memoryUsagePercent = systemHealth.memory.heapUsed / systemHealth.memory.heapTotal;
    if (memoryUsagePercent > 0.8) {
      recommendations.push('Memory usage is above 80%. Consider increasing heap size or optimizing memory usage.');
    }

    // Check for high frequency operations
    const highFreqOperations = Object.entries(performanceMetrics)
      .filter(([, metrics]: [string, any]) => metrics.count > 1000);
    
    if (highFreqOperations.length > 0) {
      recommendations.push(`${highFreqOperations.length} operations have high frequency. Consider caching or rate limiting.`);
    }

    if (recommendations.length === 0) {
      recommendations.push('System performance looks good! No immediate optimizations needed.');
    }

    return recommendations;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, target } = body;

    let result: any = {};

    switch (action) {
      case 'clear_cache':
        cache.clear();
        result = { message: 'Cache cleared successfully' };
        break;

      case 'clear_metrics':
        PerformanceMonitor.clearMetrics();
        result = { message: 'Performance metrics cleared successfully' };
        break;

      case 'force_gc':
        if (global.gc) {
          global.gc();
          result = { message: 'Garbage collection forced successfully' };
        } else {
          result = { message: 'Garbage collection not available (run with --expose-gc)' };
        }
        break;

      case 'test_performance':
        // Run a performance test
        const testResults = await this.runPerformanceTest();
        result = { message: 'Performance test completed', results: testResults };
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action specified',
          timestamp: new Date().toISOString()
        }, { status: 400 });
    }

    debugLogger.info('PERFORMANCE_API', `Performance action executed: ${action}`, result);

    return NextResponse.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    debugLogger.error('PERFORMANCE_API', 'Failed to execute performance action', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to execute performance action',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }

  private static async runPerformanceTest(): Promise<any> {
    const testResults: any = {};

    // Test cache performance
    const cacheTestStart = Date.now();
    for (let i = 0; i < 1000; i++) {
      cache.set(`test_${i}`, { data: `test_data_${i}` }, 60);
    }
    for (let i = 0; i < 1000; i++) {
      cache.get(`test_${i}`);
    }
    testResults.cacheTest = {
      duration: Date.now() - cacheTestStart,
      operations: 2000,
      opsPerSecond: Math.round(2000 / ((Date.now() - cacheTestStart) / 1000))
    };

    // Test JSON serialization performance
    const jsonTestStart = Date.now();
    const testObject = { 
      id: 'test', 
      data: Array.from({ length: 1000 }, (_, i) => ({ id: i, value: `test_${i}` }))
    };
    for (let i = 0; i < 100; i++) {
      JSON.stringify(testObject);
      JSON.parse(JSON.stringify(testObject));
    }
    testResults.jsonTest = {
      duration: Date.now() - jsonTestStart,
      operations: 200,
      opsPerSecond: Math.round(200 / ((Date.now() - jsonTestStart) / 1000))
    };

    // Clean up test cache entries
    for (let i = 0; i < 1000; i++) {
      cache.delete(`test_${i}`);
    }

    return testResults;
  }
}

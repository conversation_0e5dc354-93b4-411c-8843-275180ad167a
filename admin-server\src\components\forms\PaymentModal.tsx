/**
 * Payment Modal Component
 * Modal form for recording new payments with student search
 */

'use client';

import React, { useState, useEffect } from 'react';
import { PaymentType, PaymentMethod } from '@/types';
import { PAYMENT_CONFIG } from '@shared/utils/constants';

interface Student {
  id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  paymentStatus: 'paid' | 'unpaid' | 'debt';
  debtAmount: number;
  lastPaymentDate?: string;
}

interface PaymentFormData {
  studentId: string;
  studentName: string;
  amount: string;
  paymentMethod: PaymentMethod;
  description: string;
  startDate: string;
  endDate: string;
  debtAmount: string;
  isDebtPayment: boolean;
}

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: PaymentFormData) => Promise<void>;
  isLoading?: boolean;
}

export default function PaymentModal({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false
}: PaymentModalProps) {
  const [formData, setFormData] = useState<PaymentFormData>({
    studentId: '',
    studentName: '',
    amount: '',
    paymentMethod: PaymentMethod.CASH,
    description: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
    debtAmount: '0',
    isDebtPayment: false
  });

  const [students, setStudents] = useState<Student[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [showStudentDropdown, setShowStudentDropdown] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [searchLoading, setSearchLoading] = useState(false);

  // Search students when query changes
  useEffect(() => {
    const searchStudents = async () => {
      if (searchQuery.length < 2) {
        setStudents([]);
        return;
      }

      setSearchLoading(true);
      try {
        const response = await fetch(`/api/students/search?search=${encodeURIComponent(searchQuery)}&limit=10`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const result = await response.json();
          setStudents(result.data.students || []);
        }
      } catch (error) {
        console.error('Error searching students:', error);
      } finally {
        setSearchLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchStudents, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery]);

  const handleStudentSelect = (student: Student) => {
    setSelectedStudent(student);
    setFormData(prev => ({
      ...prev,
      studentId: student.id,
      studentName: `${student.firstName} ${student.lastName}`,
      debtAmount: student.debtAmount.toString()
    }));
    setSearchQuery(`${student.firstName} ${student.lastName}`);
    setShowStudentDropdown(false);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate student selection
    if (!formData.studentId) {
      newErrors.student = 'Please select a student';
    }

    // Validate amount
    if (!formData.amount.trim()) {
      newErrors.amount = 'Amount is required';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Amount must be a positive number';
      } else if (amount < PAYMENT_CONFIG.MIN_AMOUNT) {
        newErrors.amount = `Amount must be at least ${PAYMENT_CONFIG.MIN_AMOUNT} ${PAYMENT_CONFIG.CURRENCY}`;
      } else if (amount > PAYMENT_CONFIG.MAX_AMOUNT) {
        newErrors.amount = `Amount cannot exceed ${PAYMENT_CONFIG.MAX_AMOUNT} ${PAYMENT_CONFIG.CURRENCY}`;
      }
    }

    // Validate dates
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    if (!formData.endDate) {
      newErrors.endDate = 'End date is required';
    }
    if (formData.startDate && formData.endDate && new Date(formData.startDate) >= new Date(formData.endDate)) {
      newErrors.endDate = 'End date must be after start date';
    }

    // Validate debt amount if debt payment is selected
    if (formData.isDebtPayment && formData.debtAmount) {
      const debtAmount = parseFloat(formData.debtAmount);
      if (isNaN(debtAmount) || debtAmount < 0) {
        newErrors.debtAmount = 'Debt amount must be a valid number';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form on successful submission
      setFormData({
        studentId: '',
        studentName: '',
        amount: '',
        paymentMethod: PaymentMethod.CASH,
        description: '',
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        debtAmount: '0',
        isDebtPayment: false
      });
      setSelectedStudent(null);
      setSearchQuery('');
      onClose();
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  const handleClose = () => {
    setFormData({
      studentId: '',
      studentName: '',
      amount: '',
      paymentMethod: PaymentMethod.CASH,
      description: '',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      debtAmount: '0',
      isDebtPayment: false
    });
    setSelectedStudent(null);
    setSearchQuery('');
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">Record New Payment</h2>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Student Search */}
          <div className="relative">
            <label htmlFor="student" className="block text-sm font-medium text-gray-700 mb-1">
              Student *
            </label>
            <input
              type="text"
              id="student"
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setShowStudentDropdown(true);
              }}
              onFocus={() => setShowStudentDropdown(true)}
              placeholder="Search student by name..."
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.student ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={isLoading}
            />
            {errors.student && (
              <p className="mt-1 text-sm text-red-600">{errors.student}</p>
            )}

            {/* Student Dropdown */}
            {showStudentDropdown && (searchLoading || students.length > 0) && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                {searchLoading ? (
                  <div className="px-3 py-2 text-gray-500">Searching...</div>
                ) : (
                  students.map((student) => (
                    <button
                      key={student.id}
                      type="button"
                      onClick={() => handleStudentSelect(student)}
                      className="w-full px-3 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    >
                      <div className="font-medium">{student.firstName} {student.lastName}</div>
                      <div className="text-sm text-gray-500">
                        {student.phone && `${student.phone} • `}
                        Status: {student.paymentStatus}
                        {student.debtAmount > 0 && ` • Debt: ${student.debtAmount} ${PAYMENT_CONFIG.CURRENCY}`}
                      </div>
                    </button>
                  ))
                )}
              </div>
            )}
          </div>

          {/* Selected Student Info */}
          {selectedStudent && (
            <div className="bg-blue-50 p-3 rounded-md">
              <div className="text-sm">
                <strong>{selectedStudent.firstName} {selectedStudent.lastName}</strong>
                {selectedStudent.phone && <span className="text-gray-600"> • {selectedStudent.phone}</span>}
                <br />
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${
                  selectedStudent.paymentStatus === 'paid' ? 'bg-green-100 text-green-800' :
                  selectedStudent.paymentStatus === 'debt' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedStudent.paymentStatus}
                </span>
                {selectedStudent.debtAmount > 0 && (
                  <span className="ml-2 text-red-600 font-medium">
                    Debt: {selectedStudent.debtAmount} {PAYMENT_CONFIG.CURRENCY}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Amount */}
          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
              Amount ({PAYMENT_CONFIG.CURRENCY}) *
            </label>
            <input
              type="number"
              id="amount"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0"
              min={PAYMENT_CONFIG.MIN_AMOUNT}
              max={PAYMENT_CONFIG.MAX_AMOUNT}
              step={PAYMENT_CONFIG.DECIMAL_PLACES === 0 ? "1" : "0.01"}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.amount ? 'border-red-500' : 'border-gray-300'
              }`}
              disabled={isLoading}
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount}</p>
            )}
          </div>

          {/* Payment Method */}
          <div>
            <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 mb-1">
              Payment Method *
            </label>
            <select
              id="paymentMethod"
              value={formData.paymentMethod}
              onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value as PaymentMethod }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              <option value={PaymentMethod.CASH}>Cash</option>
              <option value={PaymentMethod.CARD}>Card</option>
            </select>
          </div>

          {/* Date Range */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date *
              </label>
              <input
                type="date"
                id="startDate"
                value={formData.startDate}
                onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.startDate ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>
              )}
            </div>

            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                End Date *
              </label>
              <input
                type="date"
                id="endDate"
                value={formData.endDate}
                onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.endDate ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>
              )}
            </div>
          </div>

          {/* Debt Payment Checkbox */}
          {selectedStudent && selectedStudent.debtAmount > 0 && (
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.isDebtPayment}
                  onChange={(e) => setFormData(prev => ({ ...prev, isDebtPayment: e.target.checked }))}
                  className="mr-2"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-700">
                  Include debt payment ({selectedStudent.debtAmount} {PAYMENT_CONFIG.CURRENCY})
                </span>
              </label>
            </div>
          )}

          {/* Debt Amount (if debt payment is selected) */}
          {formData.isDebtPayment && (
            <div>
              <label htmlFor="debtAmount" className="block text-sm font-medium text-gray-700 mb-1">
                Debt Amount ({PAYMENT_CONFIG.CURRENCY})
              </label>
              <input
                type="number"
                id="debtAmount"
                value={formData.debtAmount}
                onChange={(e) => setFormData(prev => ({ ...prev, debtAmount: e.target.value }))}
                placeholder="0"
                min="0"
                step={PAYMENT_CONFIG.DECIMAL_PLACES === 0 ? "1" : "0.01"}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.debtAmount ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={isLoading}
              />
              {errors.debtAmount && (
                <p className="mt-1 text-sm text-red-600">{errors.debtAmount}</p>
              )}
            </div>
          )}

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Optional description or notes"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              disabled={isLoading}
            >
              {isLoading ? 'Recording...' : 'Record Payment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

/**
 * Vercel Deployment Verification Script
 * Tests the deployed Innovative Centre Platform on Vercel
 */

const https = require('https');
const http = require('http');

// Configuration
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'https://innovative-centre-admin.vercel.app';
const STAFF_SERVER_URL = process.env.STAFF_SERVER_URL || 'https://innovative-centre-staff.vercel.app';
const API_KEY = process.env.VERIFICATION_API_KEY || 'test-key';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;
    const req = protocol.request(url, options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData, headers: res.headers });
        } catch (e) {
          resolve({ status: res.statusCode, data: data, headers: res.headers });
        }
      });
    });
    
    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testHealthEndpoint(serverName, url) {
  log(`\n🔍 Testing ${serverName} Health Endpoint...`, 'blue');
  
  try {
    const response = await makeRequest(`${url}/api/health`);
    
    if (response.status === 200) {
      log(`✅ ${serverName} health check passed`, 'green');
      log(`   Status: ${response.data.status}`, 'green');
      log(`   Timestamp: ${response.data.timestamp}`, 'green');
      return true;
    } else {
      log(`❌ ${serverName} health check failed: HTTP ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${serverName} health check error: ${error.message}`, 'red');
    return false;
  }
}

async function testDatabaseConnection(serverName, url) {
  log(`\n🗄️ Testing ${serverName} Database Connection...`, 'blue');
  
  try {
    const response = await makeRequest(`${url}/api/health/database`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 200 && response.data.connected) {
      log(`✅ ${serverName} database connection successful`, 'green');
      return true;
    } else {
      log(`❌ ${serverName} database connection failed`, 'red');
      return false;
    }
  } catch (error) {
    log(`⚠️ ${serverName} database test skipped: ${error.message}`, 'yellow');
    return null;
  }
}

async function testInterServiceCommunication() {
  log(`\n🔗 Testing Inter-Service Communication...`, 'blue');
  
  try {
    // Test staff server's admin integration status
    const response = await makeRequest(`${STAFF_SERVER_URL}/api/admin-integration/status`, {
      method: 'GET',
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.status === 200) {
      const data = response.data;
      log(`✅ Inter-service communication configured`, 'green');
      log(`   Integration Enabled: ${data.integrationEnabled}`, 'green');
      log(`   Admin Server URL: ${data.configuration?.adminServerUrl}`, 'green');
      log(`   API Key Configured: ${data.configuration?.apiKeyConfigured}`, 'green');
      return true;
    } else {
      log(`❌ Inter-service communication test failed: HTTP ${response.status}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ Inter-service communication error: ${error.message}`, 'red');
    return false;
  }
}

async function testEnvironmentConfiguration() {
  log(`\n⚙️ Testing Environment Configuration...`, 'blue');
  
  const tests = [
    { name: 'Admin Server URL', value: ADMIN_SERVER_URL, required: true },
    { name: 'Staff Server URL', value: STAFF_SERVER_URL, required: true },
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    if (test.required && !test.value) {
      log(`❌ ${test.name} not configured`, 'red');
      allPassed = false;
    } else if (test.value) {
      log(`✅ ${test.name}: ${test.value}`, 'green');
    }
  }
  
  // Test HTTPS in production
  if (ADMIN_SERVER_URL.startsWith('https://') && STAFF_SERVER_URL.startsWith('https://')) {
    log(`✅ Both servers using HTTPS`, 'green');
  } else {
    log(`⚠️ Servers should use HTTPS in production`, 'yellow');
  }
  
  return allPassed;
}

async function testCORSConfiguration() {
  log(`\n🌐 Testing CORS Configuration...`, 'blue');
  
  try {
    // Test preflight request
    const response = await makeRequest(`${ADMIN_SERVER_URL}/api/health`, {
      method: 'OPTIONS',
      headers: {
        'Origin': STAFF_SERVER_URL,
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'X-API-Key'
      }
    });
    
    const corsHeaders = response.headers['access-control-allow-origin'];
    if (corsHeaders) {
      log(`✅ CORS headers present`, 'green');
      log(`   Access-Control-Allow-Origin: ${corsHeaders}`, 'green');
      return true;
    } else {
      log(`⚠️ CORS headers not found (may be configured differently)`, 'yellow');
      return null;
    }
  } catch (error) {
    log(`⚠️ CORS test skipped: ${error.message}`, 'yellow');
    return null;
  }
}

async function generateReport(results) {
  log(`\n📊 Deployment Verification Report`, 'bold');
  log(`${'='.repeat(50)}`, 'blue');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(r => r === true).length;
  const failedTests = Object.values(results).filter(r => r === false).length;
  const skippedTests = Object.values(results).filter(r => r === null).length;
  
  log(`\nTest Results:`, 'bold');
  log(`✅ Passed: ${passedTests}`, 'green');
  log(`❌ Failed: ${failedTests}`, 'red');
  log(`⚠️ Skipped: ${skippedTests}`, 'yellow');
  log(`📊 Total: ${totalTests}`, 'blue');
  
  const successRate = Math.round((passedTests / (totalTests - skippedTests)) * 100);
  log(`\nSuccess Rate: ${successRate}%`, successRate >= 80 ? 'green' : 'red');
  
  if (failedTests === 0) {
    log(`\n🎉 All critical tests passed! Your deployment is ready for production.`, 'green');
  } else {
    log(`\n⚠️ Some tests failed. Please review the issues above before going live.`, 'yellow');
  }
  
  log(`\nNext Steps:`, 'bold');
  log(`1. Review any failed tests and fix configuration issues`);
  log(`2. Test user authentication and core functionality`);
  log(`3. Set up monitoring and alerts`);
  log(`4. Configure custom domains if needed`);
  log(`5. Set up backup and disaster recovery procedures`);
}

async function main() {
  log(`🚀 Vercel Deployment Verification`, 'bold');
  log(`${'='.repeat(50)}`, 'blue');
  log(`Admin Server: ${ADMIN_SERVER_URL}`);
  log(`Staff Server: ${STAFF_SERVER_URL}`);
  
  const results = {};
  
  // Run all tests
  results.adminHealth = await testHealthEndpoint('Admin Server', ADMIN_SERVER_URL);
  results.staffHealth = await testHealthEndpoint('Staff Server', STAFF_SERVER_URL);
  results.adminDatabase = await testDatabaseConnection('Admin Server', ADMIN_SERVER_URL);
  results.staffDatabase = await testDatabaseConnection('Staff Server', STAFF_SERVER_URL);
  results.interService = await testInterServiceCommunication();
  results.environment = await testEnvironmentConfiguration();
  results.cors = await testCORSConfiguration();
  
  // Generate final report
  await generateReport(results);
}

// Run the verification
main().catch(error => {
  log(`\n💥 Verification script failed: ${error.message}`, 'red');
  process.exit(1);
});

/**
 * Simple test for staff user creation
 */

const { default: fetch } = require('node-fetch');

const ADMIN_SERVER_URL = 'http://localhost:3000';

async function getAdminToken() {
  const response = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Admin123!'
    })
  });
  
  const data = await response.json();
  return data.success ? data.data.token : null;
}

async function testStaffCreate() {
  console.log('🔍 Testing staff user creation...');
  
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ No admin token');
    return;
  }
  
  const testUser = {
    email: '<EMAIL>',
    password: 'Test123!',
    role: 'reception',
    name: 'New Staff User',
    isActive: true
  };
  
  console.log('Sending request with data:', JSON.stringify(testUser, null, 2));
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response data:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Request error:', error.message);
  }
}

testStaffCreate();

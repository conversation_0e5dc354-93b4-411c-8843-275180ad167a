/**
 * System Monitoring Dashboard API for Admin Server
 * Provides comprehensive system health and monitoring data
 */

import { NextRequest, NextResponse } from 'next/server';
import { debugLogger } from '@/lib/debug-logger';
import { PerformanceMonitor, cache } from '@/lib/performance-optimizer';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '1h'; // 1h, 6h, 24h, 7d
    const includeDetails = searchParams.get('details') === 'true';

    // System overview
    const systemOverview = {
      status: 'operational',
      uptime: process.uptime(),
      version: process.version,
      platform: process.platform,
      arch: process.arch,
      timestamp: new Date().toISOString()
    };

    // Server health metrics
    const serverHealth = {
      admin: await this.checkServerHealth('http://localhost:3000'),
      staff: await this.checkServerHealth('http://localhost:3003')
    };

    // Database health
    const databaseHealth = await this.checkDatabaseHealth();

    // Interserver communication health
    const interserverHealth = await this.checkInterserverHealth();

    // Recent activity summary
    const activitySummary = await this.getActivitySummary(timeRange);

    // User activity metrics
    const userMetrics = await this.getUserMetrics(timeRange);

    // System alerts
    const alerts = await this.getSystemAlerts();

    // Performance summary
    const performanceSummary = this.getPerformanceSummary();

    const dashboardData = {
      overview: systemOverview,
      health: {
        servers: serverHealth,
        database: databaseHealth,
        interserver: interserverHealth
      },
      activity: activitySummary,
      users: userMetrics,
      performance: performanceSummary,
      alerts,
      lastUpdated: new Date().toISOString()
    };

    if (includeDetails) {
      dashboardData.details = {
        recentLogs: debugLogger.getRecentLogs(20),
        cacheStats: cache.getStats(),
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      };
    }

    return NextResponse.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    debugLogger.error('MONITORING', 'Failed to generate dashboard data', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate dashboard data',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }

  private static async checkServerHealth(url: string): Promise<any> {
    try {
      const startTime = Date.now();
      const response = await fetch(`${url}/api/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      const isHealthy = response.ok;

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        statusCode: response.status,
        url,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unreachable',
        responseTime: null,
        statusCode: null,
        url,
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  private static async checkDatabaseHealth(): Promise<any> {
    try {
      // Simplified database health check
      // In a real implementation, you'd check actual database connections
      const startTime = Date.now();
      
      // Simulate database query
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const responseTime = Date.now() - startTime;

      return {
        admin: {
          status: 'connected',
          responseTime,
          connectionPool: {
            active: 5,
            idle: 3,
            total: 8
          },
          lastChecked: new Date().toISOString()
        },
        staff: {
          status: 'connected',
          responseTime: responseTime + 5,
          connectionPool: {
            active: 3,
            idle: 2,
            total: 5
          },
          lastChecked: new Date().toISOString()
        }
      };
    } catch (error) {
      return {
        admin: { status: 'error', error: error.message },
        staff: { status: 'error', error: error.message }
      };
    }
  }

  private static async checkInterserverHealth(): Promise<any> {
    try {
      const apiKey = process.env.STAFF_SERVER_API_KEY;
      if (!apiKey) {
        return {
          status: 'misconfigured',
          error: 'API key not configured'
        };
      }

      const startTime = Date.now();
      const testResponse = await fetch('http://localhost:3000/api/staff-integration/activity-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'X-Source-Service': 'staff-server'
        },
        body: JSON.stringify({
          userId: 'health-check-user',
          action: 'HEALTH_CHECK',
          resourceType: 'SYSTEM',
          description: 'Interserver health check',
          sourceService: 'staff-server',
          timestamp: new Date().toISOString()
        })
      });

      const responseTime = Date.now() - startTime;

      return {
        status: testResponse.ok ? 'healthy' : 'unhealthy',
        responseTime,
        statusCode: testResponse.status,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  private static async getActivitySummary(timeRange: string): Promise<any> {
    // Simplified activity summary
    // In a real implementation, you'd query the activity_logs table
    
    const now = new Date();
    const ranges = {
      '1h': 1,
      '6h': 6,
      '24h': 24,
      '7d': 168
    };
    
    const hoursBack = ranges[timeRange] || 1;
    const startTime = new Date(now.getTime() - (hoursBack * 60 * 60 * 1000));

    return {
      timeRange,
      startTime: startTime.toISOString(),
      endTime: now.toISOString(),
      totalActivities: Math.floor(Math.random() * 100) + 50, // Simulated
      byAction: {
        CREATE: Math.floor(Math.random() * 30) + 10,
        UPDATE: Math.floor(Math.random() * 20) + 5,
        DELETE: Math.floor(Math.random() * 5) + 1,
        LOGIN: Math.floor(Math.random() * 40) + 15
      },
      byServer: {
        admin: Math.floor(Math.random() * 30) + 10,
        staff: Math.floor(Math.random() * 50) + 20
      },
      topUsers: [
        { userId: 'user-1', name: 'Reception User', activities: 25 },
        { userId: 'user-2', name: 'Admin User', activities: 18 },
        { userId: 'user-3', name: 'Manager User', activities: 12 }
      ]
    };
  }

  private static async getUserMetrics(timeRange: string): Promise<any> {
    // Simplified user metrics
    return {
      total: {
        admin: 4,
        staff: 9,
        active: 8,
        inactive: 5
      },
      online: {
        current: Math.floor(Math.random() * 5) + 2,
        peak24h: Math.floor(Math.random() * 8) + 5
      },
      newRegistrations: {
        today: Math.floor(Math.random() * 3),
        thisWeek: Math.floor(Math.random() * 10) + 2,
        thisMonth: Math.floor(Math.random() * 25) + 10
      },
      byRole: {
        admin: 1,
        cashier: 1,
        accountant: 1,
        management: 3,
        reception: 3,
        teacher: 4
      }
    };
  }

  private static async getSystemAlerts(): Promise<any[]> {
    const alerts: any[] = [];

    // Check system health and generate alerts
    const memoryUsage = process.memoryUsage();
    const memoryUsagePercent = memoryUsage.heapUsed / memoryUsage.heapTotal;

    if (memoryUsagePercent > 0.8) {
      alerts.push({
        id: 'high-memory-usage',
        type: 'warning',
        title: 'High Memory Usage',
        message: `Memory usage is at ${Math.round(memoryUsagePercent * 100)}%`,
        timestamp: new Date().toISOString(),
        severity: 'medium'
      });
    }

    // Check for recent errors
    const recentLogs = debugLogger.getRecentLogs(50);
    const errorLogs = recentLogs.filter(log => log.level === 0); // ERROR level

    if (errorLogs.length > 5) {
      alerts.push({
        id: 'high-error-rate',
        type: 'error',
        title: 'High Error Rate',
        message: `${errorLogs.length} errors in recent logs`,
        timestamp: new Date().toISOString(),
        severity: 'high'
      });
    }

    // Check cache performance
    const cacheStats = cache.getStats();
    if (cacheStats.expired > cacheStats.active) {
      alerts.push({
        id: 'cache-performance',
        type: 'info',
        title: 'Cache Performance',
        message: 'High cache expiration rate detected',
        timestamp: new Date().toISOString(),
        severity: 'low'
      });
    }

    return alerts;
  }

  private static getPerformanceSummary(): any {
    const metrics = PerformanceMonitor.getMetrics();
    
    const totalOperations = Object.values(metrics).reduce((sum: number, metric: any) => sum + metric.count, 0);
    const avgResponseTime = Object.values(metrics).reduce((sum: number, metric: any) => sum + metric.avgTime, 0) / Object.keys(metrics).length || 0;

    return {
      totalOperations,
      avgResponseTime: Math.round(avgResponseTime * 100) / 100,
      slowestOperation: Object.entries(metrics)
        .sort(([,a], [,b]) => (b as any).avgTime - (a as any).avgTime)[0] || null,
      fastestOperation: Object.entries(metrics)
        .sort(([,a], [,b]) => (a as any).avgTime - (b as any).avgTime)[0] || null,
      cache: cache.getStats()
    };
  }
}

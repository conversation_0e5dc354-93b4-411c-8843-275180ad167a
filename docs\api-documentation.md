# 📚 API Documentation - Innovative Centre Platform

## Overview

This document provides comprehensive API documentation for the Innovative Centre Platform, covering both Admin and Staff servers.

## Base URLs

- **Admin Server**: `http://localhost:3000/api`
- **Staff Server**: `http://localhost:3003/api`

## Authentication

All API endpoints (except login) require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Admin Server API Endpoints

### Authentication

#### POST /api/auth/login
Login to the admin system.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "admin",
      "name": "Admin User"
    },
    "token": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 86400
  }
}
```

#### POST /api/auth/logout
Logout from the system.

#### GET /api/auth/me
Get current user information.

#### POST /api/auth/refresh
Refresh authentication token.

### User Management

#### GET /api/users
List all users with pagination and filtering.

**Query Parameters:**
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `search` (string): Search term
- `role` (string): Filter by role
- `isActive` (boolean): Filter by active status

#### POST /api/users
Create a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "role": "cashier",
  "name": "New User",
  "isActive": true
}
```

#### GET /api/users/:id
Get user by ID.

#### PUT /api/users/:id
Update user information.

#### DELETE /api/users/:id
Deactivate user (soft delete).

### Payment Management

#### GET /api/payments
List all payments with filtering.

**Query Parameters:**
- `page`, `limit`: Pagination
- `studentId`: Filter by student
- `paymentType`: Filter by payment type
- `status`: Filter by status
- `dateFrom`, `dateTo`: Date range filter

#### POST /api/payments
Record a new payment.

**Request Body:**
```json
{
  "studentId": "uuid",
  "amount": 150.00,
  "paymentType": "tuition",
  "paymentMethod": "card",
  "description": "Monthly tuition payment"
}
```

#### GET /api/payments/:id
Get payment details.

#### PUT /api/payments/:id
Update payment information.

### Cabinet Management

#### GET /api/cabinets
List all cabinets.

#### POST /api/cabinets
Create a new cabinet.

#### GET /api/cabinets/:id/bookings
Get cabinet bookings.

#### POST /api/cabinets/:id/book
Book a cabinet.

### Activity Logs

#### GET /api/activity-logs
List activity logs (admin only).

**Query Parameters:**
- `userId`: Filter by user
- `action`: Filter by action type
- `resourceType`: Filter by resource type
- `dateFrom`, `dateTo`: Date range

#### GET /api/activity-logs/export
Export activity logs.

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Invalid credentials |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 422 | Unprocessable Entity - Validation error |
| 500 | Internal Server Error |

## Rate Limiting

API requests are limited to 100 requests per 15-minute window per IP address.

## Activity Logging

All administrative actions are automatically logged with:
- User information
- Action type and timestamp
- Resource affected
- Old and new values
- IP address and user agent

## Data Types

### User Roles
- `admin`: Full system access
- `cashier`: Payment operations
- `accountant`: Financial reporting

### Payment Types
- `tuition`: Regular tuition payments
- `registration`: Registration fees
- `materials`: Learning materials
- `exam_fee`: Examination fees
- `other`: Other payments

### Payment Methods
- `cash`: Cash payment
- `card`: Credit/debit card
- `bank_transfer`: Bank transfer
- `online`: Online payment
- `other`: Other methods

## Examples

### Creating a Payment
```bash
curl -X POST http://localhost:3000/api/payments \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "studentId": "student-uuid",
    "amount": 200.00,
    "paymentType": "tuition",
    "paymentMethod": "card"
  }'
```

### Filtering Activity Logs
```bash
curl "http://localhost:3000/api/activity-logs?action=CREATE&resourceType=PAYMENT&dateFrom=2024-01-01" \
  -H "Authorization: Bearer <token>"
```

## Staff Server API (Phase 2)

The Staff Server API will include endpoints for:
- Student management
- Lead management
- Group/class management
- Teacher operations
- Operational reporting

*Detailed documentation will be added during Phase 2 development.*

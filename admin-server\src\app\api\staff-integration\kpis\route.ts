/**
 * Cross-server KPI endpoint for staff server integration
 * Provides financial and operational KPIs to the staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function GET(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);

    const startDate = filters.startDate || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    const endDate = filters.endDate || new Date().toISOString().split('T')[0];
    const type = filters.type || 'all'; // financial, operational, all

    try {
      const kpiData: any = {
        dateRange: { startDate, endDate },
        generatedAt: new Date().toISOString(),
      };

      // Get financial KPIs
      if (type === 'financial' || type === 'all') {
        const financialResult = await query(`
          WITH payment_stats AS (
            SELECT 
              COALESCE(SUM(p.amount), 0) as total_revenue,
              COUNT(p.id) as total_payments,
              COUNT(DISTINCT p.invoice_id) as invoices_paid
            FROM payments p
            WHERE p.payment_date >= $1 AND p.payment_date <= $2 
              AND p.status = 'completed'
          ),
          invoice_stats AS (
            SELECT 
              COALESCE(SUM(i.amount), 0) as total_invoiced,
              COALESCE(SUM(CASE WHEN i.status = 'pending' THEN i.amount ELSE 0 END), 0) as outstanding_balance,
              COUNT(i.id) as total_invoices,
              COUNT(CASE WHEN i.status = 'pending' THEN 1 END) as pending_invoices
            FROM invoices i
            WHERE i.created_at >= $1 AND i.created_at <= $2
          ),
          monthly_revenue AS (
            SELECT 
              DATE_TRUNC('month', p.payment_date) as month,
              SUM(p.amount) as amount
            FROM payments p
            WHERE p.payment_date >= $1 AND p.payment_date <= $2 
              AND p.status = 'completed'
            GROUP BY DATE_TRUNC('month', p.payment_date)
            ORDER BY month
          ),
          cabinet_revenue AS (
            SELECT 
              COALESCE(SUM(b.total_cost), 0) as cabinet_revenue
            FROM bookings b
            WHERE b.booking_date >= $1 AND b.booking_date <= $2
              AND b.status = 'confirmed'
          )
          SELECT 
            (SELECT row_to_json(payment_stats) FROM payment_stats) as payments,
            (SELECT row_to_json(invoice_stats) FROM invoice_stats) as invoices,
            (SELECT json_agg(row_to_json(monthly_revenue)) FROM monthly_revenue) as monthly_revenue,
            (SELECT row_to_json(cabinet_revenue) FROM cabinet_revenue) as cabinets
        `, [startDate, endDate]);

        kpiData.financial = financialResult.rows[0];
      }

      // Get operational KPIs
      if (type === 'operational' || type === 'all') {
        const operationalResult = await query(`
          WITH student_stats AS (
            SELECT 
              COUNT(*) as total_students,
              COUNT(CASE WHEN sr.status = 'active' THEN 1 END) as active_students,
              COUNT(CASE WHEN sr.enrollment_date >= $1 AND sr.enrollment_date <= $2 THEN 1 END) as new_enrollments
            FROM student_records sr
          ),
          cabinet_stats AS (
            SELECT 
              COUNT(*) as total_cabinets,
              COUNT(CASE WHEN c.is_active = true THEN 1 END) as active_cabinets,
              AVG(c.hourly_rate) as avg_hourly_rate
            FROM cabinets c
          ),
          booking_stats AS (
            SELECT 
              COUNT(*) as total_bookings,
              COUNT(CASE WHEN b.status = 'confirmed' THEN 1 END) as confirmed_bookings,
              AVG(EXTRACT(EPOCH FROM (b.end_time - b.start_time))/3600) as avg_booking_hours
            FROM bookings b
            WHERE b.booking_date >= $1 AND b.booking_date <= $2
          ),
          utilization_stats AS (
            SELECT 
              ROUND(
                COUNT(b.id)::decimal / 
                (COUNT(DISTINCT c.id) * EXTRACT(DAYS FROM $2::date - $1::date + 1) * 8) * 100, 2
              ) as cabinet_utilization
            FROM cabinets c
            LEFT JOIN bookings b ON c.id = b.cabinet_id 
              AND b.booking_date >= $1 AND b.booking_date <= $2
              AND b.status = 'confirmed'
            WHERE c.is_active = true
          )
          SELECT 
            (SELECT row_to_json(student_stats) FROM student_stats) as students,
            (SELECT row_to_json(cabinet_stats) FROM cabinet_stats) as cabinets,
            (SELECT row_to_json(booking_stats) FROM booking_stats) as bookings,
            (SELECT row_to_json(utilization_stats) FROM utilization_stats) as utilization
        `, [startDate, endDate]);

        kpiData.operational = operationalResult.rows[0];
      }

      return createResponse(kpiData, true, 'Cross-server KPIs retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving cross-server KPIs:', dbError);
      return createErrorResponse('Failed to retrieve KPIs', 500);
    }

  } catch (error) {
    console.error('Cross-server KPI error:', error);
    return createErrorResponse('Failed to retrieve KPIs', 500);
  }
}

/**
 * Student Records API - Individual Student Record
 * Provides student record information for payment tracking
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface StudentRecord {
  id: string;
  staff_student_id: string;
  student_name: string;
  total_debt: number;
  last_payment_date?: Date;
  created_at: Date;
  updated_at: Date;
}

// GET /api/student-records/[id] - Get student record by staff student ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Get student record by staff student ID
    const sql = `
      SELECT 
        id,
        staff_student_id,
        student_name,
        total_debt,
        last_payment_date,
        created_at,
        updated_at
      FROM student_records 
      WHERE staff_student_id = $1
    `;

    const result = await query<StudentRecord>(sql, [id]);

    if (result.rows.length === 0) {
      return createErrorResponse('Student record not found', 404);
    }

    const studentRecord = result.rows[0];

    // Format response
    const responseRecord = {
      id: studentRecord.id,
      staffStudentId: studentRecord.staff_student_id,
      studentName: studentRecord.student_name,
      totalDebt: parseFloat(studentRecord.total_debt.toString()),
      lastPaymentDate: studentRecord.last_payment_date,
      createdAt: studentRecord.created_at,
      updatedAt: studentRecord.updated_at
    };

    return createResponse(responseRecord, true, 'Student record retrieved successfully');

  } catch (error) {
    console.error('Error getting student record:', error);
    return createErrorResponse('Failed to get student record', 500);
  }
}

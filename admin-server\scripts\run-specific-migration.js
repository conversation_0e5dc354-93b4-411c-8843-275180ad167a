/**
 * Run a specific migration file
 * Usage: node scripts/run-specific-migration.js <migration-file>
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

async function runSpecificMigration(migrationFile) {
  const pool = new Pool(dbConfig);
  
  try {
    console.log(`🚀 Running migration: ${migrationFile}`);
    
    // Test connection
    console.log('📡 Testing database connection...');
    const testResult = await pool.query('SELECT NOW() as current_time');
    console.log('✅ Database connected successfully:', testResult.rows[0].current_time);
    
    // Read migration file
    const migrationPath = path.join(__dirname, '..', migrationFile);
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migration file not found: ${migrationPath}`);
    }
    
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('🔧 Executing migration...');
    await pool.query(migrationSql);
    console.log('✅ Migration executed successfully');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Get migration file from command line arguments
const migrationFile = process.argv[2];

if (!migrationFile) {
  console.error('❌ Please provide a migration file path');
  console.log('Usage: node scripts/run-specific-migration.js <migration-file>');
  process.exit(1);
}

runSpecificMigration(migrationFile);

# 🏗️ Innovative Centre Platform - Comprehensive Architecture Guide

## 📋 Executive Summary

The Innovative Centre Platform is a sophisticated dual-server CRM system designed for English tutoring organizations managing 4,000+ students and 40+ teachers. This document provides a deep dive into the system architecture, data flows, and technical implementation.

## 🎯 System Overview

### Core Architecture Principles
- **Microservices Design**: Separate admin and staff servers with distinct responsibilities
- **Data Segregation**: Independent databases with controlled inter-service communication
- **Role-Based Security**: Granular permissions across different user types
- **Audit Trail**: Comprehensive activity logging for compliance and monitoring

### Technology Stack
- **Frontend**: Next.js 14 with TypeScript and Tailwind CSS
- **Backend**: Next.js API Routes with RESTful design
- **Database**: PostgreSQL (Neon Cloud) with separate schemas
- **Authentication**: JWT tokens with role-based access control
- **Inter-service Communication**: API key-based authentication

## 🏛️ High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                 Innovative Centre Platform                      │
├─────────────────────────┬───────────────────────────────────────┤
│      Admin Server       │         Staff Server                  │
│     (Port 3000)         │        (Port 3003)                    │
├─────────────────────────┼───────────────────────────────────────┤
│ • Financial Management  │ • Student Enrollment                  │
│ • User Administration   │ • Lead Management                     │
│ • Reports & Analytics   │ • Class Scheduling                    │
│ • Activity Monitoring   │ • Teacher Operations                  │
│ • Payment Processing    │ • Reception Tasks                     │
│ • System Configuration  │ • Operational Reporting               │
└─────────────────────────┴───────────────────────────────────────┘
           │                              │
           └──────────── API ─────────────┘
              Secure Communication
```

## 🗄️ Database Architecture

### Admin Database Schema
```sql
-- Core Tables
users                    -- Admin users (admin, cashier, accountant) + Staff users
activity_logs           -- Comprehensive audit trail
payments                -- Financial transactions
student_records         -- Student data synced from staff server
cabinet_bookings        -- Room/resource management
kpis                    -- Key performance indicators
```

### Staff Database Schema
```sql
-- Core Tables
users                   -- Staff users (management, reception, teacher)
students                -- Student enrollment and management
leads                   -- Prospective student tracking
groups                  -- Class/group management
student_groups          -- Many-to-many student-class relationships
activity_logs           -- Operational activity tracking
```

## 🔐 Authentication & Authorization

### User Roles & Permissions

#### Admin Server Roles
- **Admin**: Full system access, user management, all reports
- **Cashier**: Payment processing, student financial records
- **Accountant**: Financial reports, payment oversight

#### Staff Server Roles
- **Management**: Full operational access, KPIs, staff oversight
- **Reception**: Student enrollment, lead management, basic operations
- **Teacher**: Class management, student progress, limited access

### Authentication Flow
```
1. User Login → Credential Validation → JWT Generation
2. API Request → Token Verification → Role Check → Access Grant/Deny
3. Token Refresh → Maintain Session → Update Expiration
```

## 🔄 Inter-Service Communication

### Communication Patterns

#### Admin → Staff Communication
- **User Management**: Admin server manages staff server users
- **Financial Queries**: Admin requests student payment data
- **Health Monitoring**: Admin monitors staff server status

#### Staff → Admin Communication
- **Activity Sync**: Staff sends operational logs to admin
- **Student Data**: Staff syncs student records for payment processing
- **KPI Reporting**: Staff sends performance metrics

### API Integration Points

#### Admin Server Endpoints
```
GET  /api/staff-integration/users        # Provide user data to staff
POST /api/staff-integration/activity-sync # Receive activity logs
GET  /api/staff-integration/health       # Health check for staff
POST /api/staff-integration/reports      # Consolidated reporting
```

#### Staff Server Endpoints
```
GET  /api/students                       # Student management
POST /api/students                       # Student enrollment
GET  /api/leads                         # Lead management
GET  /api/groups                        # Class management
POST /api/admin-sync/activity           # Send activity to admin
```

## 📊 Data Flow Diagrams

### Student Enrollment Flow
```
Reception User → Staff Server → Student Creation → Admin Sync → Payment Setup
     │              │               │                │             │
     └─ Login ──────┘               │                │             │
                    └─ Validate ────┘                │             │
                                    └─ Sync Data ────┘             │
                                                     └─ Ready ─────┘
```

### Payment Processing Flow
```
Admin User → Student Search → Staff Server Query → Payment Creation → Activity Log
     │            │               │                      │              │
     └─ Login ────┘               │                      │              │
                  └─ Search API ──┘                      │              │
                                  └─ Student Data ──────┘              │
                                                         └─ Audit ─────┘
```

### Activity Logging Flow
```
User Action → Local Log → Admin Sync → Central Storage → Monitoring Dashboard
     │           │           │             │                    │
     └─ Trigger ─┘           │             │                    │
                 └─ Create ──┘             │                    │
                             └─ Send ──────┘                    │
                                           └─ Store ────────────┘
```

## 🚀 System Components

### Frontend Architecture
```
Next.js App Router
├── app/
│   ├── dashboard/          # Main application interface
│   ├── login/             # Authentication pages
│   └── api/               # API route handlers
├── components/            # Reusable UI components
├── lib/                   # Utilities and configurations
└── types/                 # TypeScript definitions
```

### Backend Services
```
API Layer
├── Authentication         # JWT token management
├── Authorization         # Role-based access control
├── Database Layer        # PostgreSQL connection pooling
├── Inter-service API     # Server-to-server communication
└── Activity Logging      # Audit trail management
```

## 🔧 Technical Implementation

### Database Connections
- **Admin DB**: `**************************************************************************************`
- **Staff DB**: `**************************************************************************************`
- **Connection Pooling**: Max 20 connections, 30s idle timeout
- **SSL**: Required in production, optional in development

### Environment Configuration
```bash
# Admin Server
ADMIN_DATABASE_URL=postgresql://...
ADMIN_JWT_SECRET=***
ADMIN_PORT=3000

# Staff Server  
STAFF_DATABASE_URL=postgresql://...
STAFF_JWT_SECRET=***
STAFF_PORT=3003

# Inter-service
ADMIN_SERVER_API_KEY=***
STAFF_SERVER_API_KEY=***
```

### Security Measures
- **Password Hashing**: bcrypt with 12 rounds
- **JWT Expiration**: 24 hours for access tokens, 7 days for refresh
- **API Key Authentication**: For inter-service communication
- **Input Validation**: Comprehensive sanitization and validation
- **Rate Limiting**: 100 requests per 15-minute window

## 📈 Performance & Monitoring

### Health Check Endpoints
- **Admin**: `GET /api/health` - Server status and configuration
- **Staff**: `GET /api/health` - Operational status and integration
- **Database**: `GET /api/db-status` - Connection and query performance

### Monitoring Dashboard
- **System Overview**: Server health, database status, integration status
- **Activity Metrics**: User actions, API calls, error rates
- **Performance**: Response times, database query performance
- **Alerts**: Critical errors, system failures, integration issues

## 🔄 Development Workflow

### Local Development Setup
1. **Clone Repository**: Single monorepo structure
2. **Environment Setup**: Configure `.env.local` for each server
3. **Database Migration**: Run SQL scripts for schema setup
4. **Server Startup**: Run both servers concurrently
5. **Integration Testing**: Verify inter-service communication

### Deployment Strategy
- **Containerization**: Docker support for both servers
- **Environment Separation**: Development, staging, production
- **Database Migration**: Automated schema updates
- **Health Monitoring**: Continuous system health checks
- **Rollback Strategy**: Database and application rollback procedures

## 📚 API Documentation

### Standard Response Format
```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Authentication Headers
```
Authorization: Bearer <jwt_token>
X-API-Key: <inter_service_key>
X-Source-Service: <admin-server|staff-server>
```

## 🎯 Business Logic

### Student Management Workflow
1. **Lead Generation**: Reception captures prospective students
2. **Enrollment Process**: Convert leads to active students
3. **Class Assignment**: Assign students to appropriate groups
4. **Payment Setup**: Create payment records in admin system
5. **Progress Tracking**: Monitor academic progress and attendance

### Financial Management
1. **Payment Collection**: Process student payments through admin
2. **Debt Tracking**: Monitor outstanding balances
3. **Financial Reporting**: Generate revenue and collection reports
4. **Audit Trail**: Maintain complete payment history

### Operational Reporting
1. **KPI Tracking**: Monitor key performance indicators
2. **Activity Analysis**: Analyze user actions and system usage
3. **Performance Metrics**: Track enrollment, retention, revenue
4. **Compliance Reporting**: Generate audit and compliance reports

This architecture provides a robust, scalable foundation for managing educational operations while maintaining security, auditability, and operational efficiency.

## 🔍 Detailed Component Analysis

### Frontend Components Structure

#### Admin Server Frontend
```
src/app/
├── dashboard/
│   ├── users/              # User management interface
│   ├── payments/           # Payment processing UI
│   ├── reports/            # Financial and operational reports
│   ├── activity-logs/      # Audit trail viewer
│   ├── cabinet-bookings/   # Room/resource management
│   └── monitoring/         # System monitoring dashboard
├── components/
│   ├── ui/                 # Base UI components
│   ├── forms/              # Form components
│   ├── tables/             # Data table components
│   └── charts/             # Analytics and reporting charts
└── lib/
    ├── auth.ts             # Authentication utilities
    ├── db.ts               # Database connection
    ├── utils.ts            # Common utilities
    └── permissions.ts      # Role-based access control
```

#### Staff Server Frontend
```
src/app/
├── dashboard/
│   ├── students/           # Student management interface
│   ├── leads/              # Lead management UI
│   ├── groups/             # Class/group management
│   ├── kpis/               # Performance metrics dashboard
│   └── profile/            # User profile management
├── components/
│   ├── ui/                 # Base UI components
│   ├── student-forms/      # Student-specific forms
│   ├── lead-tracking/      # Lead management components
│   └── class-scheduler/    # Class scheduling components
└── lib/
    ├── auth.ts             # Authentication utilities
    ├── db.ts               # Database connection
    ├── admin-service.ts    # Admin server integration
    └── utils.ts            # Common utilities
```

### API Endpoint Specifications

#### Admin Server API Routes

**Authentication & User Management**
- `POST /api/auth/login` - User authentication
- `POST /api/auth/refresh` - Token refresh
- `GET /api/users` - List users with pagination
- `POST /api/users` - Create new user
- `PUT /api/users/[id]` - Update user details
- `DELETE /api/users/[id]` - Deactivate user

**Payment Processing**
- `GET /api/payments` - List payments with filters
- `POST /api/payments` - Create new payment
- `GET /api/payments/[id]` - Get payment details
- `PUT /api/payments/[id]` - Update payment
- `GET /api/student-records` - List student financial records
- `GET /api/student-records/[id]` - Get student financial details

**Reporting & Analytics**
- `GET /api/reports/financial` - Financial reports
- `GET /api/reports/activity` - Activity reports
- `GET /api/reports/kpis` - KPI reports
- `GET /api/activity-logs` - Audit trail with filters

**Inter-service Integration**
- `GET /api/staff-integration/users` - Provide user data to staff
- `POST /api/staff-integration/activity-sync` - Receive activity logs
- `GET /api/staff-integration/health` - Health check for staff
- `POST /api/staff-integration/reports` - Consolidated reporting

#### Staff Server API Routes

**Student Management**
- `GET /api/students` - List students with pagination and filters
- `POST /api/students` - Create new student
- `GET /api/students/[id]` - Get student details
- `PUT /api/students/[id]` - Update student information
- `DELETE /api/students/[id]` - Deactivate student

**Lead Management**
- `GET /api/leads` - List leads with filters
- `POST /api/leads` - Create new lead
- `PUT /api/leads/[id]` - Update lead status
- `POST /api/leads/[id]/convert` - Convert lead to student

**Class Management**
- `GET /api/groups` - List classes/groups
- `POST /api/groups` - Create new group
- `PUT /api/groups/[id]` - Update group details
- `POST /api/groups/[id]/students` - Add student to group
- `DELETE /api/groups/[id]/students/[studentId]` - Remove student from group

**Performance & Integration**
- `GET /api/kpis` - Role-specific KPIs
- `POST /api/admin-integration/activity` - Send activity to admin
- `GET /api/admin-integration/status` - Integration status

### Security Implementation Details

#### JWT Token Structure
```json
{
  "userId": "uuid",
  "email": "<EMAIL>",
  "role": "admin|cashier|accountant|management|reception|teacher",
  "iat": **********,
  "exp": **********
}
```

#### API Key Authentication
```javascript
// Inter-service request headers
{
  "X-API-Key": "secure-api-key",
  "X-Source-Service": "admin-server|staff-server",
  "Content-Type": "application/json"
}
```

#### Role-Based Permissions Matrix
```
Action                  | Admin | Cashier | Accountant | Management | Reception | Teacher
------------------------|-------|---------|------------|------------|-----------|--------
User Management         |   ✓   |    ✗    |     ✗      |     ✓      |     ✗     |    ✗
Payment Processing      |   ✓   |    ✓    |     ✓      |     ✗      |     ✗     |    ✗
Financial Reports       |   ✓   |    ✓    |     ✓      |     ✓      |     ✗     |    ✗
Student Management      |   ✓   |    ✗    |     ✗      |     ✓      |     ✓     |    ✓
Lead Management         |   ✗   |    ✗    |     ✗      |     ✓      |     ✓     |    ✗
Class Management        |   ✗   |    ✗    |     ✗      |     ✓      |     ✗     |    ✓
Activity Logs           |   ✓   |    ✓    |     ✓      |     ✓      |     ✗     |    ✗
System Monitoring       |   ✓   |    ✗    |     ✗      |     ✓      |     ✗     |    ✗
```

### Data Synchronization Mechanisms

#### Student Data Sync Flow
1. **Creation**: Student created in Staff Server
2. **Validation**: Data validation and UUID generation
3. **Local Storage**: Store in Staff Database
4. **Sync Request**: Send to Admin Server via API
5. **Admin Processing**: Create student_record in Admin Database
6. **Confirmation**: Return success/failure status
7. **Error Handling**: Retry mechanism for failed syncs

#### Activity Log Sync Flow
1. **Action Trigger**: User performs action in Staff Server
2. **Local Log**: Create activity log in Staff Database
3. **Async Sync**: Queue for Admin Server sync
4. **Batch Processing**: Send logs in batches to Admin
5. **Admin Storage**: Store in Admin activity_logs table
6. **Acknowledgment**: Confirm receipt and processing

### Error Handling & Recovery

#### Database Connection Failures
- **Connection Pooling**: Automatic reconnection attempts
- **Graceful Degradation**: Continue operation with limited functionality
- **Error Logging**: Comprehensive error tracking
- **User Notification**: Inform users of system limitations

#### Inter-service Communication Failures
- **Retry Logic**: Exponential backoff for failed requests
- **Circuit Breaker**: Prevent cascade failures
- **Fallback Mechanisms**: Local operation when remote unavailable
- **Queue Management**: Store failed syncs for later retry

#### Data Consistency
- **Transaction Management**: ACID compliance for critical operations
- **Conflict Resolution**: Handle concurrent modifications
- **Data Validation**: Input sanitization and validation
- **Audit Trail**: Complete change tracking for accountability

### Performance Optimization

#### Database Optimization
- **Indexing Strategy**: Optimized indexes for common queries
- **Query Optimization**: Efficient SQL queries with proper joins
- **Connection Pooling**: Reuse database connections
- **Caching**: Redis-ready for session and data caching

#### API Performance
- **Pagination**: Limit result sets for large datasets
- **Filtering**: Server-side filtering to reduce data transfer
- **Compression**: Gzip compression for API responses
- **Rate Limiting**: Prevent API abuse and ensure fair usage

#### Frontend Optimization
- **Code Splitting**: Lazy loading of components
- **Caching**: Browser caching for static assets
- **Optimistic Updates**: Immediate UI feedback
- **Error Boundaries**: Graceful error handling in React

This comprehensive architecture ensures scalable, maintainable, and secure operations for educational institutions of any size.

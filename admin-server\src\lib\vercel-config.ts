/**
 * Vercel deployment configuration utilities
 * Handles environment-specific URL resolution and service communication
 */

/**
 * Get the staff server URL for inter-service communication
 * Handles both local development and Vercel production URLs
 */
export function getStaffServerUrl(): string {
  const staffServerUrl = process.env.STAFF_SERVER_URL;
  
  if (!staffServerUrl) {
    // Fallback for development
    return 'http://localhost:3003';
  }
  
  // Ensure HTTPS for production
  if (process.env.NODE_ENV === 'production' && !staffServerUrl.startsWith('https://')) {
    console.warn('Staff server URL should use HTTPS in production');
  }
  
  return staffServerUrl;
}

/**
 * Get the admin server URL for client-side usage
 */
export function getAdminServerUrl(): string {
  const adminServerUrl = process.env.NEXT_PUBLIC_APP_URL;
  
  if (!adminServerUrl) {
    // Fallback for development
    return 'http://localhost:3000';
  }
  
  return adminServerUrl;
}

/**
 * Check if staff server integration is properly configured
 */
export function isStaffServerConfigured(): boolean {
  const staffServerUrl = process.env.STAFF_SERVER_URL;
  const apiKey = process.env.STAFF_SERVER_API_KEY;
  
  return !!(staffServerUrl && apiKey);
}

/**
 * Get API headers for inter-service communication
 */
export function getInterServiceHeaders(): Record<string, string> {
  return {
    'Content-Type': 'application/json',
    'X-API-Key': process.env.STAFF_SERVER_API_KEY || '',
    'X-Source-Service': 'admin-server',
    'User-Agent': 'Innovative-Centre-Admin/1.0.0',
  };
}

/**
 * Validate environment configuration for Vercel deployment
 */
export function validateVercelConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Required environment variables
  const required = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NEXTAUTH_SECRET',
  ];
  
  for (const envVar of required) {
    if (!process.env[envVar]) {
      errors.push(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // Staff server integration
  if (!process.env.STAFF_SERVER_URL) {
    warnings.push('STAFF_SERVER_URL not configured - staff integration will be disabled');
  }
  
  if (!process.env.STAFF_SERVER_API_KEY) {
    warnings.push('STAFF_SERVER_API_KEY not configured - staff integration will be disabled');
  }
  
  // Production-specific checks
  if (process.env.NODE_ENV === 'production') {
    if (process.env.NEXT_PUBLIC_APP_URL && !process.env.NEXT_PUBLIC_APP_URL.startsWith('https://')) {
      warnings.push('NEXT_PUBLIC_APP_URL should use HTTPS in production');
    }
    
    if (process.env.STAFF_SERVER_URL && !process.env.STAFF_SERVER_URL.startsWith('https://')) {
      warnings.push('STAFF_SERVER_URL should use HTTPS in production');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

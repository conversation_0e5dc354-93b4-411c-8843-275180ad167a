/**
 * Authentication API integration tests
 */

import { NextRequest } from 'next/server';
import { POST as loginPost } from '../../src/app/api/auth/login/route';
import { POST as logoutPost } from '../../src/app/api/auth/logout/route';
import { GET as meGet } from '../../src/app/api/auth/me/route';

// Using real database and activity logger - no mocking needed for integration testing
import { query } from '../../src/lib/db';
import { logAuthEvent, getRequestContext } from '../../src/lib/activity-logger';

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  compare: jest.fn(),
}));

import { query } from '../../src/lib/db';
import bcrypt from 'bcryptjs';

const mockQuery = query as jest.MockedFunction<typeof query>;
const mockBcryptCompare = bcrypt.compare as jest.MockedFunction<typeof bcrypt.compare>;

describe('Authentication API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.JWT_SECRET = 'test-secret';
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'management',
        name: 'Test User',
        is_active: true,
      };

      mockQuery.mockResolvedValueOnce({ rows: [mockUser] });
      mockBcryptCompare.mockResolvedValueOnce(true);

      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.user.email).toBe('<EMAIL>');
      expect(data.data.token).toBeDefined();
    });

    it('should reject login with invalid email', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid email or password');
    });

    it('should reject login with invalid password', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'management',
        name: 'Test User',
        is_active: true,
      };

      mockQuery.mockResolvedValueOnce({ rows: [mockUser] });
      mockBcryptCompare.mockResolvedValueOnce(false);

      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'wrongpassword',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid email or password');
    });

    it('should reject login for inactive user', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        password_hash: 'hashed-password',
        role: 'management',
        name: 'Test User',
        is_active: false,
      };

      mockQuery.mockResolvedValueOnce({ rows: [mockUser] });

      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Account is deactivated');
    });

    it('should validate required fields', async () => {
      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          // Missing password
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Missing required fields');
    });

    it('should validate email format', async () => {
      const request = new NextRequest('http://localhost/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({
          email: 'invalid-email',
          password: 'password123',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginPost(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid email format');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully with valid token', async () => {
      // Mock getUserFromRequest to return a valid user
      jest.doMock('../../src/lib/auth', () => ({
        getUserFromRequest: jest.fn().mockResolvedValue({
          success: true,
          user: { id: 'user-123', email: '<EMAIL>', role: 'management' },
        }),
      }));

      const request = new NextRequest('http://localhost/api/auth/logout', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer valid-token',
        },
      });

      const response = await logoutPost(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.message).toContain('Logged out successfully');
    });

    it('should reject logout without authentication', async () => {
      // Mock getUserFromRequest to return authentication failure
      jest.doMock('../../src/lib/auth', () => ({
        getUserFromRequest: jest.fn().mockResolvedValue({
          success: false,
          error: 'No token provided',
        }),
      }));

      const request = new NextRequest('http://localhost/api/auth/logout', {
        method: 'POST',
      });

      const response = await logoutPost(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Authentication required');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return user information with valid token', async () => {
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'management',
        name: 'Test User',
        is_active: true,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Mock getUserFromRequest to return a valid user
      jest.doMock('../../src/lib/auth', () => ({
        getUserFromRequest: jest.fn().mockResolvedValue({
          success: true,
          user: { id: 'user-123', email: '<EMAIL>', role: 'management' },
        }),
      }));

      mockQuery.mockResolvedValueOnce({ rows: [mockUser] });

      const request = new NextRequest('http://localhost/api/auth/me', {
        headers: {
          'Authorization': 'Bearer valid-token',
        },
      });

      const response = await meGet(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.email).toBe('<EMAIL>');
      expect(data.data.role).toBe('management');
    });

    it('should reject request without authentication', async () => {
      // Mock getUserFromRequest to return authentication failure
      jest.doMock('../../src/lib/auth', () => ({
        getUserFromRequest: jest.fn().mockResolvedValue({
          success: false,
          error: 'No token provided',
        }),
      }));

      const request = new NextRequest('http://localhost/api/auth/me');

      const response = await meGet(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Authentication required');
    });
  });
});

/**
 * PaymentForm Component Tests
 * Unit tests for PaymentForm component
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PaymentForm from '../PaymentForm';
import { PaymentType, PaymentMethod } from '@/types';

describe('PaymentForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultProps = {
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render form fields correctly', () => {
    render(<PaymentForm {...defaultProps} />);

    expect(screen.getByLabelText(/student id/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/payment type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/payment method/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/payment date/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
  });

  it('should render submit and cancel buttons', () => {
    render(<PaymentForm {...defaultProps} />);

    expect(screen.getByRole('button', { name: /record payment/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('should populate form with initial data', () => {
    const initialData = {
      studentId: 'STU001',
      amount: '150.00',
      paymentType: PaymentType.TUITION,
      paymentMethod: PaymentMethod.CARD,
      description: 'Test payment',
      paymentDate: '2024-01-15',
    };

    render(<PaymentForm {...defaultProps} initialData={initialData} />);

    expect(screen.getByDisplayValue('STU001')).toBeInTheDocument();
    expect(screen.getByDisplayValue('150.00')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test payment')).toBeInTheDocument();
    expect(screen.getByDisplayValue('2024-01-15')).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/student id is required/i)).toBeInTheDocument();
      expect(screen.getByText(/amount is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should validate amount field', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    const amountInput = screen.getByLabelText(/amount/i);
    
    // Test negative amount
    await user.type(amountInput, '-50');
    
    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/amount must be a positive number/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should validate payment date', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    const dateInput = screen.getByLabelText(/payment date/i);
    
    // Set future date
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const futureDateString = futureDate.toISOString().split('T')[0];
    
    await user.clear(dateInput);
    await user.type(dateInput, futureDateString);
    
    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/payment date cannot be in the future/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    // Fill form with valid data
    await user.type(screen.getByLabelText(/student id/i), 'STU001');
    await user.type(screen.getByLabelText(/amount/i), '150.00');
    await user.selectOptions(screen.getByLabelText(/payment type/i), PaymentType.TUITION);
    await user.selectOptions(screen.getByLabelText(/payment method/i), PaymentMethod.CARD);
    await user.type(screen.getByLabelText(/description/i), 'Test payment');

    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        studentId: 'STU001',
        amount: '150.00',
        paymentType: PaymentType.TUITION,
        paymentMethod: PaymentMethod.CARD,
        description: 'Test payment',
        paymentDate: expect.any(String),
      });
    });
  });

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should disable form when loading', () => {
    render(<PaymentForm {...defaultProps} isLoading={true} />);

    expect(screen.getByLabelText(/student id/i)).toBeDisabled();
    expect(screen.getByLabelText(/amount/i)).toBeDisabled();
    expect(screen.getByRole('button', { name: /recording/i })).toBeDisabled();
  });

  it('should clear validation errors when user starts typing', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    // Trigger validation error
    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/student id is required/i)).toBeInTheDocument();
    });

    // Start typing in student ID field
    const studentIdInput = screen.getByLabelText(/student id/i);
    await user.type(studentIdInput, 'S');

    await waitFor(() => {
      expect(screen.queryByText(/student id is required/i)).not.toBeInTheDocument();
    });
  });

  it('should handle form submission errors gracefully', async () => {
    const user = userEvent.setup();
    const mockOnSubmitWithError = jest.fn().mockRejectedValue(new Error('Submission failed'));
    
    render(<PaymentForm {...defaultProps} onSubmit={mockOnSubmitWithError} />);

    // Fill form with valid data
    await user.type(screen.getByLabelText(/student id/i), 'STU001');
    await user.type(screen.getByLabelText(/amount/i), '150.00');

    const submitButton = screen.getByRole('button', { name: /record payment/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmitWithError).toHaveBeenCalled();
    });

    // Form should not crash and should remain functional
    expect(screen.getByLabelText(/student id/i)).toBeInTheDocument();
  });

  it('should respect minimum and maximum amount limits', async () => {
    const user = userEvent.setup();
    render(<PaymentForm {...defaultProps} />);

    const amountInput = screen.getByLabelText(/amount/i);
    const submitButton = screen.getByRole('button', { name: /record payment/i });

    // Test amount below minimum
    await user.type(amountInput, '0.50'); // Assuming minimum is $1
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/amount must be at least/i)).toBeInTheDocument();
    });

    // Clear and test amount above maximum
    await user.clear(amountInput);
    await user.type(amountInput, '100000000'); // Above maximum of 99,999,999
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/amount cannot exceed/i)).toBeInTheDocument();
    });
  });

  it('should show edit mode correctly', () => {
    const initialData = {
      studentId: 'STU001',
      amount: '150.00',
    };

    render(<PaymentForm {...defaultProps} initialData={initialData} mode="edit" />);

    expect(screen.getByRole('button', { name: /update payment/i })).toBeInTheDocument();
  });
});

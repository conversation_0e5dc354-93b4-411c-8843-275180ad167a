/**
 * Test Helper Utilities
 * Common utilities and mocks for testing
 */

import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';
import { UserRole } from '@/types';

// Real data creation functions for integration testing
export const createTestPayment = async (paymentData: Partial<any> = {}, userId: string) => {
  const defaultPayment = {
    student_id: `TEST-STU-${Date.now()}`,
    amount: 150.00,
    payment_type: 'tuition',
    payment_method: 'card',
    description: 'Test payment for integration testing',
    status: 'completed',
    processed_by: userId,
    start_date: new Date('2024-01-15'),
    end_date: new Date('2024-02-15'),
    debt_amount: 0,
    is_debt_payment: false,
    ...paymentData
  };

  const result = await query(
    `INSERT INTO payments (student_id, amount, payment_type, payment_method, description, status, processed_by, start_date, end_date, debt_amount, is_debt_payment)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11) RETURNING *`,
    [defaultPayment.student_id, defaultPayment.amount, defaultPayment.payment_type, defaultPayment.payment_method,
     defaultPayment.description, defaultPayment.status, defaultPayment.processed_by, defaultPayment.start_date,
     defaultPayment.end_date, defaultPayment.debt_amount, defaultPayment.is_debt_payment]
  );

  return result.rows[0];
};

export const createTestInvoice = async (invoiceData: Partial<any> = {}, userId: string) => {
  const defaultInvoice = {
    student_id: `TEST-STU-${Date.now()}`,
    amount: 200.00,
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    status: 'pending',
    created_by: userId,
    description: 'Test invoice for integration testing',
    ...invoiceData
  };

  const result = await query(
    `INSERT INTO invoices (student_id, amount, due_date, status, created_by, description)
     VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
    [defaultInvoice.student_id, defaultInvoice.amount, defaultInvoice.due_date,
     defaultInvoice.status, defaultInvoice.created_by, defaultInvoice.description]
  );

  return result.rows[0];
};

export const createTestCabinet = async (cabinetData: Partial<any> = {}) => {
  const defaultCabinet = {
    name: `Test Cabinet ${Date.now()}`,
    capacity: 10,
    equipment: ['Projector', 'Whiteboard'],
    hourly_rate: 25.00,
    is_available: true,
    ...cabinetData
  };

  const result = await query(
    `INSERT INTO cabinets (name, capacity, equipment, hourly_rate, is_available)
     VALUES ($1, $2, $3, $4, $5) RETURNING *`,
    [defaultCabinet.name, defaultCabinet.capacity, JSON.stringify(defaultCabinet.equipment),
     defaultCabinet.hourly_rate, defaultCabinet.is_available]
  );

  return result.rows[0];
};

export const createTestBooking = async (bookingData: Partial<any> = {}, userId: string, cabinetId?: string) => {
  // Create a test cabinet if none provided
  const cabinet = cabinetId ? { id: cabinetId } : await createTestCabinet();

  const defaultBooking = {
    cabinet_id: cabinet.id,
    date: new Date().toISOString().split('T')[0],
    start_time: '09:00',
    end_time: '10:00',
    booked_by: userId,
    purpose: 'Test meeting for integration testing',
    status: 'confirmed',
    ...bookingData
  };

  const result = await query(
    `INSERT INTO cabinet_bookings (cabinet_id, date, start_time, end_time, booked_by, purpose, status)
     VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
    [defaultBooking.cabinet_id, defaultBooking.date, defaultBooking.start_time,
     defaultBooking.end_time, defaultBooking.booked_by, defaultBooking.purpose, defaultBooking.status]
  );

  return result.rows[0];
};

// API response helpers
export const mockApiResponse = (data: any, success = true, message = 'Success') => ({
  data,
  success,
  message,
});

export const mockErrorResponse = (error = 'Test error', status = 500) => ({
  success: false,
  error,
  status,
});

export const mockPaginatedResponse = (data: any[], page = 1, limit = 10, total?: number) => ({
  data,
  pagination: {
    page,
    limit,
    total: total || data.length,
    totalPages: Math.ceil((total || data.length) / limit),
    hasNext: page < Math.ceil((total || data.length) / limit),
    hasPrev: page > 1,
  },
  success: true,
  message: 'Data retrieved successfully',
});

// Fetch mock helpers
export const mockFetch = (response: any, ok = true, status = 200) => {
  const mockResponse = {
    ok,
    status,
    json: jest.fn().mockResolvedValue(response),
    text: jest.fn().mockResolvedValue(JSON.stringify(response)),
  };
  
  (global.fetch as jest.Mock).mockResolvedValue(mockResponse);
  return mockResponse;
};

export const mockFetchError = (error = 'Network error') => {
  (global.fetch as jest.Mock).mockRejectedValue(new Error(error));
};

// Local storage helpers
export const mockLocalStorage = (data: Record<string, string> = {}) => {
  const defaultData = {
    token: 'test-token',
    user: JSON.stringify(mockUser()),
    ...data,
  };
  
  Object.keys(defaultData).forEach(key => {
    localStorage.setItem(key, defaultData[key as keyof typeof defaultData]);
  });
};

export const clearLocalStorage = () => {
  localStorage.clear();
};

// Form testing helpers
export const fillForm = async (container: HTMLElement, formData: Record<string, string>) => {
  const { fireEvent } = await import('@testing-library/react');
  
  Object.entries(formData).forEach(([name, value]) => {
    const input = container.querySelector(`[name="${name}"]`) as HTMLInputElement;
    if (input) {
      fireEvent.change(input, { target: { value } });
    }
  });
};

export const submitForm = async (container: HTMLElement) => {
  const { fireEvent } = await import('@testing-library/react');
  
  const form = container.querySelector('form');
  if (form) {
    fireEvent.submit(form);
  }
};

// Wait utilities
export const waitForElement = async (callback: () => HTMLElement | null, timeout = 5000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    const element = callback();
    if (element) {
      return element;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error('Element not found within timeout');
};

export const waitForCondition = async (condition: () => boolean, timeout = 5000) => {
  const startTime = Date.now();
  
  while (Date.now() - startTime < timeout) {
    if (condition()) {
      return true;
    }
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  throw new Error('Condition not met within timeout');
};

// Custom render function with providers
export const renderWithProviders = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  // Add any providers here if needed (e.g., Context providers, Router providers)
  return render(ui, options);
};

// Real database helpers for integration testing
import { query } from '@/lib/db';

export const createTestUser = async (userData: Partial<any> = {}) => {
  const defaultUser = {
    email: `test-${Date.now()}@example.com`,
    name: 'Test User',
    role: 'admin',
    password_hash: '$2b$12$test.hash.for.testing.purposes',
    is_active: true,
    server_type: 'admin',
    ...userData
  };

  const result = await query(
    `INSERT INTO users (email, name, role, password_hash, is_active, server_type)
     VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
    [defaultUser.email, defaultUser.name, defaultUser.role, defaultUser.password_hash, defaultUser.is_active, defaultUser.server_type]
  );

  return result.rows[0];
};

export const cleanupTestData = async () => {
  // Clean up test data - be careful to only delete test records
  await query(`DELETE FROM users WHERE email LIKE '<EMAIL>'`);
  await query(`DELETE FROM payments WHERE description LIKE 'Test payment%'`);
  await query(`DELETE FROM invoices WHERE student_id LIKE 'TEST%'`);
  await query(`DELETE FROM cabinet_bookings WHERE purpose LIKE 'Test%'`);
};

// Authentication helpers
export const mockAuthenticatedUser = (user = mockUser()) => {
  mockLocalStorage({
    token: 'valid-token',
    user: JSON.stringify(user),
  });
  
  // Mock successful auth verification
  mockFetch(mockApiResponse(user));
};

export const mockUnauthenticatedUser = () => {
  clearLocalStorage();
  mockFetch(mockErrorResponse('Not authenticated', 401), false, 401);
};

// Date helpers
export const mockDate = (date: string | Date) => {
  const mockDate = new Date(date);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  return mockDate;
};

export const restoreDate = () => {
  jest.restoreAllMocks();
};

// Console helpers
export const suppressConsoleErrors = () => {
  const originalError = console.error;
  console.error = jest.fn();
  return () => {
    console.error = originalError;
  };
};

export const suppressConsoleWarnings = () => {
  const originalWarn = console.warn;
  console.warn = jest.fn();
  return () => {
    console.warn = originalWarn;
  };
};

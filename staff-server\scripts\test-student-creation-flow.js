/**
 * Test the complete student creation flow including authentication and activity logging
 */

require('dotenv').config({ path: '.env.local' });

async function testStudentCreationFlow() {
  console.log('🔍 Testing Complete Student Creation Flow...');
  
  const staffServerUrl = 'http://localhost:3003';
  const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
  
  // Use a known staff user for testing
  const testCredentials = {
    email: '<EMAIL>',
    password: 'password123' // Assuming this is the password
  };

  try {
    // Step 1: Authenticate as staff user
    console.log('\n🔐 Step 1: Authenticating as staff user...');
    console.log('👤 Email:', testCredentials.email);
    
    const loginResponse = await fetch(`${staffServerUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCredentials),
    });

    console.log('📡 Login response status:', loginResponse.status);
    
    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.error('❌ Login failed:', errorText);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log('👤 User ID:', loginData.data?.user?.id);
    console.log('👤 User role:', loginData.data?.user?.role);
    console.log('👤 User name:', loginData.data?.user?.name);
    
    const authToken = loginData.data?.token;
    if (!authToken) {
      console.error('❌ No auth token received');
      return;
    }

    // Step 2: Create a test student
    console.log('\n📝 Step 2: Creating test student...');
    
    const studentData = {
      firstName: 'Test',
      lastName: 'Student',
      email: '<EMAIL>',
      phone: '+998901234567',
      dateOfBirth: '2000-01-01',
      enrollmentDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };

    const createStudentResponse = await fetch(`${staffServerUrl}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(studentData),
    });

    console.log('📡 Create student response status:', createStudentResponse.status);
    
    const createStudentText = await createStudentResponse.text();
    console.log('📡 Create student response:', createStudentText);

    if (createStudentResponse.ok) {
      console.log('✅ Student created successfully');
      
      try {
        const studentResult = JSON.parse(createStudentText);
        console.log('📋 Created student ID:', studentResult.data?.id);
      } catch (parseError) {
        console.log('⚠️  Could not parse student creation response');
      }

      // Step 3: Check if activity appeared in admin server
      console.log('\n🔍 Step 3: Checking admin server activity logs...');
      
      // Wait a moment for the activity to be processed
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      try {
        const logsResponse = await fetch(`${adminServerUrl}/api/activity-logs?limit=5&action=CREATE&resourceType=STUDENT`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (logsResponse.ok) {
          const logsData = await logsResponse.json();
          console.log('📊 Recent student creation logs:');
          if (logsData.success && logsData.data && logsData.data.logs) {
            const recentLogs = logsData.data.logs.slice(0, 3);
            recentLogs.forEach((log, index) => {
              console.log(`  ${index + 1}. [${log.timestamp}] ${log.description}`);
              console.log(`      User: ${log.user?.name || log.user?.email || log.userId}`);
              console.log(`      User ID: ${log.userId}`);
            });
            
            // Check if our test student creation appears
            const ourLog = recentLogs.find(log => 
              log.description && log.description.includes('Test Student')
            );
            
            if (ourLog) {
              console.log('✅ Our student creation activity found in admin logs!');
            } else {
              console.log('⚠️  Our student creation activity not found in recent logs');
            }
          } else {
            console.log('  No logs found or invalid response format');
          }
        } else {
          console.log('⚠️  Could not fetch activity logs for verification');
        }
      } catch (logsError) {
        console.log('⚠️  Error fetching logs for verification:', logsError.message);
      }

    } else {
      console.log('❌ Student creation failed');
      
      try {
        const errorData = JSON.parse(createStudentText);
        console.log('❌ Error details:', errorData);
      } catch (parseError) {
        console.log('❌ Raw error response:', createStudentText);
      }
    }

  } catch (error) {
    console.error('❌ Error in student creation flow test:', error.message);
    console.error('Error details:', error);
  }

  console.log('\n🎉 Student creation flow test completed!');
}

// Run the test
testStudentCreationFlow();

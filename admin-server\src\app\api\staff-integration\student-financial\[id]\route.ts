/**
 * Student financial data endpoint for staff server integration
 * Provides financial information for students to the staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, isValidUUID } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid student ID format', 400);
    }

    try {
      // Get student financial summary
      const financialSummaryResult = await query(`
        WITH student_info AS (
          SELECT id, first_name, last_name, email, status
          FROM student_records 
          WHERE staff_student_id = $1
        ),
        payment_summary AS (
          SELECT 
            COALESCE(SUM(p.amount), 0) as total_paid,
            MAX(p.payment_date) as last_payment_date
          FROM payments p
          JOIN invoices i ON p.invoice_id = i.id
          JOIN student_records sr ON i.student_id = sr.id
          WHERE sr.staff_student_id = $1 AND p.status = 'completed'
        ),
        invoice_summary AS (
          SELECT 
            COALESCE(SUM(i.amount), 0) as total_invoiced,
            COALESCE(SUM(CASE WHEN i.status = 'pending' THEN i.amount ELSE 0 END), 0) as outstanding_balance
          FROM invoices i
          JOIN student_records sr ON i.student_id = sr.id
          WHERE sr.staff_student_id = $1
        )
        SELECT 
          si.*,
          ps.total_paid,
          ps.last_payment_date,
          ins.total_invoiced,
          ins.outstanding_balance
        FROM student_info si, payment_summary ps, invoice_summary ins
      `, [id]);

      if (financialSummaryResult.rows.length === 0) {
        return createErrorResponse('Student not found in financial system', 404);
      }

      const summary = financialSummaryResult.rows[0];

      // Get recent payment history
      const paymentHistoryResult = await query(`
        SELECT 
          p.id,
          p.amount,
          p.payment_date as date,
          p.payment_method as method,
          p.status,
          i.description as invoice_description
        FROM payments p
        JOIN invoices i ON p.invoice_id = i.id
        JOIN student_records sr ON i.student_id = sr.id
        WHERE sr.staff_student_id = $1
        ORDER BY p.payment_date DESC
        LIMIT 10
      `, [id]);

      // Get pending invoices
      const pendingInvoicesResult = await query(`
        SELECT 
          i.id,
          i.amount,
          i.due_date,
          i.description,
          i.created_at
        FROM invoices i
        JOIN student_records sr ON i.student_id = sr.id
        WHERE sr.staff_student_id = $1 AND i.status = 'pending'
        ORDER BY i.due_date ASC
      `, [id]);

      return createResponse({
        studentId: id,
        studentName: `${summary.first_name} ${summary.last_name}`,
        studentEmail: summary.email,
        studentStatus: summary.status,
        totalPaid: parseFloat(summary.total_paid || 0),
        totalInvoiced: parseFloat(summary.total_invoiced || 0),
        outstandingBalance: parseFloat(summary.outstanding_balance || 0),
        lastPaymentDate: summary.last_payment_date,
        paymentHistory: paymentHistoryResult.rows,
        pendingInvoices: pendingInvoicesResult.rows,
        retrievedAt: new Date().toISOString()
      }, true, 'Student financial data retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving student financial data:', dbError);
      return createErrorResponse('Failed to retrieve financial data', 500);
    }

  } catch (error) {
    console.error('Student financial data error:', error);
    return createErrorResponse('Failed to retrieve financial data', 500);
  }
}

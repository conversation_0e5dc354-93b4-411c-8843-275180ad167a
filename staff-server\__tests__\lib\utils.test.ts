/**
 * Utility functions unit tests
 */

import {
  validateRequiredFields,
  sanitizeInput,
  isValidEmail,
  isValidUUID,
  capitalize,
  camelToTitle,
  truncate,
  getInitials,
  formatPhoneNumber,
  calculatePercentage,
  getStatusColor,
  formatStudentName,
  calculateAge,
  parsePaginationParams,
  parseFilterParams
} from '../../src/lib/utils';

describe('Utility Functions', () => {
  describe('validateRequiredFields', () => {
    it('should validate when all required fields are present', () => {
      const body = { name: '<PERSON>', email: '<EMAIL>', age: 25 };
      const requiredFields = ['name', 'email'];
      const result = validateRequiredFields(body, requiredFields);
      
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toHaveLength(0);
    });

    it('should identify missing required fields', () => {
      const body = { name: '<PERSON>' };
      const requiredFields = ['name', 'email', 'phone'];
      const result = validateRequiredFields(body, requiredFields);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['email', 'phone']);
    });

    it('should treat empty strings as missing', () => {
      const body = { name: '', email: '<EMAIL>' };
      const requiredFields = ['name', 'email'];
      const result = validateRequiredFields(body, requiredFields);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['name']);
    });

    it('should treat null and undefined as missing', () => {
      const body = { name: null, email: undefined, phone: 'valid' };
      const requiredFields = ['name', 'email', 'phone'];
      const result = validateRequiredFields(body, requiredFields);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['name', 'email']);
    });
  });

  describe('sanitizeInput', () => {
    it('should remove dangerous HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = sanitizeInput(input);
      
      expect(result).toBe('scriptalert("xss")/scriptHello');
    });

    it('should remove javascript: protocol', () => {
      const input = 'javascript:alert("xss")';
      const result = sanitizeInput(input);
      
      expect(result).toBe('alert("xss")');
    });

    it('should remove event handlers', () => {
      const input = 'onclick=alert("xss") Hello';
      const result = sanitizeInput(input);
      
      expect(result).toBe('alert("xss") Hello');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const result = sanitizeInput(input);
      
      expect(result).toBe('Hello World');
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
      expect(isValidEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(isValidEmail('invalid-email')).toBe(false);
      expect(isValidEmail('@example.com')).toBe(false);
      expect(isValidEmail('test@')).toBe(false);
      expect(isValidEmail('test.example.com')).toBe(false);
    });
  });

  describe('isValidUUID', () => {
    it('should validate correct UUIDs', () => {
      expect(isValidUUID('123e4567-e89b-12d3-a456-************')).toBe(true);
      expect(isValidUUID('550e8400-e29b-41d4-a716-************')).toBe(true);
    });

    it('should reject invalid UUIDs', () => {
      expect(isValidUUID('invalid-uuid')).toBe(false);
      expect(isValidUUID('123e4567-e89b-12d3-a456')).toBe(false);
      expect(isValidUUID('123e4567-e89b-12d3-a456-42661417400g')).toBe(false);
    });
  });

  describe('String Formatting Functions', () => {
    it('should capitalize strings correctly', () => {
      expect(capitalize('hello')).toBe('Hello');
      expect(capitalize('WORLD')).toBe('World');
      expect(capitalize('hELLo')).toBe('Hello');
    });

    it('should convert camelCase to Title Case', () => {
      expect(camelToTitle('firstName')).toBe('First Name');
      expect(camelToTitle('lastName')).toBe('Last Name');
      expect(camelToTitle('phoneNumber')).toBe('Phone Number');
    });

    it('should truncate strings correctly', () => {
      expect(truncate('Hello World', 5)).toBe('He...');
      expect(truncate('Short', 10)).toBe('Short');
      expect(truncate('ExactlyTen', 10)).toBe('ExactlyTen');
      expect(truncate('Exactly Ten', 10)).toBe('Exactly...');
    });

    it('should get initials from names', () => {
      expect(getInitials('John Doe')).toBe('JD');
      expect(getInitials('Mary Jane Watson')).toBe('MJ');
      expect(getInitials('SingleName')).toBe('S');
    });

    it('should format student names', () => {
      expect(formatStudentName('John', 'Doe')).toBe('John Doe');
      expect(formatStudentName('Mary', '')).toBe('Mary');
      expect(formatStudentName('', 'Smith')).toBe('Smith');
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format 10-digit phone numbers', () => {
      expect(formatPhoneNumber('1234567890')).toBe('(*************');
      expect(formatPhoneNumber('************')).toBe('(*************');
    });

    it('should return original for non-10-digit numbers', () => {
      expect(formatPhoneNumber('123456789')).toBe('123456789');
      expect(formatPhoneNumber('******-123-4567')).toBe('******-123-4567');
    });
  });

  describe('calculatePercentage', () => {
    it('should calculate percentages correctly', () => {
      expect(calculatePercentage(25, 100)).toBe(25);
      expect(calculatePercentage(1, 3)).toBe(33);
      expect(calculatePercentage(2, 3)).toBe(67);
    });

    it('should handle zero total', () => {
      expect(calculatePercentage(5, 0)).toBe(0);
    });
  });

  describe('getStatusColor', () => {
    it('should return correct colors for student statuses', () => {
      expect(getStatusColor('active')).toContain('text-green-600');
      expect(getStatusColor('inactive')).toContain('text-gray-600');
      expect(getStatusColor('graduated')).toContain('text-blue-600');
      expect(getStatusColor('dropped')).toContain('text-red-600');
    });

    it('should return correct colors for lead statuses', () => {
      expect(getStatusColor('new')).toContain('text-blue-600');
      expect(getStatusColor('interested')).toContain('text-green-600');
      expect(getStatusColor('rejected')).toContain('text-red-600');
    });

    it('should return default color for unknown status', () => {
      expect(getStatusColor('unknown')).toContain('text-gray-600');
    });
  });

  describe('calculateAge', () => {
    it('should calculate age correctly', () => {
      const birthDate = new Date('1990-01-01');
      const age = calculateAge(birthDate);
      
      expect(age).toBeGreaterThan(30);
      expect(age).toBeLessThan(40);
    });

    it('should handle birthday not yet occurred this year', () => {
      const today = new Date();
      const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
      const birthDate = new Date(today.getFullYear() - 25, nextMonth.getMonth(), nextMonth.getDate());
      
      const age = calculateAge(birthDate);
      expect(age).toBe(24); // Should be 24 if birthday hasn't occurred yet
    });
  });

  describe('URL Parameter Parsing', () => {
    it('should parse pagination parameters correctly', () => {
      const searchParams = new URLSearchParams('page=2&limit=50&sortBy=name&sortOrder=asc');
      const result = parsePaginationParams(searchParams);
      
      expect(result.page).toBe(2);
      expect(result.limit).toBe(50);
      expect(result.sortBy).toBe('name');
      expect(result.sortOrder).toBe('asc');
    });

    it('should use default pagination values', () => {
      const searchParams = new URLSearchParams();
      const result = parsePaginationParams(searchParams);
      
      expect(result.page).toBe(1);
      expect(result.limit).toBe(20);
      expect(result.sortBy).toBeUndefined();
      expect(result.sortOrder).toBe('desc');
    });

    it('should enforce pagination limits', () => {
      const searchParams = new URLSearchParams('page=0&limit=200');
      const result = parsePaginationParams(searchParams);
      
      expect(result.page).toBe(1); // Minimum 1
      expect(result.limit).toBe(100); // Maximum 100
    });

    it('should parse filter parameters correctly', () => {
      const searchParams = new URLSearchParams('search=john&status=active&dateFrom=2024-01-01');
      const result = parseFilterParams(searchParams);
      
      expect(result.search).toBe('john');
      expect(result.status).toBe('active');
      expect(result.dateFrom).toBe('2024-01-01');
    });
  });
});

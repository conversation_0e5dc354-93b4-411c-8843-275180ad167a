/**
 * Payments Integration Tests
 * End-to-end tests for payment workflows
 */

import { NextRequest } from 'next/server';
import { GET, POST } from '../../src/app/api/payments/route';
import { GET as GetPayment, PUT, DELETE } from '../../src/app/api/payments/[id]/route';
// Using real database connections and services - no mocking
import { query } from '@/lib/db';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { logPaymentOperation } from '@/lib/activity-logger';
import { createTestUser, createTestPayment, cleanupTestData } from '../utils/test-helpers';

describe('Payments Integration Tests', () => {
  let testUser: any;
  let testPayment: any;

  beforeAll(async () => {
    // Create real test user and payment data
    testUser = await createTestUser({
      role: 'admin',
      email: `test-payments-${Date.now()}@example.com`
    });
  });

  beforeEach(async () => {
    jest.clearAllMocks();

    // Create fresh test payment for each test
    testPayment = await createTestPayment({}, testUser.id);
  });

  afterAll(async () => {
    // Clean up test data
    await cleanupTestData();
  });

  describe('Payment CRUD Operations', () => {
    it('should complete full payment lifecycle', async () => {
      // 1. Create payment
      const createData = {
        studentId: 'STU001',
        amount: 150.00,
        paymentType: 'tuition',
        paymentMethod: 'card',
        description: 'Monthly tuition payment',
        startDate: '2024-01-15',
        endDate: '2024-02-15',
      };

      // Mock student record upsert (first call)
      mockQuery.mockResolvedValueOnce({ rows: [{ id: 'student-record-id' }] });
      // Mock payment creation (second call)
      mockQuery.mockResolvedValueOnce({ rows: [{ ...testPayment, ...createData }] });

      const createRequest = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(createData),
        headers: { 'Content-Type': 'application/json' },
      });

      const createResponse = await POST(createRequest);
      const createResult = await createResponse.json();

      expect(createResponse.status).toBe(201);
      expect(createResult.success).toBe(true);
      expect(createResult.data.studentId).toBe(createData.studentId);

      // 2. Read payment
      mockQuery.mockResolvedValueOnce({ rows: [createResult.data] });

      const readRequest = new NextRequest(`http://localhost:3000/api/payments/${createResult.data.id}`);
      const readResponse = await GetPayment(readRequest, { params: { id: createResult.data.id } });
      const readResult = await readResponse.json();

      expect(readResponse.status).toBe(200);
      expect(readResult.success).toBe(true);
      expect(readResult.data.id).toBe(createResult.data.id);

      // 3. Update payment
      const updateData = {
        description: 'Updated payment description',
        amount: 175.00,
      };

      mockQuery
        .mockResolvedValueOnce({ rows: [createResult.data] }) // Current payment
        .mockResolvedValueOnce({ rows: [{ ...createResult.data, ...updateData }] }); // Updated payment

      const updateRequest = new NextRequest(`http://localhost:3000/api/payments/${createResult.data.id}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      const updateResponse = await PUT(updateRequest, { params: { id: createResult.data.id } });
      const updateResult = await updateResponse.json();

      expect(updateResponse.status).toBe(200);
      expect(updateResult.success).toBe(true);
      expect(updateResult.data.description).toBe(updateData.description);
      expect(updateResult.data.amount).toBe(updateData.amount);

      // 4. Delete payment
      mockQuery
        .mockResolvedValueOnce({ rows: [updateResult.data] }) // Current payment
        .mockResolvedValueOnce({ rows: [{ ...updateResult.data, status: 'cancelled' }] }); // Cancelled payment

      const deleteRequest = new NextRequest(`http://localhost:3000/api/payments/${createResult.data.id}`, {
        method: 'DELETE',
      });

      const deleteResponse = await DELETE(deleteRequest, { params: { id: createResult.data.id } });
      const deleteResult = await deleteResponse.json();

      expect(deleteResponse.status).toBe(200);
      expect(deleteResult.success).toBe(true);

      // Verify activity logging was called for each operation
      expect(mockLogPaymentOperation).toHaveBeenCalledTimes(4); // Create, Read, Update, Delete
    });

    it('should handle payment list with filters and pagination', async () => {
      const payments = [
        mockPayment({ id: '1', studentId: 'STU001', status: 'completed' }),
        mockPayment({ id: '2', studentId: 'STU002', status: 'pending' }),
        mockPayment({ id: '3', studentId: 'STU001', status: 'completed' }),
      ];

      // Test without filters
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '3' }] })
        .mockResolvedValueOnce({ rows: payments });

      const listRequest = new NextRequest('http://localhost:3000/api/payments');
      const listResponse = await GET(listRequest);
      const listResult = await listResponse.json();

      expect(listResponse.status).toBe(200);
      expect(listResult.data.data).toHaveLength(3);
      expect(listResult.data.pagination.total).toBe(3);

      // Test with student filter
      const filteredPayments = payments.filter(p => p.studentId === 'STU001');
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '2' }] })
        .mockResolvedValueOnce({ rows: filteredPayments });

      const filteredRequest = new NextRequest('http://localhost:3000/api/payments?studentId=STU001');
      const filteredResponse = await GET(filteredRequest);
      const filteredResult = await filteredResponse.json();

      expect(filteredResponse.status).toBe(200);
      expect(filteredResult.data.data).toHaveLength(2);
      expect(filteredResult.data.data.every((p: any) => p.studentId === 'STU001')).toBe(true);

      // Test with status filter
      const completedPayments = payments.filter(p => p.status === 'completed');
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '2' }] })
        .mockResolvedValueOnce({ rows: completedPayments });

      const statusRequest = new NextRequest('http://localhost:3000/api/payments?status=completed');
      const statusResponse = await GET(statusRequest);
      const statusResult = await statusResponse.json();

      expect(statusResponse.status).toBe(200);
      expect(statusResult.data.data).toHaveLength(2);
      expect(statusResult.data.data.every((p: any) => p.status === 'completed')).toBe(true);
    });

    it('should handle pagination correctly', async () => {
      const allPayments = Array.from({ length: 25 }, (_, i) => 
        mockPayment({ id: `payment-${i + 1}` })
      );

      // Page 1 (first 10)
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '25' }] })
        .mockResolvedValueOnce({ rows: allPayments.slice(0, 10) });

      const page1Request = new NextRequest('http://localhost:3000/api/payments?page=1&limit=10');
      const page1Response = await GET(page1Request);
      const page1Result = await page1Response.json();

      expect(page1Response.status).toBe(200);
      expect(page1Result.data.data).toHaveLength(10);
      expect(page1Result.data.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: false,
      });

      // Page 2 (next 10)
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '25' }] })
        .mockResolvedValueOnce({ rows: allPayments.slice(10, 20) });

      const page2Request = new NextRequest('http://localhost:3000/api/payments?page=2&limit=10');
      const page2Response = await GET(page2Request);
      const page2Result = await page2Response.json();

      expect(page2Response.status).toBe(200);
      expect(page2Result.data.data).toHaveLength(10);
      expect(page2Result.data.pagination).toEqual({
        page: 2,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });

      // Page 3 (last 5)
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '25' }] })
        .mockResolvedValueOnce({ rows: allPayments.slice(20, 25) });

      const page3Request = new NextRequest('http://localhost:3000/api/payments?page=3&limit=10');
      const page3Response = await GET(page3Request);
      const page3Result = await page3Response.json();

      expect(page3Response.status).toBe(200);
      expect(page3Result.data.data).toHaveLength(5);
      expect(page3Result.data.pagination).toEqual({
        page: 3,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: false,
        hasPrev: true,
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors', async () => {
      mockQuery.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/payments');
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(500);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Failed to fetch payments');
    });

    it('should handle invalid payment data', async () => {
      const invalidData = {
        studentId: '', // Empty student ID
        amount: -100, // Negative amount
        paymentType: 'invalid_type',
        paymentMethod: 'invalid_method',
      };

      const request = new NextRequest('http://localhost:3000/api/payments', {
        method: 'POST',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required fields');
    });

    it('should handle unauthorized access', async () => {
      mockGetUserFromRequest.mockResolvedValue({
        success: false,
        user: null,
      });

      const request = new NextRequest('http://localhost:3000/api/payments');
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(401);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Not authenticated');
    });

    it('should handle insufficient permissions', async () => {
      mockHasPermission.mockReturnValue(false);

      const request = new NextRequest('http://localhost:3000/api/payments');
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(403);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Insufficient permissions');
    });
  });

  describe('Business Logic Validation', () => {
    it('should enforce payment amount limits', async () => {
      const testCases = [
        { amount: 0.50, shouldFail: true, error: 'Amount must be between 1 and 99999999 UZS' },
        { amount: 1.00, shouldFail: false },
        { amount: 50000.00, shouldFail: false },
        { amount: 100000000.00, shouldFail: true, error: 'Amount must be between 1 and 99999999 UZS' },
      ];

      for (const testCase of testCases) {
        const paymentData = {
          studentId: 'STU001',
          amount: testCase.amount,
          paymentType: 'tuition',
          paymentMethod: 'card',
          startDate: '2024-01-15',
          endDate: '2024-02-15',
        };

        if (!testCase.shouldFail) {
          mockQuery.mockResolvedValueOnce({ rows: [mockPayment(paymentData)] });
        }

        const request = new NextRequest('http://localhost:3000/api/payments', {
          method: 'POST',
          body: JSON.stringify(paymentData),
          headers: { 'Content-Type': 'application/json' },
        });

        const response = await POST(request);
        const result = await response.json();

        if (testCase.shouldFail) {
          expect(response.status).toBe(400);
          expect(result.success).toBe(false);
          expect(result.error).toContain(testCase.error);
        } else {
          expect(response.status).toBe(201);
          expect(result.success).toBe(true);
        }
      }
    });

    it('should validate payment date constraints', async () => {
      const testCases = [
        {
          startDate: '2024-01-15',
          endDate: '2024-02-15',
          shouldFail: false
        },
        {
          startDate: '2024-02-15',
          endDate: '2024-01-15',
          shouldFail: true,
          error: 'End date must be after start date'
        },
        {
          startDate: '2024-01-15',
          endDate: '2024-01-15',
          shouldFail: true,
          error: 'End date must be after start date'
        },
      ];

      for (const testCase of testCases) {
        const paymentData = {
          studentId: 'STU001',
          amount: 150.00,
          paymentType: 'tuition',
          paymentMethod: 'card',
          startDate: testCase.startDate,
          endDate: testCase.endDate,
        };

        if (!testCase.shouldFail) {
          mockQuery.mockResolvedValueOnce({ rows: [mockPayment(paymentData)] });
        }

        const request = new NextRequest('http://localhost:3000/api/payments', {
          method: 'POST',
          body: JSON.stringify(paymentData),
          headers: { 'Content-Type': 'application/json' },
        });

        const response = await POST(request);
        const result = await response.json();

        if (testCase.shouldFail) {
          expect(response.status).toBe(400);
          expect(result.success).toBe(false);
          expect(result.error).toContain(testCase.error);
        } else {
          expect(response.status).toBe(201);
          expect(result.success).toBe(true);
        }
      }
    });
  });
});

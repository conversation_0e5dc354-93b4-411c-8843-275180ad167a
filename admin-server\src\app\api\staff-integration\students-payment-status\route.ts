/**
 * Staff Integration API - Students Payment Status
 * Provides payment status information for multiple students to staff server
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { query } from '@/lib/db';

interface StudentsPaymentStatusRequest {
  studentIds: string[];
}

interface PaymentStatusResponse {
  [studentId: string]: {
    paymentStatus: 'paid' | 'unpaid' | 'debt';
    debtAmount?: number;
    lastPaymentDate?: string;
  };
}

// POST /api/staff-integration/students-payment-status
export async function POST(request: NextRequest) {
  try {
    // Verify API key for staff server integration
    const apiKey = request.headers.get('X-API-Key');
    const sourceService = request.headers.get('X-Source-Service');
    
    if (!apiKey || apiKey !== process.env.STAFF_SERVER_API_KEY) {
      return createErrorResponse('Invalid API key', 401);
    }

    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 403);
    }

    const body: StudentsPaymentStatusRequest = await request.json();

    // Validate required fields
    const validation = validateRequiredFields(body, ['studentIds']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const { studentIds } = body;

    if (!Array.isArray(studentIds) || studentIds.length === 0) {
      return createErrorResponse('studentIds must be a non-empty array', 400);
    }

    // Get payment status for all requested students
    const paymentStatusMap: PaymentStatusResponse = {};

    // Query student records and recent payments
    const placeholders = studentIds.map((_, index) => `$${index + 1}`).join(',');
    const sql = `
      SELECT 
        sr.staff_student_id,
        sr.total_debt,
        sr.last_payment_date,
        CASE 
          WHEN sr.total_debt > 0 THEN 'debt'
          WHEN sr.last_payment_date IS NOT NULL AND sr.last_payment_date >= CURRENT_DATE - INTERVAL '30 days' THEN 'paid'
          ELSE 'unpaid'
        END as payment_status
      FROM student_records sr
      WHERE sr.staff_student_id = ANY($1)
    `;

    const result = await query(sql, [studentIds]);

    // Process results
    for (const row of result.rows) {
      paymentStatusMap[row.staff_student_id] = {
        paymentStatus: row.payment_status,
        debtAmount: row.total_debt > 0 ? parseFloat(row.total_debt) : undefined,
        lastPaymentDate: row.last_payment_date || undefined,
      };
    }

    // For students not found in student_records, mark as unpaid
    for (const studentId of studentIds) {
      if (!paymentStatusMap[studentId]) {
        paymentStatusMap[studentId] = {
          paymentStatus: 'unpaid',
        };
      }
    }

    return createResponse(
      paymentStatusMap,
      true,
      'Students payment status retrieved successfully'
    );

  } catch (error) {
    console.error('Error getting students payment status:', error);
    return createErrorResponse('Failed to get students payment status', 500);
  }
}

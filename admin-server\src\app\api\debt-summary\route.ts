/**
 * Debt Summary API
 * Provides summary statistics about student debt
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';

interface DebtSummary {
  totalStudentsWithDebt: number;
  totalDebtAmount: number;
  averageDebt: number;
}

// GET /api/debt-summary - Get debt summary statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'payments', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    // Get debt summary from student_records table
    const sql = `
      SELECT 
        COUNT(*) FILTER (WHERE total_debt > 0) as students_with_debt,
        COALESCE(SUM(total_debt), 0) as total_debt,
        COALESCE(AVG(total_debt) FILTER (WHERE total_debt > 0), 0) as average_debt
      FROM student_records
    `;

    const result = await query(sql);
    const row = result.rows[0];

    const debtSummary: DebtSummary = {
      totalStudentsWithDebt: parseInt(row.students_with_debt) || 0,
      totalDebtAmount: parseFloat(row.total_debt) || 0,
      averageDebt: parseFloat(row.average_debt) || 0
    };

    return createResponse(debtSummary, true, 'Debt summary retrieved successfully');

  } catch (error) {
    console.error('Error getting debt summary:', error);
    return createErrorResponse('Failed to get debt summary', 500);
  }
}

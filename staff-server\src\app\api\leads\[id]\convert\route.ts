/**
 * Lead conversion endpoint
 * Converts a lead to a student and updates lead status
 */

import { NextRequest } from 'next/server';
import { query, transaction } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  isValidUUID
} from '@/lib/utils';
import { logLeadOperation, logStudentOperation, getRequestContext } from '@/lib/activity-logger';
import { adminService, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole } from '@/shared/types/common';

interface Lead {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  source?: string;
  status: string;
  assigned_to?: string;
  created_at: Date;
  updated_at: Date;
}

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  date_of_birth?: Date;
  enrollment_date: Date;
  status: string;
  created_at: Date;
  updated_at: Date;
}

// POST /api/leads/[id]/convert - Convert lead to student
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid lead ID format', 400);
    }

    // Check permissions - management and reception can convert leads
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { 
      dateOfBirth, 
      enrollmentDate = new Date().toISOString().split('T')[0] 
    } = body;

    // Validate dates
    if (dateOfBirth && new Date(dateOfBirth) > new Date()) {
      return createErrorResponse('Date of birth cannot be in the future', 400);
    }

    if (enrollmentDate && new Date(enrollmentDate) > new Date()) {
      return createErrorResponse('Enrollment date cannot be in the future', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Use transaction to ensure data consistency
      const result = await transaction(async (client) => {
        // Get lead details
        const leadResult = await client.query<Lead>(
          `SELECT id, first_name, last_name, email, phone, source, 
                  status, assigned_to, created_at, updated_at 
           FROM leads WHERE id = $1`,
          [id]
        );

        if (leadResult.rows.length === 0) {
          throw new Error('Lead not found');
        }

        const lead = leadResult.rows[0];

        // Check if lead can be converted
        if (lead.status === 'enrolled') {
          throw new Error('Lead has already been converted to student');
        }

        if (lead.status === 'rejected') {
          throw new Error('Cannot convert rejected lead');
        }

        // Check if student with same email already exists (if email provided)
        if (lead.email) {
          const existingStudentResult = await client.query(
            'SELECT id FROM students WHERE email = $1',
            [lead.email]
          );

          if (existingStudentResult.rows.length > 0) {
            throw new Error('Student with this email already exists');
          }
        }

        // Create student from lead data
        const studentResult = await client.query<Student>(
          `INSERT INTO students (first_name, last_name, email, phone, date_of_birth, enrollment_date, status)
           VALUES ($1, $2, $3, $4, $5, $6, $7)
           RETURNING id, first_name, last_name, email, phone, date_of_birth, enrollment_date, status, created_at, updated_at`,
          [
            lead.first_name,
            lead.last_name,
            lead.email,
            lead.phone,
            dateOfBirth || null,
            enrollmentDate,
            'active'
          ]
        );

        const newStudent = studentResult.rows[0];

        // Update lead status to enrolled
        const updatedLeadResult = await client.query<Lead>(
          `UPDATE leads 
           SET status = 'enrolled', updated_at = CURRENT_TIMESTAMP
           WHERE id = $1
           RETURNING id, first_name, last_name, email, phone, source, 
                     status, assigned_to, created_at, updated_at`,
          [id]
        );

        const updatedLead = updatedLeadResult.rows[0];

        return { lead, updatedLead, newStudent };
      });

      // Log lead conversion
      await logLeadOperation(
        'UPDATE' as any,
        authResult.user.id,
        result.updatedLead,
        result.lead,
        context
      );

      // Log student creation
      await logStudentOperation(
        'CREATE' as any,
        authResult.user.id,
        result.newStudent,
        undefined,
        context
      );

      // Notify admin server of student enrollment if integration is enabled
      if (isAdminIntegrationEnabled()) {
        await adminService.notifyStudentEnrollment({
          id: result.newStudent.id,
          firstName: result.newStudent.first_name,
          lastName: result.newStudent.last_name,
          email: result.newStudent.email || undefined,
          phone: result.newStudent.phone || undefined,
          enrollmentDate: result.newStudent.enrollment_date.toISOString().split('T')[0]
        });
      }

      return createResponse({
        lead: {
          id: result.updatedLead.id,
          firstName: result.updatedLead.first_name,
          lastName: result.updatedLead.last_name,
          email: result.updatedLead.email,
          phone: result.updatedLead.phone,
          source: result.updatedLead.source,
          status: result.updatedLead.status,
          assignedTo: result.updatedLead.assigned_to,
          createdAt: result.updatedLead.created_at,
          updatedAt: result.updatedLead.updated_at
        },
        student: {
          id: result.newStudent.id,
          firstName: result.newStudent.first_name,
          lastName: result.newStudent.last_name,
          email: result.newStudent.email,
          phone: result.newStudent.phone,
          dateOfBirth: result.newStudent.date_of_birth,
          enrollmentDate: result.newStudent.enrollment_date,
          status: result.newStudent.status,
          createdAt: result.newStudent.created_at,
          updatedAt: result.newStudent.updated_at
        }
      }, true, 'Lead converted to student successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error converting lead:', dbError);
      if (dbError instanceof Error) {
        return createErrorResponse(dbError.message, 400);
      }
      return createErrorResponse('Failed to convert lead', 500);
    }

  } catch (error) {
    console.error('Convert lead error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

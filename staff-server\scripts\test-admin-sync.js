/**
 * Test admin server activity sync directly
 */

require('dotenv').config({ path: '.env.local' });

async function testAdminSync() {
  console.log('🔍 Testing Admin Server Activity Sync...');
  
  const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
  const apiKey = process.env.ADMIN_SERVER_API_KEY;
  
  console.log('🔗 Admin Server URL:', adminServerUrl);
  console.log('🔑 API Key configured:', !!apiKey);
  
  if (!apiKey) {
    console.error('❌ ADMIN_SERVER_API_KEY not configured');
    return;
  }

  // Test data
  const testActivityData = {
    userId: '00000000-0000-0000-0000-000000000001', // Test user ID
    action: 'CREATE',
    resourceType: 'STUDENT',
    resourceId: '00000000-0000-0000-0000-000000000002',
    description: 'Test activity sync from staff server',
    sourceService: 'staff-server',
    timestamp: new Date().toISOString(),
    newValues: {
      firstName: 'Test',
      lastName: 'Student',
      email: '<EMAIL>'
    }
  };

  try {
    console.log('\n📤 Sending test activity to admin server...');
    
    const response = await fetch(`${adminServerUrl}/api/staff-integration/activity-sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
        'X-Source-Service': 'staff-server',
      },
      body: JSON.stringify(testActivityData),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response status text:', response.statusText);

    const responseText = await response.text();
    console.log('📡 Response body:', responseText);

    if (response.ok) {
      console.log('✅ Activity sync test successful!');
      
      // Try to parse response as JSON
      try {
        const responseData = JSON.parse(responseText);
        console.log('📋 Response data:', responseData);
      } catch (parseError) {
        console.log('⚠️  Response is not JSON');
      }
    } else {
      console.log('❌ Activity sync test failed');
    }

  } catch (error) {
    console.error('❌ Error testing admin sync:', error.message);
    console.error('Error details:', error);
  }

  // Test admin server health
  try {
    console.log('\n🏥 Testing admin server health...');
    const healthResponse = await fetch(`${adminServerUrl}/api/health`);
    console.log('🏥 Health check status:', healthResponse.status);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('🏥 Admin server is healthy:', healthData.data?.status);
    }
  } catch (healthError) {
    console.error('❌ Admin server health check failed:', healthError.message);
  }

  console.log('\n🎉 Admin sync test completed!');
}

// Run the test
testAdminSync();

/**
 * Check database structure for debugging
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function checkDatabaseStructure() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('🔍 Checking Database Structure...');
    
    await client.connect();
    console.log('📡 Database connected successfully');

    // Check activity_logs table structure
    const tableStructure = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'activity_logs'
      ORDER BY ordinal_position
    `);

    console.log('\n📋 Activity logs table structure:');
    tableStructure.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    // Check foreign key constraints
    const constraints = await client.query(`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'activity_logs'
    `);

    console.log('\n🔗 Foreign key constraints:');
    constraints.rows.forEach(constraint => {
      console.log(`  ${constraint.column_name} -> ${constraint.foreign_table_name}.${constraint.foreign_column_name}`);
    });

    // Check if users table exists
    const usersTableExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);

    console.log('\n👥 Users table exists:', usersTableExists.rows[0].exists);

    if (usersTableExists.rows[0].exists) {
      const usersCount = await client.query('SELECT COUNT(*) FROM users');
      console.log('👥 Users count:', usersCount.rows[0].count);
    }

    console.log('\n🎉 Database structure check completed!');

  } catch (error) {
    console.error('❌ Database structure check failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

// Run the check
checkDatabaseStructure();

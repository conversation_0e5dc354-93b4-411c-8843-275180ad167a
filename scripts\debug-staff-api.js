/**
 * Debug script for staff API endpoints
 */

const { default: fetch } = require('node-fetch');

const ADMIN_SERVER_URL = 'http://localhost:3000';

async function getAdminToken() {
  const response = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Admin123!'
    })
  });
  
  const data = await response.json();
  return data.success ? data.data.token : null;
}

async function testStaffEndpoints() {
  console.log('🔍 Testing staff API endpoints...');
  
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ No admin token');
    return;
  }
  
  // Test GET staff users
  console.log('\n1. Testing GET /api/staff-integration/users');
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    console.log('Status:', response.status);
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
  
  // Test POST staff user
  console.log('\n2. Testing POST /api/staff-integration/users');
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123!',
        role: 'reception',
        name: 'Debug Staff User',
        isActive: true
      })
    });
    
    console.log('Status:', response.status);
    const data = await response.json();
    console.log('Response:', JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testStaffEndpoints();

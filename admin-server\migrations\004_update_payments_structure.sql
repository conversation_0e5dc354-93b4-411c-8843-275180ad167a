-- Migration: Update payments table structure for new payment system
-- Date: 2024-01-20
-- Description: Add start/end dates, debt tracking, and remove payment_date

BEGIN;

-- Add new columns to payments table
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS start_date DATE,
ADD COLUMN IF NOT EXISTS end_date DATE,
ADD COLUMN IF NOT EXISTS debt_amount DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_debt_payment BOOLEAN DEFAULT FALSE;

-- Update existing records to have start_date and end_date based on payment_date
UPDATE payments 
SET 
  start_date = payment_date::DATE,
  end_date = (payment_date::DATE + INTERVAL '30 days')::DATE
WHERE start_date IS NULL;

-- Make start_date and end_date NOT NULL after updating existing records
ALTER TABLE payments
ALTER COLUMN start_date SET NOT NULL,
ALTER COLUMN end_date SET NOT NULL;

-- Drop the view that depends on payment_date column
DROP VIEW IF EXISTS monthly_payment_summary;

-- Drop the old payment_date column since we now use start_date and end_date
ALTER TABLE payments DROP COLUMN IF EXISTS payment_date;

-- Recreate the view with the new column structure
CREATE VIEW monthly_payment_summary AS
SELECT
    DATE_TRUNC('month', start_date) as month,
    payment_type,
    payment_method,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount
FROM payments
WHERE status = 'completed'
GROUP BY DATE_TRUNC('month', start_date), payment_type, payment_method
ORDER BY month DESC;

-- Create student_records table if it doesn't exist
CREATE TABLE IF NOT EXISTS student_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_student_id UUID UNIQUE NOT NULL,
  first_name VARCHAR(100) NOT NULL DEFAULT 'Unknown',
  last_name VARCHAR(100) NOT NULL DEFAULT 'Student',
  phone VARCHAR(20),
  enrollment_date DATE,
  status VARCHAR(50) DEFAULT 'active',
  total_debt DECIMAL(12,0) DEFAULT 0,
  last_payment_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_student_records_staff_student_id ON student_records(staff_student_id);
CREATE INDEX IF NOT EXISTS idx_payments_student_id ON payments(student_id);
CREATE INDEX IF NOT EXISTS idx_payments_start_date ON payments(start_date);
CREATE INDEX IF NOT EXISTS idx_payments_end_date ON payments(end_date);
CREATE INDEX IF NOT EXISTS idx_payments_is_debt_payment ON payments(is_debt_payment);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to student_records table
DROP TRIGGER IF EXISTS update_student_records_updated_at ON student_records;
CREATE TRIGGER update_student_records_updated_at
    BEFORE UPDATE ON student_records
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Migrate existing payment data to student_records
INSERT INTO student_records (staff_student_id, first_name, last_name, last_payment_date, total_debt)
SELECT
  p.student_id,
  'Unknown', -- Default first name
  'Student', -- Default last name
  MAX(p.start_date),
  0 -- Initialize debt as 0
FROM payments p
WHERE NOT EXISTS (
  SELECT 1 FROM student_records sr WHERE sr.staff_student_id = p.student_id
)
GROUP BY p.student_id
ON CONFLICT (staff_student_id) DO NOTHING;

-- Add comments to document the new structure
COMMENT ON COLUMN payments.start_date IS 'Start date of the payment period';
COMMENT ON COLUMN payments.end_date IS 'End date of the payment period';
COMMENT ON COLUMN payments.debt_amount IS 'Amount of debt being paid (if applicable)';
COMMENT ON COLUMN payments.is_debt_payment IS 'Whether this payment includes debt payment';

COMMENT ON TABLE student_records IS 'Student records maintained by admin server for payment tracking';
COMMENT ON COLUMN student_records.staff_student_id IS 'Student ID from staff server';
COMMENT ON COLUMN student_records.first_name IS 'Student first name cached from staff server';
COMMENT ON COLUMN student_records.last_name IS 'Student last name cached from staff server';
COMMENT ON COLUMN student_records.total_debt IS 'Current total debt amount';
COMMENT ON COLUMN student_records.last_payment_date IS 'Date of last payment made';

COMMIT;

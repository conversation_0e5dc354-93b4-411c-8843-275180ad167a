/**
 * Activity logging implementation for Staff Server
 * Handles comprehensive audit trail for all staff operational actions
 */

import { query } from './db';
import {
  ActivityAction,
  ResourceType,
  CreateActivityLogRequest,
  ActivityContext,
  generateActivityDescription,
  sanitizeLogData
} from '../../../shared/types/activity-log';
import {
  ActivityLogger,
  LogActivityParams,
  LogUserActionParams,
  LogSystemActionParams,
  ActivityLoggerConfig
} from '../../../shared/utils/activity-logger';
import { adminService, isAdminIntegrationEnabled } from './admin-service';

// Staff server specific activity logger configuration
const STAFF_LOGGER_CONFIG: ActivityLoggerConfig = {
  enabled: process.env.ENABLE_ACTIVITY_LOGGING === 'true',
  sanitizeSensitiveData: true,
  maxLogSize: 1000000, // 1MB
  retentionDays: parseInt(process.env.LOG_RETENTION_DAYS || '365'),
  batchSize: 50,
  flushInterval: 3000 // 3 seconds
};

/**
 * Persist activity log to database and sync with admin server
 */
async function persistActivityLog(log: CreateActivityLogRequest): Promise<void> {
  try {
    console.log('📝 Persisting activity log:', {
      userId: log.userId,
      action: log.action,
      resourceType: log.resourceType,
      description: log.description
    });

    // For staff server, we primarily sync to admin server instead of storing locally
    // This avoids foreign key issues with admin server user IDs

    // Sync with admin server if integration is enabled
    if (isAdminIntegrationEnabled()) {
      console.log('🔄 Syncing activity log to admin server...');
      try {
        await adminService.syncActivityLog({
          userId: log.userId,
          action: log.action,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          oldValues: log.oldValues,
          newValues: log.newValues,
          description: log.description,
          ipAddress: log.ipAddress,
          userAgent: log.userAgent,
        });
        console.log('✅ Activity log synced to admin server successfully');
      } catch (syncError) {
        console.error('❌ Failed to sync activity log to admin server:', syncError);
        // Continue to try local storage
      }
    } else {
      console.log('⚠️  Admin integration not enabled - skipping sync');
    }

    // Try to store locally, but don't fail if user doesn't exist in staff DB
    try {
      console.log('💾 Attempting to store activity log locally...');
      const sql = `
        INSERT INTO activity_logs (
          user_id, action, resource_type, resource_id,
          old_values, new_values, ip_address, user_agent, description
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      `;

      const params = [
        log.userId,
        log.action,
        log.resourceType,
        log.resourceId || null,
        log.oldValues ? JSON.stringify(log.oldValues) : null,
        log.newValues ? JSON.stringify(log.newValues) : null,
        log.ipAddress || null,
        log.userAgent || null,
        log.description || null
      ];

      await query(sql, params);
      console.log('✅ Activity log stored locally successfully');
    } catch (localError: any) {
      // If it's a foreign key constraint error, just log it and continue
      if (localError.code === '23503') {
        console.log('⚠️  Skipping local activity log storage - user not in staff database');
      } else {
        console.error('❌ Failed to store activity log locally:', localError);
      }
    }
  } catch (error) {
    console.error('❌ Failed to persist staff activity log:', error);
    // Don't throw error to avoid breaking the main operation
  }
}

// Create staff server activity logger instance
export const staffActivityLogger = new ActivityLogger(
  persistActivityLog,
  STAFF_LOGGER_CONFIG
);

/**
 * Helper function to get client IP and user agent from request headers
 */
export function getRequestContext(headers: Headers): {
  ipAddress?: string;
  userAgent?: string;
} {
  const xForwardedFor = headers.get('x-forwarded-for');
  const xRealIP = headers.get('x-real-ip');
  const userAgent = headers.get('user-agent');
  
  let ipAddress: string | undefined;
  
  if (xForwardedFor) {
    ipAddress = xForwardedFor.split(',')[0].trim();
  } else if (xRealIP) {
    ipAddress = xRealIP;
  }
  
  return {
    ipAddress,
    userAgent: userAgent || undefined
  };
}

/**
 * Log user authentication events
 */
export async function logAuthEvent(
  action: 'LOGIN' | 'LOGOUT',
  userId: string,
  context: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  await staffActivityLogger.logUserAction({
    userId,
    action: action === 'LOGIN' ? ActivityAction.LOGIN : ActivityAction.LOGOUT,
    resourceType: ResourceType.USER,
    resourceId: userId,
    description: action === 'LOGIN' ? 'Staff user logged in' : 'Staff user logged out',
    ipAddress: context.ipAddress,
    userAgent: context.userAgent
  });
}

/**
 * Log user management operations
 */
export async function logUserOperation(
  action: ActivityAction,
  performedBy: string,
  targetUser: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  let newValues: any = undefined;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Created staff user: ${targetUser.email}`;
      newValues = sanitizeLogData(targetUser);
      break;
    case ActivityAction.UPDATE:
      description = `Updated staff user: ${targetUser.email}`;
      newValues = sanitizeLogData(targetUser);
      break;
    case ActivityAction.DELETE:
      description = `Deactivated staff user: ${targetUser.email}`;
      break;
    default:
      description = `${action} operation on staff user: ${targetUser.email}`;
  }
  
  await staffActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.USER,
    resourceId: targetUser.id,
    oldValues: oldValues ? sanitizeLogData(oldValues) : undefined,
    newValues,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log student operations
 */
export async function logStudentOperation(
  action: ActivityAction,
  performedBy: string,
  student: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  // Handle both camelCase and snake_case property names
  const firstName = student.firstName || student.first_name;
  const lastName = student.lastName || student.last_name;
  const studentName = `${firstName} ${lastName}`;

  switch (action) {
    case ActivityAction.CREATE:
      description = `Enrolled new student: ${studentName}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated student: ${studentName}`;
      break;
    case ActivityAction.DELETE:
      description = `Deactivated student: ${studentName}`;
      break;
    default:
      description = `${action} operation on student: ${studentName}`;
  }

  await staffActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.STUDENT,
    resourceId: student.id,
    oldValues,
    newValues: student,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log lead operations
 */
export async function logLeadOperation(
  action: ActivityAction,
  performedBy: string,
  lead: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  // Handle both camelCase and snake_case property names
  const firstName = lead.firstName || lead.first_name;
  const lastName = lead.lastName || lead.last_name;
  const leadName = `${firstName} ${lastName}`;

  switch (action) {
    case ActivityAction.CREATE:
      description = `Created new lead: ${leadName}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated lead: ${leadName} (status: ${lead.status})`;
      break;
    case ActivityAction.DELETE:
      description = `Deleted lead: ${leadName}`;
      break;
    default:
      description = `${action} operation on lead: ${leadName}`;
  }

  await staffActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.LEAD,
    resourceId: lead.id,
    oldValues,
    newValues: lead,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log group operations
 */
export async function logGroupOperation(
  action: ActivityAction,
  performedBy: string,
  group: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Created new group: ${group.name}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated group: ${group.name}`;
      break;
    case ActivityAction.DELETE:
      description = `Deactivated group: ${group.name}`;
      break;
    default:
      description = `${action} operation on group: ${group.name}`;
  }
  
  await staffActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.GROUP,
    resourceId: group.id,
    oldValues,
    newValues: group,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log report generation and export operations
 */
export async function logReportOperation(
  action: 'EXPORT' | 'VIEW',
  performedBy: string,
  reportType: string,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  const description = action === 'EXPORT' 
    ? `Exported ${reportType} report` 
    : `Viewed ${reportType} report`;
  
  await staffActivityLogger.logUserAction({
    userId: performedBy,
    action: action === 'EXPORT' ? ActivityAction.EXPORT : ActivityAction.VIEW,
    resourceType: 'REPORT' as ResourceType,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Cleanup old activity logs based on retention policy
 */
export async function cleanupOldLogs(): Promise<void> {
  try {
    const retentionDays = STAFF_LOGGER_CONFIG.retentionDays;
    const sql = `
      DELETE FROM activity_logs 
      WHERE timestamp < NOW() - INTERVAL '${retentionDays} days'
    `;
    
    const result = await query(sql);
    console.log(`Cleaned up ${result.rowCount} old staff activity logs`);
  } catch (error) {
    console.error('Failed to cleanup old staff activity logs:', error);
  }
}

// Export the logger instance for direct use
export { staffActivityLogger as activityLogger };

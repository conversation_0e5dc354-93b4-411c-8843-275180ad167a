/**
 * API-related types for the Innovative Centre Platform
 */

import {
  PaymentType,
  PaymentMethod,
  PaymentStatus,
  InvoiceStatus,
  BookingStatus,
  BaseEntity,
  StudentPaymentStatus
} from './common';

// Payment interfaces
export interface Payment extends BaseEntity {
  studentId: string;
  amount: number;
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  description?: string;
  status: PaymentStatus;
  processedBy: string;
  startDate: Date;
  endDate: Date;
  debtAmount?: number;
  isDebtPayment?: boolean;
}

export interface CreatePaymentRequest {
  studentId: string;
  amount: number;
  paymentType: PaymentType;
  paymentMethod: PaymentMethod;
  description?: string;
  startDate: Date;
  endDate: Date;
  debtAmount?: number;
  isDebtPayment?: boolean;
}

export interface UpdatePaymentRequest {
  amount?: number;
  paymentType?: PaymentType;
  paymentMethod?: PaymentMethod;
  description?: string;
  status?: PaymentStatus;
  startDate?: Date;
  endDate?: Date;
  debtAmount?: number;
  isDebtPayment?: boolean;
}

export interface PaymentResponse extends Payment {
  processedByUser?: {
    id: string;
    name: string;
    email: string;
  };
}

// Student interfaces for inter-server communication
export interface StudentBasicInfo {
  id: string;
  firstName: string;
  lastName: string;
  phone?: string;
  paymentStatus: StudentPaymentStatus;
  debtAmount?: number;
  lastPaymentDate?: Date;
}

export interface StudentSearchResponse {
  students: StudentBasicInfo[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Invoice interfaces
export interface Invoice extends BaseEntity {
  studentId: string;
  amount: number;
  dueDate: Date;
  paidDate?: Date;
  status: InvoiceStatus;
  createdBy: string;
}

export interface CreateInvoiceRequest {
  studentId: string;
  amount: number;
  dueDate: Date;
}

export interface UpdateInvoiceRequest {
  amount?: number;
  dueDate?: Date;
  paidDate?: Date;
  status?: InvoiceStatus;
}

export interface InvoiceResponse extends Invoice {
  createdByUser?: {
    id: string;
    name: string;
    email: string;
  };
}

// Cabinet interfaces
export interface Cabinet extends BaseEntity {
  name: string;
  capacity: number;
  equipment: string[];
  hourlyRate?: number;
  isAvailable: boolean;
}

export interface CreateCabinetRequest {
  name: string;
  capacity: number;
  equipment?: string[];
  hourlyRate?: number;
  isAvailable?: boolean;
}

export interface UpdateCabinetRequest {
  name?: string;
  capacity?: number;
  equipment?: string[];
  hourlyRate?: number;
  isAvailable?: boolean;
}

// Cabinet booking interfaces
export interface CabinetBooking extends BaseEntity {
  cabinetId: string;
  date: Date;
  startTime: string;
  endTime: string;
  bookedBy: string;
  purpose?: string;
  status: BookingStatus;
}

export interface CreateBookingRequest {
  cabinetId: string;
  date: Date;
  startTime: string;
  endTime: string;
  purpose?: string;
}

export interface UpdateBookingRequest {
  date?: Date;
  startTime?: string;
  endTime?: string;
  purpose?: string;
  status?: BookingStatus;
}

export interface BookingResponse extends CabinetBooking {
  cabinet?: {
    id: string;
    name: string;
    capacity: number;
  };
  bookedByUser?: {
    id: string;
    name: string;
    email: string;
  };
}

// KPI interfaces
export interface TeacherKPI extends BaseEntity {
  teacherId: string;
  month: Date;
  studentsTaught: number;
  retentionRate: number;
  performanceScore: number;
}

export interface ReceptionKPI extends BaseEntity {
  staffId: string;
  month: Date;
  leadsConverted: number;
  studentsEnrolled: number;
  satisfactionScore: number;
}

export interface CreateTeacherKPIRequest {
  teacherId: string;
  month: Date;
  studentsTaught?: number;
  retentionRate?: number;
  performanceScore?: number;
}

export interface CreateReceptionKPIRequest {
  staffId: string;
  month: Date;
  leadsConverted?: number;
  studentsEnrolled?: number;
  satisfactionScore?: number;
}

// Dashboard data interfaces
export interface DashboardStats {
  totalPayments: number;
  totalRevenue: number;
  pendingInvoices: number;
  activeBookings: number;
  totalUsers: number;
  recentActivity: number;
}

export interface FinancialSummary {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingAmount: number;
  completedPayments: number;
  pendingInvoices: number;
  revenueByType: Record<PaymentType, number>;
  revenueByMethod: Record<PaymentMethod, number>;
}

export interface CabinetUtilization {
  totalCabinets: number;
  availableCabinets: number;
  bookedCabinets: number;
  utilizationRate: number;
  bookingsByStatus: Record<BookingStatus, number>;
  upcomingBookings: BookingResponse[];
}

// Filter interfaces
export interface PaymentFilterParams {
  studentId?: string;
  paymentType?: PaymentType;
  paymentMethod?: PaymentMethod;
  status?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
  processedBy?: string;
}

export interface InvoiceFilterParams {
  studentId?: string;
  status?: InvoiceStatus;
  dueDateFrom?: string;
  dueDateTo?: string;
  amountMin?: number;
  amountMax?: number;
  createdBy?: string;
}

export interface BookingFilterParams {
  cabinetId?: string;
  status?: BookingStatus;
  dateFrom?: string;
  dateTo?: string;
  bookedBy?: string;
}

// Report interfaces
export interface ReportRequest {
  type: 'financial' | 'cabinet' | 'user' | 'activity';
  dateFrom: string;
  dateTo: string;
  format: 'pdf' | 'csv' | 'json';
  filters?: Record<string, any>;
}

export interface ReportResponse {
  id: string;
  type: string;
  status: 'generating' | 'completed' | 'failed';
  downloadUrl?: string;
  createdAt: Date;
  expiresAt: Date;
}

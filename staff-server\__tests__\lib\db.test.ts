/**
 * Database utilities unit tests
 */

import { buildWhereClause, buildOrderByClause, buildPaginationClause } from '../../src/lib/db';

describe('Database Utilities', () => {
  describe('buildWhereClause', () => {
    it('should build WHERE clause with single condition', () => {
      const conditions = { status: 'active' };
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('WHERE status = $1');
      expect(result.params).toEqual(['active']);
      expect(result.nextIndex).toBe(2);
    });

    it('should build WHERE clause with multiple conditions', () => {
      const conditions = { 
        status: 'active', 
        role: 'teacher',
        created_at: '2024-01-01'
      };
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('WHERE status = $1 AND role = $2 AND created_at = $3');
      expect(result.params).toEqual(['active', 'teacher', '2024-01-01']);
      expect(result.nextIndex).toBe(4);
    });

    it('should handle array values for IN clauses', () => {
      const conditions = { status: ['active', 'inactive'] };
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('WHERE status IN ($1, $2)');
      expect(result.params).toEqual(['active', 'inactive']);
      expect(result.nextIndex).toBe(3);
    });

    it('should handle LIKE clauses with % in value', () => {
      const conditions = { name: '%john%' };
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('WHERE name ILIKE $1');
      expect(result.params).toEqual(['%john%']);
      expect(result.nextIndex).toBe(2);
    });

    it('should skip null and undefined values', () => {
      const conditions = { 
        status: 'active', 
        name: null, 
        email: undefined 
      };
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('WHERE status = $1');
      expect(result.params).toEqual(['active']);
      expect(result.nextIndex).toBe(2);
    });

    it('should return empty WHERE clause for empty conditions', () => {
      const conditions = {};
      const result = buildWhereClause(conditions);
      
      expect(result.whereClause).toBe('');
      expect(result.params).toEqual([]);
      expect(result.nextIndex).toBe(1);
    });

    it('should handle custom start index', () => {
      const conditions = { status: 'active' };
      const result = buildWhereClause(conditions, 5);
      
      expect(result.whereClause).toBe('WHERE status = $5');
      expect(result.params).toEqual(['active']);
      expect(result.nextIndex).toBe(6);
    });
  });

  describe('buildOrderByClause', () => {
    it('should build ORDER BY clause with valid column', () => {
      const result = buildOrderByClause('name', 'asc');
      expect(result).toBe('ORDER BY name ASC');
    });

    it('should build ORDER BY clause with desc order', () => {
      const result = buildOrderByClause('created_at', 'desc');
      expect(result).toBe('ORDER BY created_at DESC');
    });

    it('should return default ORDER BY for invalid column', () => {
      const result = buildOrderByClause('invalid_column', 'asc');
      expect(result).toBe('ORDER BY created_at DESC');
    });

    it('should return empty string for no sortBy', () => {
      const result = buildOrderByClause();
      expect(result).toBe('');
    });

    it('should default to asc order', () => {
      const result = buildOrderByClause('name');
      expect(result).toBe('ORDER BY name ASC');
    });
  });

  describe('buildPaginationClause', () => {
    it('should build pagination clause with default values', () => {
      const result = buildPaginationClause();
      
      expect(result.limitClause).toBe('LIMIT 20 OFFSET 0');
      expect(result.offset).toBe(0);
    });

    it('should build pagination clause with custom values', () => {
      const result = buildPaginationClause(3, 10);
      
      expect(result.limitClause).toBe('LIMIT 10 OFFSET 20');
      expect(result.offset).toBe(20);
    });

    it('should calculate offset correctly for different pages', () => {
      const page1 = buildPaginationClause(1, 25);
      const page2 = buildPaginationClause(2, 25);
      const page3 = buildPaginationClause(3, 25);
      
      expect(page1.offset).toBe(0);
      expect(page2.offset).toBe(25);
      expect(page3.offset).toBe(50);
    });

    it('should handle large page numbers', () => {
      const result = buildPaginationClause(100, 50);
      
      expect(result.limitClause).toBe('LIMIT 50 OFFSET 4950');
      expect(result.offset).toBe(4950);
    });
  });
});

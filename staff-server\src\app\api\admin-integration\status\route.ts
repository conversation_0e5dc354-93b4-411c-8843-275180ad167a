/**
 * Admin server integration status endpoint
 * Provides information about the connection to the admin server
 */

import { NextRequest } from 'next/server';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getAdminServerStatus, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - only management can view integration status
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      const integrationEnabled = isAdminIntegrationEnabled();
      const adminStatus = await getAdminServerStatus();

      return createResponse({
        integrationEnabled,
        adminServer: adminStatus,
        configuration: {
          adminServerUrl: process.env.ADMIN_SERVER_URL || 'Not configured',
          apiKeyConfigured: !!(process.env.ADMIN_SERVER_API_KEY),
        },
        features: {
          activitySync: integrationEnabled,
          financialDataAccess: integrationEnabled,
          cabinetBooking: integrationEnabled,
          kpiReporting: integrationEnabled,
          studentStatusSync: integrationEnabled,
        }
      }, true, 'Admin integration status retrieved successfully');

    } catch (error) {
      console.error('Error getting admin integration status:', error);
      return createErrorResponse('Failed to get integration status', 500);
    }

  } catch (error) {
    console.error('Admin integration status error:', error);
    return createErrorResponse('Failed to get integration status', 500);
  }
}

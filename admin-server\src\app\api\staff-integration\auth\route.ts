/**
 * Staff authentication endpoint for admin server
 * Handles authentication requests from staff server
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';
import { verifyPassword, generateTokens } from '@/lib/auth';
import { query } from '@/lib/db';
import { logAuthEvent, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  server_type: string;
}

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const body = await request.json();
    const { email, password } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    const context = getRequestContext(request.headers);

    try {
      // Find staff user by email
      const userResult = await query<User>(
        `SELECT id, email, password_hash, role, name, is_active, server_type 
         FROM users 
         WHERE email = $1 AND server_type = 'staff'`,
        [email.toLowerCase()]
      );

      if (userResult.rows.length === 0) {
        console.log(`Failed staff authentication attempt for email: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      const user = userResult.rows[0];

      // Check if user is active
      if (!user.is_active) {
        console.log(`Authentication attempt for inactive staff user: ${email}`);
        return createErrorResponse('Account is deactivated', 401);
      }

      // Verify role is a staff role
      const staffRoles = [UserRole.MANAGEMENT, UserRole.RECEPTION, UserRole.TEACHER];
      if (!staffRoles.includes(user.role)) {
        console.log(`Authentication attempt for non-staff role: ${email} (${user.role})`);
        return createErrorResponse('Invalid user role for staff server', 403);
      }

      // Verify password
      const isPasswordValid = await verifyPassword(password, user.password_hash);
      if (!isPasswordValid) {
        console.log(`Invalid password for staff user: ${email}`);
        return createErrorResponse('Invalid email or password', 401);
      }

      // Generate JWT tokens
      const tokens = generateTokens({
        id: user.id,
        email: user.email,
        role: user.role
      });

      // Log successful authentication
      await logAuthEvent('LOGIN', user.id, context);

      // Return success response
      return createResponse({
        user: {
          id: user.id,
          email: user.email,
          role: user.role,
          name: user.name,
          isActive: user.is_active
        },
        token: tokens.token,
        refreshToken: tokens.refreshToken,
        expiresIn: tokens.expiresIn
      }, true, 'Staff authentication successful');

    } catch (dbError) {
      console.error('Database error during staff authentication:', dbError);
      return createErrorResponse('Authentication service temporarily unavailable', 503);
    }

  } catch (error) {
    console.error('Staff authentication error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

/**
 * Migration script to update user schema for staff roles support
 * Adds support for management, reception, and teacher roles in admin database
 */

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' });

const { Pool } = require('pg');

// Database configuration
const dbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
};

async function runMigration() {
  const pool = new Pool(dbConfig);
  
  try {
    console.log('🔄 Starting user schema migration...');
    
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected to admin database');
    
    // Check if server_type column already exists
    const columnCheck = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'server_type'
    `);
    
    if (columnCheck.rows.length === 0) {
      console.log('📝 Adding server_type column to users table...');
      
      // Add server_type column
      await client.query(`
        ALTER TABLE users 
        ADD COLUMN server_type VARCHAR(20) DEFAULT 'admin' 
        CHECK (server_type IN ('admin', 'staff'))
      `);
      
      console.log('✅ Added server_type column');
    } else {
      console.log('ℹ️  server_type column already exists');
    }
    
    // Update role constraint to include staff roles
    console.log('📝 Updating role constraint to include staff roles...');
    
    // Drop existing constraint
    await client.query(`
      ALTER TABLE users 
      DROP CONSTRAINT IF EXISTS users_role_check
    `);
    
    // Add new constraint with staff roles
    await client.query(`
      ALTER TABLE users 
      ADD CONSTRAINT users_role_check 
      CHECK (role IN ('admin', 'cashier', 'accountant', 'management', 'reception', 'teacher'))
    `);
    
    console.log('✅ Updated role constraint');
    
    // Update existing users to have server_type = 'admin'
    const updateResult = await client.query(`
      UPDATE users 
      SET server_type = 'admin' 
      WHERE server_type IS NULL OR server_type = 'admin'
    `);
    
    console.log(`✅ Updated ${updateResult.rowCount} existing users with server_type = 'admin'`);
    
    // Create index for server_type if it doesn't exist
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_users_server_type ON users(server_type)
    `);
    
    console.log('✅ Created index for server_type');
    
    client.release();
    await pool.end();
    
    console.log('🎉 User schema migration completed successfully!');
    console.log('');
    console.log('📋 Summary of changes:');
    console.log('  - Added server_type column to users table');
    console.log('  - Updated role constraint to include staff roles (management, reception, teacher)');
    console.log('  - Set existing users to server_type = "admin"');
    console.log('  - Created index for server_type column');
    console.log('');
    console.log('✨ Admin database now supports both admin and staff user management!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Error details:', error);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };

/**
 * Individual Cabinet API endpoint
 * Handles operations on specific cabinets (GET, PUT, DELETE)
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';
import { getUserFromRequest, hasPermission } from '@/lib/auth';
import { query } from '@/lib/db';
import { logCabinetOperation, getRequestContext } from '@/lib/activity-logger';
import { ActivityAction } from '@/types';

interface Cabinet {
  id: string;
  name: string;
  capacity: number;
  equipment: string[];
  hourly_rate?: number;
  is_available: boolean;
  created_at: Date;
  updated_at: Date;
}

interface UpdateCabinetRequest {
  name?: string;
  capacity?: number;
  equipment?: string[];
  hourlyRate?: number;
  isAvailable?: boolean;
}

// GET /api/cabinets/[id] - Get cabinet by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'read')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid cabinet ID format', 400);
    }

    // Get cabinet with booking statistics
    const sql = `
      SELECT 
        c.id,
        c.name,
        c.capacity,
        c.equipment,
        c.hourly_rate,
        c.is_available,
        c.created_at,
        c.updated_at,
        COUNT(cb.id) as total_bookings,
        COUNT(CASE WHEN cb.status = 'confirmed' AND cb.date >= CURRENT_DATE THEN 1 END) as upcoming_bookings
      FROM cabinets c
      LEFT JOIN cabinet_bookings cb ON c.id = cb.cabinet_id
      WHERE c.id = $1
      GROUP BY c.id, c.name, c.capacity, c.equipment, c.hourly_rate, c.is_available, c.created_at, c.updated_at
    `;

    const result = await query(sql, [id]);

    if (result.rows.length === 0) {
      return createErrorResponse('Cabinet not found', 404);
    }

    const cabinet = result.rows[0];

    // Format response
    const responseCabinet = {
      id: cabinet.id,
      name: cabinet.name,
      capacity: cabinet.capacity,
      equipment: cabinet.equipment || [],
      hourlyRate: cabinet.hourly_rate,
      isAvailable: cabinet.is_available,
      createdAt: cabinet.created_at,
      updatedAt: cabinet.updated_at,
      statistics: {
        totalBookings: parseInt(cabinet.total_bookings),
        upcomingBookings: parseInt(cabinet.upcoming_bookings)
      }
    };

    return createResponse(responseCabinet, true, 'Cabinet retrieved successfully');

  } catch (error) {
    console.error('Error fetching cabinet:', error);
    return createErrorResponse('Failed to fetch cabinet', 500);
  }
}

// PUT /api/cabinets/[id] - Update cabinet
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'update')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid cabinet ID format', 400);
    }

    const body: UpdateCabinetRequest = await request.json();

    // Get current cabinet data
    const currentSql = `
      SELECT id, name, capacity, equipment, hourly_rate, is_available, created_at, updated_at
      FROM cabinets 
      WHERE id = $1
    `;

    const currentResult = await query<Cabinet>(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Cabinet not found', 404);
    }

    const currentCabinet = currentResult.rows[0];

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateParams: any[] = [];
    let paramIndex = 1;

    if (body.name !== undefined) {
      // Check if new name conflicts with existing cabinet
      if (body.name !== currentCabinet.name) {
        const existingCabinet = await query(
          'SELECT id FROM cabinets WHERE name = $1 AND id != $2',
          [body.name, id]
        );

        if (existingCabinet.rows.length > 0) {
          return createErrorResponse('Cabinet with this name already exists', 400);
        }
      }

      updateFields.push(`name = $${paramIndex}`);
      updateParams.push(body.name);
      paramIndex++;
    }

    if (body.capacity !== undefined) {
      if (body.capacity <= 0) {
        return createErrorResponse('Capacity must be greater than 0', 400);
      }
      updateFields.push(`capacity = $${paramIndex}`);
      updateParams.push(body.capacity);
      paramIndex++;
    }

    if (body.equipment !== undefined) {
      updateFields.push(`equipment = $${paramIndex}`);
      updateParams.push(body.equipment);
      paramIndex++;
    }

    if (body.hourlyRate !== undefined) {
      if (body.hourlyRate < 0) {
        return createErrorResponse('Hourly rate cannot be negative', 400);
      }
      updateFields.push(`hourly_rate = $${paramIndex}`);
      updateParams.push(body.hourlyRate);
      paramIndex++;
    }

    if (body.isAvailable !== undefined) {
      updateFields.push(`is_available = $${paramIndex}`);
      updateParams.push(body.isAvailable);
      paramIndex++;
    }

    // If no fields to update
    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', 400);
    }

    // Add updated_at field
    updateFields.push(`updated_at = CURRENT_TIMESTAMP`);

    // Add WHERE clause parameter
    updateParams.push(id);

    // Execute update
    const updateSql = `
      UPDATE cabinets 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING id, name, capacity, equipment, hourly_rate, is_available, created_at, updated_at
    `;

    const updateResult = await query<Cabinet>(updateSql, updateParams);
    const updatedCabinet = updateResult.rows[0];

    // Log the cabinet update
    const context = getRequestContext(request.headers);
    await logCabinetOperation(
      ActivityAction.UPDATE,
      authResult.user.id,
      {
        id: updatedCabinet.id,
        name: updatedCabinet.name,
        capacity: updatedCabinet.capacity,
        equipment: updatedCabinet.equipment,
        hourlyRate: updatedCabinet.hourly_rate,
        isAvailable: updatedCabinet.is_available
      },
      {
        id: currentCabinet.id,
        name: currentCabinet.name,
        capacity: currentCabinet.capacity,
        equipment: currentCabinet.equipment,
        hourlyRate: currentCabinet.hourly_rate,
        isAvailable: currentCabinet.is_available
      },
      context
    );

    // Format response
    const responseCabinet = {
      id: updatedCabinet.id,
      name: updatedCabinet.name,
      capacity: updatedCabinet.capacity,
      equipment: updatedCabinet.equipment || [],
      hourlyRate: updatedCabinet.hourly_rate,
      isAvailable: updatedCabinet.is_available,
      createdAt: updatedCabinet.created_at,
      updatedAt: updatedCabinet.updated_at
    };

    return createResponse(responseCabinet, true, 'Cabinet updated successfully');

  } catch (error) {
    console.error('Error updating cabinet:', error);
    return createErrorResponse('Failed to update cabinet', 500);
  }
}

// DELETE /api/cabinets/[id] - Delete cabinet (soft delete by setting unavailable)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Check permissions
    if (!hasPermission(authResult.user.role, 'cabinets', 'delete')) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid cabinet ID format', 400);
    }

    // Check if cabinet exists and get current data
    const currentSql = `
      SELECT id, name, capacity, equipment, hourly_rate, is_available, created_at, updated_at
      FROM cabinets 
      WHERE id = $1
    `;

    const currentResult = await query<Cabinet>(currentSql, [id]);

    if (currentResult.rows.length === 0) {
      return createErrorResponse('Cabinet not found', 404);
    }

    const currentCabinet = currentResult.rows[0];

    // Check for active bookings
    const activeBookingsSql = `
      SELECT COUNT(*) as count
      FROM cabinet_bookings 
      WHERE cabinet_id = $1 
        AND status IN ('confirmed', 'pending') 
        AND date >= CURRENT_DATE
    `;

    const activeBookingsResult = await query(activeBookingsSql, [id]);
    const activeBookingsCount = parseInt(activeBookingsResult.rows[0].count);

    if (activeBookingsCount > 0) {
      return createErrorResponse(
        `Cannot delete cabinet with ${activeBookingsCount} active booking(s). Cancel bookings first.`,
        400
      );
    }

    // Soft delete by setting is_available to false
    const deleteSql = `
      UPDATE cabinets 
      SET is_available = false, updated_at = CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING id, name, capacity, equipment, hourly_rate, is_available, created_at, updated_at
    `;

    const deleteResult = await query<Cabinet>(deleteSql, [id]);
    const deletedCabinet = deleteResult.rows[0];

    // Log the cabinet deletion
    const context = getRequestContext(request.headers);
    await logCabinetOperation(
      ActivityAction.DELETE,
      authResult.user.id,
      {
        id: deletedCabinet.id,
        name: deletedCabinet.name,
        capacity: deletedCabinet.capacity,
        equipment: deletedCabinet.equipment,
        hourlyRate: deletedCabinet.hourly_rate,
        isAvailable: deletedCabinet.is_available
      },
      {
        id: currentCabinet.id,
        name: currentCabinet.name,
        capacity: currentCabinet.capacity,
        equipment: currentCabinet.equipment,
        hourlyRate: currentCabinet.hourly_rate,
        isAvailable: currentCabinet.is_available
      },
      context
    );

    return createResponse(
      { id: deletedCabinet.id },
      true,
      'Cabinet deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting cabinet:', error);
    return createErrorResponse('Failed to delete cabinet', 500);
  }
}

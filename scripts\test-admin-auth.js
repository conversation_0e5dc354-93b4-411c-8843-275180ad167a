/**
 * Test script for admin server staff authentication endpoint
 * Tests the admin server's staff authentication API directly
 */

const { default: fetch } = require('node-fetch');

// Configuration
const ADMIN_SERVER_URL = 'http://localhost:3000';
const API_KEY = 'staff-server-api-key-innovative-centre-2024';

// Test credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Manager123!'
};

async function testAdminStaffAuth() {
  console.log('🔍 Testing admin server staff authentication endpoint...');
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': API_KEY,
        'X-Source-Service': 'staff-server'
      },
      body: JSON.stringify(testUser)
    });
    
    const data = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ Admin server staff authentication working!');
      return true;
    } else {
      console.error('❌ Admin server staff authentication failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Request failed:', error.message);
    return false;
  }
}

async function testAdminHealth() {
  console.log('🔍 Testing admin server health...');
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/health`);
    const data = await response.json();
    
    console.log('Admin server status:', data.data?.status || 'unknown');
    return response.ok;
  } catch (error) {
    console.error('❌ Admin server health check failed:', error.message);
    return false;
  }
}

async function runTest() {
  console.log('🚀 Testing Admin Server Staff Authentication');
  console.log('='.repeat(50));
  
  const healthOk = await testAdminHealth();
  if (!healthOk) {
    console.log('❌ Admin server not available');
    return;
  }
  
  await testAdminStaffAuth();
}

runTest().catch(error => {
  console.error('❌ Test failed:', error);
});

/**
 * Consolidated reports endpoint for staff server integration
 * Provides combined financial and operational reports to the staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function GET(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);

    const startDate = filters.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
    const endDate = filters.endDate || new Date().toISOString().split('T')[0];
    const reportType = filters.type || 'summary';

    try {
      let reportData: any = {};

      if (reportType === 'financial' || reportType === 'summary') {
        // Financial data
        const financialResult = await query(
          `SELECT 
             COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as total_payments,
             COALESCE(SUM(CASE WHEN p.status = 'completed' THEN p.amount ELSE 0 END), 0) as total_revenue,
             COUNT(CASE WHEN p.status = 'pending' THEN 1 END) as pending_payments,
             COALESCE(SUM(CASE WHEN p.status = 'pending' THEN p.amount ELSE 0 END), 0) as pending_amount,
             COUNT(CASE WHEN i.status = 'overdue' THEN 1 END) as overdue_invoices,
             COALESCE(SUM(CASE WHEN i.status = 'overdue' THEN i.amount ELSE 0 END), 0) as overdue_amount
           FROM payments p
           LEFT JOIN invoices i ON p.invoice_id = i.id
           WHERE p.created_at BETWEEN $1 AND $2`,
          [startDate, endDate]
        );

        const monthlyRevenueResult = await query(
          `SELECT 
             DATE_TRUNC('month', p.created_at) as month,
             COALESCE(SUM(p.amount), 0) as revenue
           FROM payments p
           WHERE p.status = 'completed' 
           AND p.created_at BETWEEN $1 AND $2
           GROUP BY DATE_TRUNC('month', p.created_at)
           ORDER BY month`,
          [startDate, endDate]
        );

        reportData.financial = {
          summary: financialResult.rows[0] || {
            total_payments: 0,
            total_revenue: 0,
            pending_payments: 0,
            pending_amount: 0,
            overdue_invoices: 0,
            overdue_amount: 0
          },
          monthlyRevenue: monthlyRevenueResult.rows.map(row => ({
            month: row.month,
            revenue: parseFloat(row.revenue)
          }))
        };
      }

      if (reportType === 'operational' || reportType === 'summary') {
        // Cabinet utilization
        const cabinetUtilizationResult = await query(
          `SELECT 
             c.id,
             c.name,
             COUNT(b.id) as total_bookings,
             COALESCE(SUM(
               EXTRACT(EPOCH FROM (
                 (DATE '1970-01-01' + b.end_time::time) - 
                 (DATE '1970-01-01' + b.start_time::time)
               )) / 3600
             ), 0) as total_hours_booked
           FROM cabinets c
           LEFT JOIN cabinet_bookings b ON c.id = b.cabinet_id 
             AND b.date BETWEEN $1 AND $2
             AND b.status = 'confirmed'
           WHERE c.is_available = true
           GROUP BY c.id, c.name
           ORDER BY total_bookings DESC`,
          [startDate, endDate]
        );

        // Activity summary
        const activityResult = await query(
          `SELECT 
             action,
             resource_type,
             COUNT(*) as count
           FROM activity_logs
           WHERE timestamp BETWEEN $1 AND $2
           GROUP BY action, resource_type
           ORDER BY count DESC`,
          [startDate, endDate]
        );

        // User activity
        const userActivityResult = await query(
          `SELECT 
             u.name,
             u.role,
             COUNT(al.id) as activity_count
           FROM users u
           LEFT JOIN activity_logs al ON u.id = al.user_id
             AND al.timestamp BETWEEN $1 AND $2
           WHERE u.server_type = 'admin'
           GROUP BY u.id, u.name, u.role
           ORDER BY activity_count DESC
           LIMIT 10`,
          [startDate, endDate]
        );

        reportData.operational = {
          cabinetUtilization: cabinetUtilizationResult.rows.map(row => ({
            cabinetId: row.id,
            cabinetName: row.name,
            totalBookings: parseInt(row.total_bookings),
            totalHoursBooked: parseFloat(row.total_hours_booked)
          })),
          activitySummary: activityResult.rows.map(row => ({
            action: row.action,
            resourceType: row.resource_type,
            count: parseInt(row.count)
          })),
          topActiveUsers: userActivityResult.rows.map(row => ({
            name: row.name,
            role: row.role,
            activityCount: parseInt(row.activity_count)
          }))
        };
      }

      if (reportType === 'detailed') {
        // Detailed financial breakdown
        const detailedFinancialResult = await query(
          `SELECT 
             p.id,
             p.amount,
             p.payment_method,
             p.status,
             p.created_at,
             i.invoice_number,
             s.first_name || ' ' || s.last_name as student_name
           FROM payments p
           LEFT JOIN invoices i ON p.invoice_id = i.id
           LEFT JOIN students s ON i.student_id = s.id
           WHERE p.created_at BETWEEN $1 AND $2
           ORDER BY p.created_at DESC
           LIMIT 100`,
          [startDate, endDate]
        );

        // Detailed booking information
        const detailedBookingsResult = await query(
          `SELECT 
             b.id,
             b.date,
             b.start_time,
             b.end_time,
             b.purpose,
             b.status,
             c.name as cabinet_name,
             u.name as booked_by_name
           FROM cabinet_bookings b
           JOIN cabinets c ON b.cabinet_id = c.id
           LEFT JOIN users u ON b.booked_by = u.id
           WHERE b.date BETWEEN $1 AND $2
           ORDER BY b.date DESC, b.start_time DESC
           LIMIT 100`,
          [startDate, endDate]
        );

        reportData.detailed = {
          recentPayments: detailedFinancialResult.rows,
          recentBookings: detailedBookingsResult.rows
        };
      }

      return createResponse({
        reportType,
        dateRange: {
          startDate,
          endDate
        },
        generatedAt: new Date().toISOString(),
        data: reportData
      }, true, 'Consolidated report generated successfully');

    } catch (dbError) {
      console.error('Database error generating consolidated report:', dbError);
      return createErrorResponse('Failed to generate report', 500);
    }

  } catch (error) {
    console.error('Consolidated report error:', error);
    return createErrorResponse('Failed to generate report', 500);
  }
}

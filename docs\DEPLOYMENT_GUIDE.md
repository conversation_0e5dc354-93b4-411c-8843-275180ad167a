# 🚀 Deployment Guide - Innovative Centre Platform

## 📋 Overview

This guide provides comprehensive instructions for deploying the Innovative Centre Platform in various environments, from development to production.

## 🏗️ Deployment Options

### 1. Development Deployment
- Local development servers
- Local PostgreSQL databases
- Hot reloading enabled
- Debug logging active

### 2. Staging Deployment
- Docker containers
- Cloud databases
- Production-like configuration
- Testing environment

### 3. Production Deployment
- Kubernetes cluster
- High availability setup
- Load balancing
- Monitoring and alerting

## 🐳 Docker Deployment

### Admin Server Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application
CMD ["npm", "start"]
```

### Staff Server Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3003/api/health || exit 1

# Start application
CMD ["npm", "start"]
```

### Docker Compose Configuration
```yaml
version: '3.8'

services:
  admin-server:
    build: ./admin-server
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${ADMIN_DATABASE_URL}
      - JWT_SECRET=${JWT_SECRET}
      - STAFF_SERVER_API_KEY=${STAFF_SERVER_API_KEY}
    depends_on:
      - admin-db
    restart: unless-stopped

  staff-server:
    build: ./staff-server
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${STAFF_DATABASE_URL}
      - ADMIN_SERVER_URL=http://admin-server:3000
      - ADMIN_SERVER_API_KEY=${STAFF_SERVER_API_KEY}
      - ENABLE_ADMIN_INTEGRATION=true
    depends_on:
      - staff-db
      - admin-server
    restart: unless-stopped

  admin-db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=innovative_admin
      - POSTGRES_USER=${ADMIN_DB_USER}
      - POSTGRES_PASSWORD=${ADMIN_DB_PASSWORD}
    volumes:
      - admin_db_data:/var/lib/postgresql/data
      - ./admin-server/migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped

  staff-db:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=innovative_staff
      - POSTGRES_USER=${STAFF_DB_USER}
      - POSTGRES_PASSWORD=${STAFF_DB_PASSWORD}
    volumes:
      - staff_db_data:/var/lib/postgresql/data
      - ./staff-server/migrations:/docker-entrypoint-initdb.d
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - admin-server
      - staff-server
    restart: unless-stopped

volumes:
  admin_db_data:
  staff_db_data:
```

## ☸️ Kubernetes Deployment

### Admin Server Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-server
  labels:
    app: admin-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: admin-server
  template:
    metadata:
      labels:
        app: admin-server
    spec:
      containers:
      - name: admin-server
        image: innovative-platform/admin-server:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: admin-database-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: jwt-secret
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: admin-server-service
spec:
  selector:
    app: admin-server
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
  type: ClusterIP
```

### Staff Server Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: staff-server
  labels:
    app: staff-server
spec:
  replicas: 2
  selector:
    matchLabels:
      app: staff-server
  template:
    metadata:
      labels:
        app: staff-server
    spec:
      containers:
      - name: staff-server
        image: innovative-platform/staff-server:latest
        ports:
        - containerPort: 3003
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: staff-database-url
        - name: ADMIN_SERVER_URL
          value: "http://admin-server-service"
        - name: ADMIN_SERVER_API_KEY
          valueFrom:
            secretKeyRef:
              name: app-secrets
              key: staff-server-api-key
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3003
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3003
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: staff-server-service
spec:
  selector:
    app: staff-server
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3003
  type: ClusterIP
```

## 🔧 Environment Configuration

### Production Environment Variables
```bash
# Admin Server
NODE_ENV=production
DATABASE_URL=**********************************************/innovative_admin
JWT_SECRET=your-super-secure-jwt-secret-here
STAFF_SERVER_API_KEY=staff-server-api-key-innovative-centre-2024
CORS_ORIGIN=https://admin.innovativecentre.com

# Staff Server
NODE_ENV=production
DATABASE_URL=**********************************************/innovative_staff
ADMIN_SERVER_URL=https://admin.innovativecentre.com
ADMIN_SERVER_API_KEY=staff-server-api-key-innovative-centre-2024
ENABLE_ADMIN_INTEGRATION=true
CORS_ORIGIN=https://staff.innovativecentre.com
```

### Secrets Management
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: database-secrets
type: Opaque
data:
  admin-database-url: <base64-encoded-url>
  staff-database-url: <base64-encoded-url>
---
apiVersion: v1
kind: Secret
metadata:
  name: app-secrets
type: Opaque
data:
  jwt-secret: <base64-encoded-secret>
  staff-server-api-key: <base64-encoded-key>
```

## 🌐 Load Balancer Configuration

### Nginx Configuration
```nginx
upstream admin_backend {
    server admin-server-1:3000;
    server admin-server-2:3000;
    server admin-server-3:3000;
}

upstream staff_backend {
    server staff-server-1:3003;
    server staff-server-2:3003;
}

server {
    listen 80;
    server_name admin.innovativecentre.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.innovativecentre.com;

    ssl_certificate /etc/nginx/ssl/admin.crt;
    ssl_certificate_key /etc/nginx/ssl/admin.key;

    location / {
        proxy_pass http://admin_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/health {
        proxy_pass http://admin_backend;
        access_log off;
    }
}

server {
    listen 80;
    server_name staff.innovativecentre.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name staff.innovativecentre.com;

    ssl_certificate /etc/nginx/ssl/staff.crt;
    ssl_certificate_key /etc/nginx/ssl/staff.key;

    location / {
        proxy_pass http://staff_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api/health {
        proxy_pass http://staff_backend;
        access_log off;
    }
}
```

## 📊 Monitoring and Logging

### Prometheus Configuration
```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'admin-server'
    static_configs:
      - targets: ['admin-server:3000']
    metrics_path: '/api/metrics'

  - job_name: 'staff-server'
    static_configs:
      - targets: ['staff-server:3003']
    metrics_path: '/api/metrics'
```

### Grafana Dashboard
- Server response times
- Database connection status
- Active user sessions
- Error rates and types
- Interserver communication health

## 🔒 Security Considerations

### SSL/TLS Configuration
- Use Let's Encrypt for certificates
- Enable HTTP/2
- Configure HSTS headers
- Implement proper cipher suites

### Network Security
- Use private networks for database connections
- Implement firewall rules
- Enable VPN access for administrative tasks
- Regular security updates

### Application Security
- Environment variable encryption
- API rate limiting
- Input validation and sanitization
- Regular dependency updates

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] Database migrations tested
- [ ] SSL certificates installed
- [ ] Load balancer configured
- [ ] Monitoring setup complete

### Deployment
- [ ] Build and push Docker images
- [ ] Deploy to staging environment
- [ ] Run integration tests
- [ ] Deploy to production
- [ ] Verify health checks

### Post-deployment
- [ ] Monitor application logs
- [ ] Verify interserver communication
- [ ] Test user authentication
- [ ] Check database performance
- [ ] Validate backup procedures

This deployment guide ensures a robust, scalable, and secure production environment for the Innovative Centre Platform.

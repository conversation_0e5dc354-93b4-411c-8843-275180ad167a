/**
 * System Health Checker
 * Comprehensive health monitoring for the Innovative Centre Platform
 */

require('dotenv').config({ path: '../admin-server/.env.local' });

class SystemHealthChecker {
  constructor() {
    this.adminServerUrl = 'http://localhost:3000';
    this.staffServerUrl = 'http://localhost:3003';
    this.healthStatus = {
      overall: 'unknown',
      servers: {},
      databases: {},
      interserver: {},
      performance: {},
      timestamp: new Date().toISOString()
    };
  }

  async checkServerHealth(serverUrl, serverName) {
    console.log(`🔍 Checking ${serverName} server health...`);
    
    try {
      const startTime = Date.now();
      const response = await fetch(`${serverUrl}/api/health`, {
        method: 'GET',
        timeout: 5000
      });
      
      const responseTime = Date.now() - startTime;
      const isHealthy = response.ok;
      
      if (response.ok) {
        const healthData = await response.json();
        console.log(`✅ ${serverName} server is healthy (${responseTime}ms)`);
        
        return {
          status: 'healthy',
          responseTime,
          data: healthData,
          url: serverUrl,
          lastChecked: new Date().toISOString()
        };
      } else {
        console.log(`⚠️ ${serverName} server returned status ${response.status}`);
        return {
          status: 'unhealthy',
          responseTime,
          statusCode: response.status,
          url: serverUrl,
          lastChecked: new Date().toISOString()
        };
      }
    } catch (error) {
      console.log(`❌ ${serverName} server is unreachable: ${error.message}`);
      return {
        status: 'unreachable',
        error: error.message,
        url: serverUrl,
        lastChecked: new Date().toISOString()
      };
    }
  }

  async checkDatabaseConnectivity() {
    console.log('🔍 Checking database connectivity...');
    
    try {
      // Check admin database through API
      const adminDbCheck = await fetch(`${this.adminServerUrl}/api/health/database`);
      const adminDbHealthy = adminDbCheck.ok;
      
      // Check staff database through API
      const staffDbCheck = await fetch(`${this.staffServerUrl}/api/health/database`);
      const staffDbHealthy = staffDbCheck.ok;
      
      console.log(`${adminDbHealthy ? '✅' : '❌'} Admin database: ${adminDbHealthy ? 'Connected' : 'Disconnected'}`);
      console.log(`${staffDbHealthy ? '✅' : '❌'} Staff database: ${staffDbHealthy ? 'Connected' : 'Disconnected'}`);
      
      return {
        admin: {
          status: adminDbHealthy ? 'connected' : 'disconnected',
          lastChecked: new Date().toISOString()
        },
        staff: {
          status: staffDbHealthy ? 'connected' : 'disconnected',
          lastChecked: new Date().toISOString()
        }
      };
    } catch (error) {
      console.log(`❌ Database connectivity check failed: ${error.message}`);
      return {
        admin: { status: 'error', error: error.message },
        staff: { status: 'error', error: error.message }
      };
    }
  }

  async checkInterserverCommunication() {
    console.log('🔍 Checking interserver communication...');
    
    try {
      const apiKey = process.env.STAFF_SERVER_API_KEY;
      if (!apiKey) {
        console.log('❌ Interserver API key not configured');
        return {
          status: 'misconfigured',
          error: 'API key not configured'
        };
      }

      const startTime = Date.now();
      const testResponse = await fetch(`${this.adminServerUrl}/api/staff-integration/activity-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'X-Source-Service': 'staff-server'
        },
        body: JSON.stringify({
          userId: 'health-check-user',
          action: 'HEALTH_CHECK',
          resourceType: 'SYSTEM',
          description: 'Interserver communication health check',
          sourceService: 'staff-server',
          timestamp: new Date().toISOString()
        })
      });

      const responseTime = Date.now() - startTime;
      const isHealthy = testResponse.ok;

      console.log(`${isHealthy ? '✅' : '❌'} Interserver communication: ${isHealthy ? 'Working' : 'Failed'} (${responseTime}ms)`);

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        statusCode: testResponse.status,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      console.log(`❌ Interserver communication check failed: ${error.message}`);
      return {
        status: 'error',
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  async checkPerformanceMetrics() {
    console.log('🔍 Checking performance metrics...');
    
    try {
      // Get admin server performance
      const adminPerfResponse = await fetch(`${this.adminServerUrl}/api/performance/metrics`);
      const adminPerf = adminPerfResponse.ok ? await adminPerfResponse.json() : null;

      // Get staff server performance (if endpoint exists)
      let staffPerf = null;
      try {
        const staffPerfResponse = await fetch(`${this.staffServerUrl}/api/performance/metrics`);
        staffPerf = staffPerfResponse.ok ? await staffPerfResponse.json() : null;
      } catch (error) {
        // Staff server might not have performance endpoint yet
      }

      const performance = {
        admin: adminPerf?.data || null,
        staff: staffPerf?.data || null,
        lastChecked: new Date().toISOString()
      };

      if (adminPerf?.data) {
        const avgResponseTime = Object.values(adminPerf.data.performance || {})
          .reduce((sum, metric) => sum + (metric.avgTime || 0), 0) / 
          Object.keys(adminPerf.data.performance || {}).length || 0;

        console.log(`📊 Admin server avg response time: ${Math.round(avgResponseTime)}ms`);
        console.log(`💾 Cache stats: ${adminPerf.data.cache?.active || 0} active, ${adminPerf.data.cache?.total || 0} total`);
      }

      return performance;
    } catch (error) {
      console.log(`❌ Performance metrics check failed: ${error.message}`);
      return {
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  async checkSystemResources() {
    console.log('🔍 Checking system resources...');
    
    try {
      // This would typically check CPU, memory, disk usage
      // For now, we'll simulate basic checks
      
      const memoryUsage = process.memoryUsage();
      const memoryUsagePercent = memoryUsage.heapUsed / memoryUsage.heapTotal;
      
      console.log(`💾 Memory usage: ${Math.round(memoryUsagePercent * 100)}%`);
      console.log(`⏱️ Process uptime: ${Math.round(process.uptime())} seconds`);
      
      return {
        memory: {
          usage: Math.round(memoryUsagePercent * 100),
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) // MB
        },
        uptime: Math.round(process.uptime()),
        platform: process.platform,
        nodeVersion: process.version,
        lastChecked: new Date().toISOString()
      };
    } catch (error) {
      console.log(`❌ System resources check failed: ${error.message}`);
      return {
        error: error.message,
        lastChecked: new Date().toISOString()
      };
    }
  }

  async runComprehensiveHealthCheck() {
    console.log('🚀 Starting Comprehensive System Health Check\n');
    
    const startTime = Date.now();

    // Run all health checks
    const [
      adminServerHealth,
      staffServerHealth,
      databaseHealth,
      interserverHealth,
      performanceMetrics,
      systemResources
    ] = await Promise.all([
      this.checkServerHealth(this.adminServerUrl, 'Admin'),
      this.checkServerHealth(this.staffServerUrl, 'Staff'),
      this.checkDatabaseConnectivity(),
      this.checkInterserverCommunication(),
      this.checkPerformanceMetrics(),
      this.checkSystemResources()
    ]);

    // Update health status
    this.healthStatus = {
      overall: this.calculateOverallHealth({
        adminServerHealth,
        staffServerHealth,
        databaseHealth,
        interserverHealth
      }),
      servers: {
        admin: adminServerHealth,
        staff: staffServerHealth
      },
      databases: databaseHealth,
      interserver: interserverHealth,
      performance: performanceMetrics,
      system: systemResources,
      checkDuration: Date.now() - startTime,
      timestamp: new Date().toISOString()
    };

    // Print summary
    this.printHealthSummary();
    
    return this.healthStatus;
  }

  calculateOverallHealth(checks) {
    const criticalChecks = [
      checks.adminServerHealth.status === 'healthy',
      checks.staffServerHealth.status === 'healthy',
      checks.databaseHealth.admin.status === 'connected',
      checks.databaseHealth.staff.status === 'connected'
    ];

    const healthyCount = criticalChecks.filter(Boolean).length;
    const totalChecks = criticalChecks.length;

    if (healthyCount === totalChecks) {
      return 'healthy';
    } else if (healthyCount >= totalChecks * 0.75) {
      return 'degraded';
    } else {
      return 'unhealthy';
    }
  }

  printHealthSummary() {
    console.log('\n📊 System Health Summary:');
    console.log('=' .repeat(50));
    
    const statusEmoji = {
      healthy: '✅',
      degraded: '⚠️',
      unhealthy: '❌',
      unknown: '❓'
    };

    console.log(`${statusEmoji[this.healthStatus.overall]} Overall Status: ${this.healthStatus.overall.toUpperCase()}`);
    console.log(`🕐 Check Duration: ${this.healthStatus.checkDuration}ms`);
    console.log(`📅 Timestamp: ${this.healthStatus.timestamp}`);
    
    console.log('\n🖥️ Server Status:');
    console.log(`  Admin: ${statusEmoji[this.healthStatus.servers.admin.status]} ${this.healthStatus.servers.admin.status}`);
    console.log(`  Staff: ${statusEmoji[this.healthStatus.servers.staff.status]} ${this.healthStatus.servers.staff.status}`);
    
    console.log('\n💾 Database Status:');
    console.log(`  Admin DB: ${statusEmoji[this.healthStatus.databases.admin.status === 'connected' ? 'healthy' : 'unhealthy']} ${this.healthStatus.databases.admin.status}`);
    console.log(`  Staff DB: ${statusEmoji[this.healthStatus.databases.staff.status === 'connected' ? 'healthy' : 'unhealthy']} ${this.healthStatus.databases.staff.status}`);
    
    console.log('\n🔗 Interserver Communication:');
    console.log(`  Status: ${statusEmoji[this.healthStatus.interserver.status]} ${this.healthStatus.interserver.status}`);
    
    if (this.healthStatus.system) {
      console.log('\n⚡ System Resources:');
      console.log(`  Memory: ${this.healthStatus.system.memory?.usage || 'N/A'}%`);
      console.log(`  Uptime: ${this.healthStatus.system.uptime || 'N/A'}s`);
    }

    console.log('\n' + '=' .repeat(50));
    
    if (this.healthStatus.overall === 'healthy') {
      console.log('🎉 All systems operational! Ready for presentation.');
    } else {
      console.log('⚠️ Some issues detected. Please review the details above.');
    }
  }

  async exportHealthReport(filename) {
    const fs = require('fs').promises;
    
    try {
      await fs.writeFile(filename, JSON.stringify(this.healthStatus, null, 2));
      console.log(`📄 Health report exported to: ${filename}`);
    } catch (error) {
      console.error(`❌ Failed to export health report: ${error.message}`);
    }
  }
}

// Run health check if called directly
if (require.main === module) {
  const healthChecker = new SystemHealthChecker();
  
  healthChecker.runComprehensiveHealthCheck()
    .then(async (healthStatus) => {
      // Export report
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      await healthChecker.exportHealthReport(`health-report-${timestamp}.json`);
      
      // Exit with appropriate code
      process.exit(healthStatus.overall === 'healthy' ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Health check failed:', error);
      process.exit(1);
    });
}

module.exports = SystemHealthChecker;

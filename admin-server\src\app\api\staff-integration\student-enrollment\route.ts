/**
 * Student enrollment notification endpoint
 * Receives notifications from staff server when students are enrolled
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, validateRequiredFields } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const body = await request.json();
    const {
      id,
      firstName,
      lastName,
      email,
      phone,
      enrollmentDate,
      groupId,
      groupName
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['id', 'firstName', 'lastName', 'enrollmentDate']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    try {
      // Check if student record already exists in admin system
      const existingStudentResult = await query(
        'SELECT id FROM student_records WHERE staff_student_id = $1',
        [id]
      );

      let studentRecordId;

      if (existingStudentResult.rows.length === 0) {
        // Create new student record for financial tracking
        const studentRecordResult = await query(`
          INSERT INTO student_records (
            staff_student_id, first_name, last_name, email, phone, 
            enrollment_date, group_id, group_name, status
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
          RETURNING id
        `, [
          id,
          firstName,
          lastName,
          email || null,
          phone || null,
          enrollmentDate,
          groupId || null,
          groupName || null,
          'active'
        ]);

        studentRecordId = studentRecordResult.rows[0].id;

        // Create initial invoice for the student (if auto-invoice is enabled)
        const autoInvoice = process.env.AUTO_CREATE_INVOICE === 'true';
        if (autoInvoice) {
          const defaultAmount = parseFloat(process.env.DEFAULT_MONTHLY_FEE || '100');
          
          await query(`
            INSERT INTO invoices (
              student_id, amount, due_date, status, description, created_by
            ) VALUES ($1, $2, $3, $4, $5, $6)
          `, [
            studentRecordId,
            defaultAmount,
            new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
            'pending',
            `Monthly fee for ${firstName} ${lastName}`,
            'system' // System-generated invoice
          ]);
        }

        console.log(`Created student record for enrollment: ${firstName} ${lastName} (${id})`);
      } else {
        studentRecordId = existingStudentResult.rows[0].id;
        
        // Update existing record
        await query(`
          UPDATE student_records 
          SET first_name = $2, last_name = $3, email = $4, phone = $5,
              group_id = $6, group_name = $7, updated_at = CURRENT_TIMESTAMP
          WHERE staff_student_id = $1
        `, [
          id, firstName, lastName, email, phone, groupId, groupName
        ]);

        console.log(`Updated student record for: ${firstName} ${lastName} (${id})`);
      }

      return createResponse({
        studentRecordId,
        staffStudentId: id,
        processed: true,
        timestamp: new Date().toISOString()
      }, true, 'Student enrollment processed successfully');

    } catch (dbError) {
      console.error('Database error processing student enrollment:', dbError);
      return createErrorResponse('Failed to process student enrollment', 500);
    }

  } catch (error) {
    console.error('Student enrollment processing error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

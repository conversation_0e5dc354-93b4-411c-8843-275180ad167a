/**
 * Student management endpoints
 * Handles CRUD operations for student enrollment and management
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidEmail,
  formatPhoneNumber
} from '@/lib/utils';
import { logStudentOperation, getRequestContext } from '@/lib/activity-logger';
import { adminService, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole, StudentStatus } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface Student {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  date_of_birth?: Date;
  enrollment_date: Date;
  status: StudentStatus;
  created_at: Date;
  updated_at: Date;
}

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const sourceService = request.headers.get('X-Source-Service');
  const expectedKey = process.env.ADMIN_SERVER_API_KEY;

  if (!expectedKey) {
    return false;
  }

  return apiKey === expectedKey && sourceService === 'admin-server';
}

// GET /api/students - List students with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Check for API key authentication (for admin server integration)
    const isApiKeyAuth = validateApiKey(request);

    if (!isApiKeyAuth) {
      // Authenticate user with JWT
      const authResult = await getUserFromRequest(request);
      if (!authResult.success || !authResult.user) {
        return createErrorResponse('Authentication required', 401);
      }

      // Check permissions - all staff roles can view students
      if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
        return createErrorResponse('Insufficient permissions', 403);
      }
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);
    const paymentStatusFilter = searchParams.get('paymentStatus');

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      // Handle search across multiple fields
      if (filters.search) {
        const searchTerm = `%${filters.search}%`;
        const searchResult = await query<{total: string}>(
          `SELECT COUNT(*) as total FROM students
           WHERE (first_name ILIKE $1 OR last_name ILIKE $1 OR email ILIKE $1 OR phone ILIKE $1)`,
          [searchTerm]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const studentsResult = await query<Student>(
          `SELECT id, first_name, last_name, email, phone, date_of_birth, 
                  enrollment_date, status, created_at, updated_at 
           FROM students 
           WHERE (first_name ILIKE $1 OR last_name ILIKE $1 OR email ILIKE $1 OR phone ILIKE $1)
           ORDER BY created_at DESC 
           LIMIT $2 OFFSET $3`,
          [searchTerm, pagination.limit, offset]
        );

        // Transform students to camelCase format
        const transformedStudents = studentsResult.rows.map(student => ({
          id: student.id,
          firstName: student.first_name,
          lastName: student.last_name,
          email: student.email,
          phone: student.phone,
          dateOfBirth: student.date_of_birth,
          enrollmentDate: student.enrollment_date,
          status: student.status,
          createdAt: student.created_at,
          updatedAt: student.updated_at
        }));

        return createResponse({
          students: transformedStudents,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Students retrieved successfully');
      }

      // Add status filter
      if (filters.status && ['active', 'inactive', 'graduated', 'dropped'].includes(filters.status)) {
        conditions.status = filters.status;
      }

      // Add enrollment date range filters
      if (filters.enrollmentFrom) {
        conditions['enrollment_date >='] = filters.enrollmentFrom;
      }

      if (filters.enrollmentTo) {
        conditions['enrollment_date <='] = filters.enrollmentTo;
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM students ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results
      const offset = (pagination.page - 1) * pagination.limit;
      const dataSql = `
        SELECT id, first_name, last_name, email, phone, date_of_birth, 
               enrollment_date, status, created_at, updated_at 
        FROM students ${whereClause} 
        ORDER BY created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const studentsResult = await query<Student>(dataSql, [...params, pagination.limit, offset]);
      let finalStudents = studentsResult.rows;
      let finalTotal = total;

      // If payment status filter is applied, get payment status from admin server and filter
      if (paymentStatusFilter && ['paid', 'unpaid', 'debt'].includes(paymentStatusFilter)) {
        try {
          const studentIds = studentsResult.rows.map(student => student.id);
          const paymentStatusMap = await adminService.getStudentsPaymentStatus(studentIds);

          // Filter students by payment status
          finalStudents = studentsResult.rows.filter(student => {
            const paymentStatus = paymentStatusMap[student.id]?.paymentStatus || 'unpaid';
            return paymentStatus === paymentStatusFilter;
          });

          // Recalculate total for filtered results
          // Note: This is an approximation since we're filtering after pagination
          finalTotal = Math.round(total * (finalStudents.length / studentsResult.rows.length));
        } catch (adminError) {
          console.warn('Failed to filter by payment status:', adminError);
          // Continue with unfiltered results
        }
      }

      // Transform students to camelCase format
      const transformedStudents = finalStudents.map(student => ({
        id: student.id,
        firstName: student.first_name,
        lastName: student.last_name,
        email: student.email,
        phone: student.phone,
        dateOfBirth: student.date_of_birth,
        enrollmentDate: student.enrollment_date,
        status: student.status,
        createdAt: student.created_at,
        updatedAt: student.updated_at
      }));

      return createResponse({
        students: transformedStudents,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: finalTotal,
          totalPages: Math.ceil(finalTotal / pagination.limit),
          hasNext: pagination.page < Math.ceil(finalTotal / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Students retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving students:', dbError);
      return createErrorResponse('Failed to retrieve students', 500);
    }

  } catch (error) {
    console.error('Get students error:', error);
    return createErrorResponse('Failed to retrieve students', 500);
  }
}

// POST /api/students - Create new student
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - management and reception can create students
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { 
      firstName, 
      lastName, 
      email, 
      phone, 
      dateOfBirth, 
      enrollmentDate = new Date().toISOString().split('T')[0],
      status = 'active' 
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['firstName', 'lastName']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate status
    if (!['active', 'inactive', 'graduated', 'dropped'].includes(status)) {
      return createErrorResponse('Invalid status. Must be active, inactive, graduated, or dropped', 400);
    }

    // Validate dates
    if (dateOfBirth && new Date(dateOfBirth) > new Date()) {
      return createErrorResponse('Date of birth cannot be in the future', 400);
    }

    if (enrollmentDate && new Date(enrollmentDate) > new Date()) {
      return createErrorResponse('Enrollment date cannot be in the future', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if student with same email already exists (if email provided)
      if (email) {
        const existingStudentResult = await query(
          'SELECT id FROM students WHERE email = $1',
          [email.toLowerCase()]
        );

        if (existingStudentResult.rows.length > 0) {
          return createErrorResponse('Student with this email already exists', 409);
        }
      }

      // Create student
      const studentResult = await query<Student>(
        `INSERT INTO students (first_name, last_name, email, phone, date_of_birth, enrollment_date, status)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id, first_name, last_name, email, phone, date_of_birth, enrollment_date, status, created_at, updated_at`,
        [
          firstName.trim(),
          lastName.trim(),
          email ? email.toLowerCase() : null,
          phone ? formatPhoneNumber(phone) : null,
          dateOfBirth || null,
          enrollmentDate,
          status
        ]
      );

      const newStudent = studentResult.rows[0];

      // Log student creation
      await logStudentOperation(
        'CREATE' as any,
        authResult.user.id,
        newStudent,
        undefined,
        context
      );

      // Notify admin server of new enrollment
      if (isAdminIntegrationEnabled()) {
        await adminService.notifyStudentEnrollment({
          id: newStudent.id,
          firstName: newStudent.first_name,
          lastName: newStudent.last_name,
          email: newStudent.email || undefined,
          phone: newStudent.phone || undefined,
          enrollmentDate: newStudent.enrollment_date.toISOString(),
        });
      }

      return createResponse({
        id: newStudent.id,
        firstName: newStudent.first_name,
        lastName: newStudent.last_name,
        email: newStudent.email,
        phone: newStudent.phone,
        dateOfBirth: newStudent.date_of_birth,
        enrollmentDate: newStudent.enrollment_date,
        status: newStudent.status,
        createdAt: newStudent.created_at,
        updatedAt: newStudent.updated_at
      }, true, 'Student created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating student:', dbError);
      return createErrorResponse('Failed to create student', 500);
    }

  } catch (error) {
    console.error('Create student error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

/**
 * Simple test to create a student and observe logging
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function testSimpleStudentCreation() {
  console.log('🔍 Testing Simple Student Creation with Logging...');
  
  const staffServerUrl = 'http://localhost:3003';
  
  // Use a known staff user for testing
  const testCredentials = {
    email: '<EMAIL>',
    password: 'password123'
  };

  try {
    // Step 1: Login
    console.log('\n🔐 Step 1: Logging in...');
    
    const loginResponse = await fetch(`${staffServerUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testCredentials),
    });

    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      console.error('❌ Login failed:', loginResponse.status, errorText);
      
      // Try with different password
      console.log('🔄 Trying with different password...');
      const altCredentials = { ...testCredentials, password: 'Reception123!' };
      
      const altLoginResponse = await fetch(`${staffServerUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(altCredentials),
      });

      if (!altLoginResponse.ok) {
        const altErrorText = await altLoginResponse.text();
        console.error('❌ Alternative login also failed:', altLoginResponse.status, altErrorText);
        return;
      }

      const altLoginData = await altLoginResponse.json();
      console.log('✅ Alternative login successful');
      var authToken = altLoginData.data?.token;
      var userId = altLoginData.data?.user?.id;
    } else {
      const loginData = await loginResponse.json();
      console.log('✅ Login successful');
      var authToken = loginData.data?.token;
      var userId = loginData.data?.user?.id;
    }

    console.log('👤 User ID:', userId);
    
    if (!authToken) {
      console.error('❌ No auth token received');
      return;
    }

    // Step 2: Create student
    console.log('\n📝 Step 2: Creating student...');
    
    const studentData = {
      firstName: 'Debug',
      lastName: 'Student',
      email: '<EMAIL>',
      phone: '+998901234567',
      dateOfBirth: '2000-01-01',
      enrollmentDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };

    console.log('📤 Sending student creation request...');
    
    const createResponse = await fetch(`${staffServerUrl}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
      body: JSON.stringify(studentData),
    });

    console.log('📡 Create response status:', createResponse.status);
    
    const responseText = await createResponse.text();
    console.log('📡 Create response body:', responseText);

    if (createResponse.ok) {
      console.log('✅ Student creation request successful');
      
      // Parse response
      try {
        const responseData = JSON.parse(responseText);
        console.log('📋 Created student:', responseData.data);
      } catch (parseError) {
        console.log('⚠️  Could not parse response');
      }
    } else {
      console.log('❌ Student creation failed');
    }

    // Step 3: Check admin server logs after a delay
    console.log('\n⏳ Waiting 3 seconds for activity processing...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n🔍 Step 3: Checking admin server logs...');
    
    const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
    
    try {
      const logsResponse = await fetch(`${adminServerUrl}/api/activity-logs?limit=3`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (logsResponse.ok) {
        const logsData = await logsResponse.json();
        console.log('📊 Recent admin server logs:');
        if (logsData.success && logsData.data && logsData.data.logs) {
          logsData.data.logs.forEach((log, index) => {
            console.log(`  ${index + 1}. [${log.timestamp}] ${log.action} ${log.resource_type}`);
            console.log(`      Description: ${log.description}`);
            console.log(`      User: ${log.user?.name || log.user?.email || log.userId}`);
          });
        }
      } else {
        console.log('⚠️  Could not fetch admin server logs');
      }
    } catch (logsError) {
      console.log('⚠️  Error fetching admin server logs:', logsError.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }

  console.log('\n🎉 Simple student creation test completed!');
}

// Run the test
testSimpleStudentCreation();

/**
 * Individual group management endpoints
 * Handles GET, PUT, DELETE operations for specific groups
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidUUID
} from '@/lib/utils';
import { logGroupOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';

interface Group {
  id: string;
  name: string;
  level?: string;
  teacher_id?: string;
  max_students: number;
  schedule?: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

interface GroupWithDetails extends Group {
  teacher_name?: string;
  teacher_email?: string;
  current_students?: number;
  students?: Array<{
    id: string;
    first_name: string;
    last_name: string;
    email?: string;
    status: string;
  }>;
}

// GET /api/groups/[id] - Get specific group
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid group ID format', 400);
    }

    // Check permissions - all staff roles can view groups
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    try {
      // Get group details with teacher information
      const groupResult = await query<GroupWithDetails>(
        `SELECT g.id, g.name, g.level, g.teacher_id, g.max_students, 
                g.schedule, g.is_active, g.created_at, g.updated_at,
                u.name as teacher_name, u.email as teacher_email,
                (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id) as current_students
         FROM groups g
         LEFT JOIN users u ON g.teacher_id = u.id
         WHERE g.id = $1`,
        [id]
      );

      if (groupResult.rows.length === 0) {
        return createErrorResponse('Group not found', 404);
      }

      const group = groupResult.rows[0];

      // Get students in this group with enrollment details
      const studentsResult = await query(
        `SELECT s.id, s.first_name, s.last_name, s.email, s.status,
                sg.enrollment_date, sg.status as enrollment_status
         FROM students s
         JOIN student_groups sg ON s.id = sg.student_id
         WHERE sg.group_id = $1 AND sg.status = 'active'
         ORDER BY s.first_name, s.last_name`,
        [id]
      );

      return createResponse({
        id: group.id,
        name: group.name,
        level: group.level,
        teacherId: group.teacher_id,
        teacherName: group.teacher_name,
        teacherEmail: group.teacher_email,
        maxStudents: group.max_students,
        schedule: group.schedule,
        isActive: group.is_active,
        currentStudents: parseInt(group.current_students?.toString() || '0'),
        students: studentsResult.rows.map(student => ({
          id: student.id,
          firstName: student.first_name,
          lastName: student.last_name,
          email: student.email,
          status: student.status,
          enrollmentDate: student.enrollment_date,
          enrollmentStatus: student.enrollment_status
        })),
        createdAt: group.created_at,
        updatedAt: group.updated_at
      }, true, 'Group retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving group:', dbError);
      return createErrorResponse('Failed to retrieve group', 500);
    }

  } catch (error) {
    console.error('Get group error:', error);
    return createErrorResponse('Failed to retrieve group', 500);
  }
}

// PUT /api/groups/[id] - Update specific group
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid group ID format', 400);
    }

    // Check permissions - management and reception can update groups, teachers can update limited fields
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { name, level, teacherId, maxStudents, schedule, isActive } = body;

    // Validate teacherId if provided
    if (teacherId && !isValidUUID(teacherId)) {
      return createErrorResponse('Invalid teacher ID format', 400);
    }

    // Validate maxStudents if provided
    if (maxStudents !== undefined && (maxStudents < 1 || maxStudents > 50)) {
      return createErrorResponse('Max students must be between 1 and 50', 400);
    }

    // Teachers can only update limited fields
    if (authResult.user.role === 'teacher' && (name !== undefined || teacherId !== undefined || maxStudents !== undefined || isActive !== undefined)) {
      return createErrorResponse('Teachers can only update level and schedule', 403);
    }

    // Validate schedule format if provided
    if (schedule && typeof schedule !== 'object') {
      return createErrorResponse('Schedule must be a valid JSON object', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current group data
      const currentGroupResult = await query<Group>(
        `SELECT id, name, level, teacher_id, max_students, schedule, is_active, created_at, updated_at 
         FROM groups WHERE id = $1`,
        [id]
      );

      if (currentGroupResult.rows.length === 0) {
        return createErrorResponse('Group not found', 404);
      }

      const currentGroup = currentGroupResult.rows[0];

      // Check if teacher exists and is a teacher
      if (teacherId) {
        const teacherResult = await query(
          'SELECT id, role FROM users WHERE id = $1 AND is_active = true',
          [teacherId]
        );

        if (teacherResult.rows.length === 0) {
          return createErrorResponse('Teacher not found or inactive', 400);
        }

        const teacher = teacherResult.rows[0];
        if (teacher.role !== 'teacher') {
          return createErrorResponse('Assigned user must be a teacher', 400);
        }
      }

      // Check if name is being changed and if it already exists
      if (name && name.trim() !== currentGroup.name) {
        const existingGroupResult = await query(
          'SELECT id FROM groups WHERE name = $1 AND id != $2',
          [name.trim(), id]
        );

        if (existingGroupResult.rows.length > 0) {
          return createErrorResponse('Group with this name already exists', 409);
        }
      }

      // Check if reducing max students would exceed current enrollment
      if (maxStudents !== undefined && maxStudents < currentGroup.max_students) {
        const currentStudentsResult = await query(
          'SELECT COUNT(*) as count FROM student_groups WHERE group_id = $1',
          [id]
        );
        const currentStudentCount = parseInt(currentStudentsResult.rows[0].count);

        if (maxStudents < currentStudentCount) {
          return createErrorResponse(
            `Cannot reduce max students to ${maxStudents}. Current enrollment is ${currentStudentCount}`,
            400
          );
        }
      }

      // Build update query
      const updateFields: string[] = [];
      const updateValues: any[] = [];
      let paramIndex = 1;

      if (name !== undefined) {
        updateFields.push(`name = $${paramIndex++}`);
        updateValues.push(name.trim());
      }

      if (level !== undefined) {
        updateFields.push(`level = $${paramIndex++}`);
        updateValues.push(level?.trim() || null);
      }

      if (teacherId !== undefined) {
        updateFields.push(`teacher_id = $${paramIndex++}`);
        updateValues.push(teacherId || null);
      }

      if (maxStudents !== undefined) {
        updateFields.push(`max_students = $${paramIndex++}`);
        updateValues.push(maxStudents);
      }

      if (schedule !== undefined) {
        updateFields.push(`schedule = $${paramIndex++}`);
        updateValues.push(schedule ? JSON.stringify(schedule) : null);
      }

      if (isActive !== undefined) {
        updateFields.push(`is_active = $${paramIndex++}`);
        updateValues.push(isActive);
      }

      if (updateFields.length === 0) {
        return createErrorResponse('No fields to update', 400);
      }

      updateFields.push(`updated_at = CURRENT_TIMESTAMP`);
      updateValues.push(id);

      const updateSql = `
        UPDATE groups 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, name, level, teacher_id, max_students, schedule, is_active, created_at, updated_at
      `;

      const updatedGroupResult = await query<Group>(updateSql, updateValues);
      const updatedGroup = updatedGroupResult.rows[0];

      // Get teacher information if assigned
      let teacherInfo = null;
      if (updatedGroup.teacher_id) {
        const teacherResult = await query(
          'SELECT name, email FROM users WHERE id = $1',
          [updatedGroup.teacher_id]
        );
        if (teacherResult.rows.length > 0) {
          teacherInfo = teacherResult.rows[0];
        }
      }

      // Get current student count
      const currentStudentsResult = await query(
        'SELECT COUNT(*) as count FROM student_groups WHERE group_id = $1',
        [id]
      );
      const currentStudentCount = parseInt(currentStudentsResult.rows[0].count);

      // Log group update
      await logGroupOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedGroup,
        currentGroup,
        context
      );

      return createResponse({
        id: updatedGroup.id,
        name: updatedGroup.name,
        level: updatedGroup.level,
        teacherId: updatedGroup.teacher_id,
        teacherName: teacherInfo?.name,
        teacherEmail: teacherInfo?.email,
        maxStudents: updatedGroup.max_students,
        schedule: updatedGroup.schedule,
        isActive: updatedGroup.is_active,
        currentStudents: currentStudentCount,
        createdAt: updatedGroup.created_at,
        updatedAt: updatedGroup.updated_at
      }, true, 'Group updated successfully');

    } catch (dbError) {
      console.error('Database error updating group:', dbError);
      return createErrorResponse('Failed to update group', 500);
    }

  } catch (error) {
    console.error('Update group error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

// DELETE /api/groups/[id] - Deactivate specific group
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid group ID format', 400);
    }

    // Only management can deactivate groups
    if (authResult.user.role !== UserRole.MANAGEMENT) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const context = getRequestContext(request.headers);

    try {
      // Get current group data
      const currentGroupResult = await query<Group>(
        `SELECT id, name, level, teacher_id, max_students, schedule, is_active, created_at, updated_at 
         FROM groups WHERE id = $1`,
        [id]
      );

      if (currentGroupResult.rows.length === 0) {
        return createErrorResponse('Group not found', 404);
      }

      const currentGroup = currentGroupResult.rows[0];

      if (!currentGroup.is_active) {
        return createErrorResponse('Group is already inactive', 400);
      }

      // Check if group has active students
      const activeStudentsResult = await query(
        `SELECT COUNT(*) as count FROM student_groups sg
         JOIN students s ON sg.student_id = s.id
         WHERE sg.group_id = $1 AND s.status = 'active'`,
        [id]
      );
      const activeStudentCount = parseInt(activeStudentsResult.rows[0].count);

      if (activeStudentCount > 0) {
        return createErrorResponse(
          `Cannot deactivate group with ${activeStudentCount} active students. Please reassign students first.`,
          400
        );
      }

      // Deactivate group (soft delete)
      const deactivatedGroupResult = await query<Group>(
        `UPDATE groups 
         SET is_active = false, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1
         RETURNING id, name, level, teacher_id, max_students, schedule, is_active, created_at, updated_at`,
        [id]
      );

      const deactivatedGroup = deactivatedGroupResult.rows[0];

      // Log group deactivation
      await logGroupOperation(
        'DELETE' as any,
        authResult.user.id,
        deactivatedGroup,
        currentGroup,
        context
      );

      return createResponse({
        id: deactivatedGroup.id,
        name: deactivatedGroup.name,
        level: deactivatedGroup.level,
        teacherId: deactivatedGroup.teacher_id,
        maxStudents: deactivatedGroup.max_students,
        schedule: deactivatedGroup.schedule,
        isActive: deactivatedGroup.is_active,
        createdAt: deactivatedGroup.created_at,
        updatedAt: deactivatedGroup.updated_at
      }, true, 'Group deactivated successfully');

    } catch (dbError) {
      console.error('Database error deactivating group:', dbError);
      return createErrorResponse('Failed to deactivate group', 500);
    }

  } catch (error) {
    console.error('Deactivate group error:', error);
    return createErrorResponse('Failed to deactivate group', 500);
  }
}

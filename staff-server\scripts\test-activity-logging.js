/**
 * Test activity logging functionality
 * Verifies that activity logs are properly created and synced
 */

const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function testActivityLogging() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    console.log('🔍 Testing Activity Logging...');
    
    await client.connect();
    console.log('📡 Database connected successfully');

    // Check if activity_logs table exists
    const tableExistsResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'activity_logs'
      );
    `);

    if (!tableExistsResult.rows[0].exists) {
      console.log('❌ activity_logs table does not exist');
      return;
    }

    console.log('✅ activity_logs table exists');

    // Check recent activity logs
    const recentLogsResult = await client.query(`
      SELECT 
        id, user_id, action, resource_type, resource_id, 
        description, timestamp
      FROM activity_logs 
      ORDER BY timestamp DESC 
      LIMIT 10
    `);

    console.log(`📊 Found ${recentLogsResult.rows.length} recent activity logs:`);
    
    recentLogsResult.rows.forEach((log, index) => {
      console.log(`  ${index + 1}. [${log.timestamp}] ${log.action} ${log.resource_type} - ${log.description}`);
    });

    // Test activity log structure
    if (recentLogsResult.rows.length > 0) {
      const sampleLog = recentLogsResult.rows[0];
      console.log('\n📋 Sample activity log structure:');
      console.log('  ID:', sampleLog.id);
      console.log('  User ID:', sampleLog.user_id);
      console.log('  Action:', sampleLog.action);
      console.log('  Resource Type:', sampleLog.resource_type);
      console.log('  Resource ID:', sampleLog.resource_id);
      console.log('  Description:', sampleLog.description);
      console.log('  Timestamp:', sampleLog.timestamp);
    }

    // Check activity log statistics
    const statsResult = await client.query(`
      SELECT 
        action,
        resource_type,
        COUNT(*) as count
      FROM activity_logs 
      WHERE timestamp >= NOW() - INTERVAL '7 days'
      GROUP BY action, resource_type
      ORDER BY count DESC
    `);

    console.log('\n📈 Activity statistics (last 7 days):');
    statsResult.rows.forEach(stat => {
      console.log(`  ${stat.action} ${stat.resource_type}: ${stat.count} times`);
    });

    // Test admin integration configuration
    console.log('\n🔗 Admin Integration Configuration:');
    console.log('  ADMIN_SERVER_URL:', process.env.ADMIN_SERVER_URL || 'Not configured');
    console.log('  ADMIN_SERVER_API_KEY:', process.env.ADMIN_SERVER_API_KEY ? 'Configured' : 'Not configured');
    console.log('  ENABLE_ADMIN_INTEGRATION:', process.env.ENABLE_ADMIN_INTEGRATION || 'Not set');
    console.log('  ENABLE_ACTIVITY_LOGGING:', process.env.ENABLE_ACTIVITY_LOGGING || 'Not set');

    // Test if we can create a test activity log
    try {
      const testLogResult = await client.query(`
        INSERT INTO activity_logs (
          user_id, action, resource_type, description
        ) VALUES (
          '00000000-0000-0000-0000-000000000000',
          'TEST',
          'SYSTEM',
          'Activity logging test - ' || NOW()
        )
        RETURNING id, timestamp
      `);

      console.log('\n✅ Successfully created test activity log:', testLogResult.rows[0].id);
      
      // Clean up test log
      await client.query('DELETE FROM activity_logs WHERE id = $1', [testLogResult.rows[0].id]);
      console.log('🧹 Test log cleaned up');

    } catch (testError) {
      console.log('⚠️  Could not create test activity log:', testError.message);
    }

    console.log('\n🎉 Activity logging test completed!');

  } catch (error) {
    console.error('❌ Activity logging test failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await client.end();
  }
}

// Run the test
testActivityLogging();

/**
 * Final comprehensive integration test
 * Tests the complete staff server authentication and user management system
 */

const { default: fetch } = require('node-fetch');

const ADMIN_SERVER_URL = 'http://localhost:3000';
const STAFF_SERVER_URL = 'http://localhost:3001';

async function getAdminToken() {
  const response = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      email: '<EMAIL>',
      password: 'Admin123!'
    })
  });
  
  const data = await response.json();
  return data.success ? data.data.token : null;
}

async function testAdminUserManagement() {
  console.log('🔍 Testing admin user management...');
  
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ No admin token');
    return false;
  }
  
  // Test fetching all users (admin + staff)
  const [adminResponse, staffResponse] = await Promise.all([
    fetch(`${ADMIN_SERVER_URL}/api/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    }),
    fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
  ]);
  
  const adminData = await adminResponse.json();
  const staffData = await staffResponse.json();
  
  if (adminData.success && staffData.success) {
    console.log('✅ Admin can fetch both admin and staff users');
    console.log(`  - Admin users: ${adminData.data.users.length}`);
    console.log(`  - Staff users: ${staffData.data.users.length}`);
    return true;
  } else {
    console.log('❌ Failed to fetch users');
    return false;
  }
}

async function testStaffAuthentication() {
  console.log('\n🔍 Testing staff authentication...');
  
  const testUsers = [
    { email: '<EMAIL>', password: 'Manager123!', role: 'management' },
    { email: '<EMAIL>', password: 'Reception123!', role: 'reception' },
    { email: '<EMAIL>', password: 'Teacher123!', role: 'teacher' }
  ];
  
  let allPassed = true;
  
  for (const user of testUsers) {
    try {
      const response = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: user.email,
          password: user.password
        })
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`✅ ${user.role} authentication successful`);
      } else {
        console.log(`❌ ${user.role} authentication failed: ${data.error}`);
        allPassed = false;
      }
    } catch (error) {
      console.log(`❌ ${user.role} authentication error: ${error.message}`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function testStaffUserCreation() {
  console.log('\n🔍 Testing staff user creation via admin...');
  
  const token = await getAdminToken();
  if (!token) {
    console.log('❌ No admin token');
    return false;
  }
  
  const newUser = {
    email: `test-${Date.now()}@innovativecentre.com`,
    password: 'TestUser123!',
    role: 'teacher',
    name: 'Test Teacher User',
    isActive: true
  };
  
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newUser)
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log('✅ Staff user created successfully via admin');
      console.log(`  - User: ${data.data.name} (${data.data.email})`);
      console.log(`  - Role: ${data.data.role}`);
      console.log(`  - Server Type: ${data.data.server_type}`);
      
      // Test if the new user can authenticate
      console.log('🔍 Testing new user authentication...');
      const authResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: newUser.email,
          password: newUser.password
        })
      });
      
      const authData = await authResponse.json();
      
      if (authResponse.ok && authData.success) {
        console.log('✅ New staff user can authenticate successfully');
        return true;
      } else {
        console.log('❌ New staff user authentication failed');
        return false;
      }
    } else {
      console.log('❌ Staff user creation failed:', data.error);
      return false;
    }
  } catch (error) {
    console.log('❌ Staff user creation error:', error.message);
    return false;
  }
}

async function testInterServerCommunication() {
  console.log('\n🔍 Testing inter-server communication...');
  
  try {
    // Test admin server health
    const adminHealth = await fetch(`${ADMIN_SERVER_URL}/api/health`);
    const adminData = await adminHealth.json();
    
    // Test staff server health
    const staffHealth = await fetch(`${STAFF_SERVER_URL}/api/health`);
    const staffData = await staffHealth.json();
    
    if (adminHealth.ok && staffHealth.ok) {
      console.log('✅ Both servers are healthy');
      console.log(`  - Admin server: ${adminData.data?.status || 'OK'}`);
      console.log(`  - Staff server: ${staffData.status || 'OK'}`);
      return true;
    } else {
      console.log('❌ Server health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Server communication error:', error.message);
    return false;
  }
}

async function runFinalTest() {
  console.log('🚀 Final Integration Test - Staff Server Authentication System');
  console.log('='.repeat(70));
  
  const results = {
    serverCommunication: await testInterServerCommunication(),
    adminUserManagement: await testAdminUserManagement(),
    staffAuthentication: await testStaffAuthentication(),
    staffUserCreation: await testStaffUserCreation()
  };
  
  console.log('\n' + '='.repeat(70));
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log('='.repeat(70));
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASSED' : '❌ FAILED';
    const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
    console.log(`${status} - ${testName}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n' + '='.repeat(70));
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! 🎉');
    console.log('✨ Staff server authentication system is fully functional!');
    console.log('🔗 Admin server successfully manages both admin and staff users');
    console.log('🔐 Inter-server communication is working perfectly');
    console.log('👥 Staff users can be created and authenticated seamlessly');
  } else {
    console.log('❌ SOME TESTS FAILED');
    console.log('Please check the issues above and fix them.');
  }
  console.log('='.repeat(70));
  
  return allPassed;
}

runFinalTest().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});

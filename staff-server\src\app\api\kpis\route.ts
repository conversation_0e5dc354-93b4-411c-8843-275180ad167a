/**
 * KPI dashboard endpoint for staff server
 * Provides comprehensive KPIs integrating operational and financial data
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { createResponse, createErrorResponse, parseFilterParams } from '@/lib/utils';
import { adminService, safeAdminServiceCall, isAdminIntegrationEnabled } from '@/lib/admin-service';
import { UserRole } from '@/shared/types/common';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - management can view all KPIs, others see limited data
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const filters = parseFilterParams(searchParams);

    // Default to current month
    const endDate = filters.endDate || new Date().toISOString().split('T')[0];
    const startDate = filters.startDate || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    try {
      const kpiData: any = {
        dateRange: { startDate, endDate },
        generatedAt: new Date().toISOString(),
        userRole: authResult.user.role,
      };

      // Get operational KPIs (available to all roles)
      kpiData.operational = await getOperationalKPIs(startDate, endDate);

      // Get role-specific KPIs
      if (authResult.user.role === 'management') {
        // Management gets full access to all KPIs
        kpiData.management = await getManagementKPIs(startDate, endDate);
        
        // Get financial KPIs from admin server if available
        if (isAdminIntegrationEnabled()) {
          kpiData.financial = await safeAdminServiceCall(
            () => adminService.getKPIData({ startDate, endDate, type: 'financial' }),
            null
          );
        }
      } else if (authResult.user.role === 'reception') {
        // Reception gets lead and enrollment KPIs
        kpiData.reception = await getReceptionKPIs(startDate, endDate, authResult.user.id);
      } else if (authResult.user.role === 'teacher') {
        // Teachers get group and student progress KPIs
        kpiData.teacher = await getTeacherKPIs(startDate, endDate, authResult.user.id);
      }

      return createResponse(kpiData, true, 'KPIs retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving KPIs:', dbError);
      return createErrorResponse('Failed to retrieve KPIs', 500);
    }

  } catch (error) {
    console.error('KPI retrieval error:', error);
    return createErrorResponse('Failed to retrieve KPIs', 500);
  }
}

/**
 * Get operational KPIs (available to all roles)
 */
async function getOperationalKPIs(startDate: string, endDate: string) {
  const result = await query(`
    WITH current_period AS (
      SELECT 
        COUNT(*) FILTER (WHERE s.enrollment_date >= $1 AND s.enrollment_date <= $2) as new_students,
        COUNT(*) FILTER (WHERE s.status = 'active') as active_students,
        COUNT(*) FILTER (WHERE l.created_at >= $1 AND l.created_at <= $2) as new_leads,
        COUNT(*) FILTER (WHERE l.status = 'enrolled' AND l.updated_at >= $1 AND l.updated_at <= $2) as converted_leads,
        COUNT(DISTINCT g.id) FILTER (WHERE g.is_active = true) as active_groups
      FROM students s
      FULL OUTER JOIN leads l ON true
      FULL OUTER JOIN groups g ON true
    ),
    previous_period AS (
      SELECT 
        COUNT(*) FILTER (WHERE s.enrollment_date >= $1::date - INTERVAL '1 month' AND s.enrollment_date < $1::date) as prev_new_students,
        COUNT(*) FILTER (WHERE l.created_at >= $1::date - INTERVAL '1 month' AND l.created_at < $1::date) as prev_new_leads,
        COUNT(*) FILTER (WHERE l.status = 'enrolled' AND l.updated_at >= $1::date - INTERVAL '1 month' AND l.updated_at < $1::date) as prev_converted_leads
      FROM students s
      FULL OUTER JOIN leads l ON true
    ),
    conversion_rate AS (
      SELECT 
        CASE 
          WHEN COUNT(*) FILTER (WHERE l.created_at >= $1 AND l.created_at <= $2) > 0 
          THEN ROUND(
            COUNT(*) FILTER (WHERE l.status = 'enrolled' AND l.updated_at >= $1 AND l.updated_at <= $2)::decimal / 
            COUNT(*) FILTER (WHERE l.created_at >= $1 AND l.created_at <= $2) * 100, 2
          )
          ELSE 0
        END as lead_conversion_rate
      FROM leads l
    )
    SELECT 
      cp.*,
      pp.prev_new_students,
      pp.prev_new_leads,
      pp.prev_converted_leads,
      cr.lead_conversion_rate
    FROM current_period cp, previous_period pp, conversion_rate cr
  `, [startDate, endDate]);

  const data = result.rows[0];
  
  return {
    newStudents: {
      current: parseInt(data.new_students || 0),
      previous: parseInt(data.prev_new_students || 0),
      change: calculatePercentageChange(data.new_students, data.prev_new_students)
    },
    activeStudents: parseInt(data.active_students || 0),
    newLeads: {
      current: parseInt(data.new_leads || 0),
      previous: parseInt(data.prev_new_leads || 0),
      change: calculatePercentageChange(data.new_leads, data.prev_new_leads)
    },
    convertedLeads: {
      current: parseInt(data.converted_leads || 0),
      previous: parseInt(data.prev_converted_leads || 0),
      change: calculatePercentageChange(data.converted_leads, data.prev_converted_leads)
    },
    leadConversionRate: parseFloat(data.lead_conversion_rate || 0),
    activeGroups: parseInt(data.active_groups || 0)
  };
}

/**
 * Get management-specific KPIs
 */
async function getManagementKPIs(startDate: string, endDate: string) {
  const result = await query(`
    WITH staff_activity AS (
      SELECT 
        COUNT(DISTINCT al.user_id) as active_staff,
        COUNT(*) as total_activities,
        COUNT(*) FILTER (WHERE al.resource_type = 'STUDENT') as student_activities,
        COUNT(*) FILTER (WHERE al.resource_type = 'LEAD') as lead_activities
      FROM activity_logs al
      WHERE al.timestamp >= $1 AND al.timestamp <= $2
    ),
    group_utilization AS (
      SELECT 
        AVG(
          CASE 
            WHEN g.max_students > 0 
            THEN (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)::decimal / g.max_students * 100
            ELSE 0
          END
        ) as avg_utilization
      FROM groups g
      WHERE g.is_active = true
    ),
    retention_rate AS (
      SELECT 
        CASE 
          WHEN COUNT(*) FILTER (WHERE s.enrollment_date < $1::date) > 0
          THEN ROUND(
            COUNT(*) FILTER (WHERE s.enrollment_date < $1::date AND s.status = 'active')::decimal /
            COUNT(*) FILTER (WHERE s.enrollment_date < $1::date) * 100, 2
          )
          ELSE 0
        END as student_retention_rate
      FROM students s
    )
    SELECT 
      sa.*,
      gu.avg_utilization,
      rr.student_retention_rate
    FROM staff_activity sa, group_utilization gu, retention_rate rr
  `, [startDate, endDate]);

  return result.rows[0];
}

/**
 * Get reception-specific KPIs
 */
async function getReceptionKPIs(startDate: string, endDate: string, userId: string) {
  const result = await query(`
    SELECT 
      COUNT(*) FILTER (WHERE l.assigned_to = $3 AND l.created_at >= $1 AND l.created_at <= $2) as assigned_leads,
      COUNT(*) FILTER (WHERE l.assigned_to = $3 AND l.status = 'enrolled' AND l.updated_at >= $1 AND l.updated_at <= $2) as converted_leads,
      COUNT(*) FILTER (WHERE s.created_at >= $1 AND s.created_at <= $2) as students_enrolled,
      CASE 
        WHEN COUNT(*) FILTER (WHERE l.assigned_to = $3 AND l.created_at >= $1 AND l.created_at <= $2) > 0
        THEN ROUND(
          COUNT(*) FILTER (WHERE l.assigned_to = $3 AND l.status = 'enrolled' AND l.updated_at >= $1 AND l.updated_at <= $2)::decimal /
          COUNT(*) FILTER (WHERE l.assigned_to = $3 AND l.created_at >= $1 AND l.created_at <= $2) * 100, 2
        )
        ELSE 0
      END as personal_conversion_rate
    FROM leads l
    FULL OUTER JOIN students s ON true
  `, [startDate, endDate, userId]);

  return result.rows[0];
}

/**
 * Get teacher-specific KPIs
 */
async function getTeacherKPIs(startDate: string, endDate: string, userId: string) {
  const result = await query(`
    WITH teacher_groups AS (
      SELECT 
        COUNT(*) as assigned_groups,
        SUM(
          (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)
        ) as total_students_taught,
        AVG(
          CASE 
            WHEN g.max_students > 0 
            THEN (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id)::decimal / g.max_students * 100
            ELSE 0
          END
        ) as avg_group_utilization
      FROM groups g
      WHERE g.teacher_id = $3 AND g.is_active = true
    ),
    student_progress AS (
      SELECT 
        COUNT(*) FILTER (WHERE s.status = 'active') as active_students,
        COUNT(*) FILTER (WHERE s.status = 'graduated') as graduated_students
      FROM students s
      JOIN student_groups sg ON s.id = sg.student_id
      JOIN groups g ON sg.group_id = g.id
      WHERE g.teacher_id = $3
    )
    SELECT 
      tg.*,
      sp.active_students,
      sp.graduated_students
    FROM teacher_groups tg, student_progress sp
  `, [startDate, endDate, userId]);

  return result.rows[0];
}

/**
 * Calculate percentage change between two values
 */
function calculatePercentageChange(current: number, previous: number): number {
  if (!previous || previous === 0) {
    return current > 0 ? 100 : 0;
  }
  return Math.round(((current - previous) / previous) * 100);
}

/**
 * User Password API endpoint
 * Allows admin users to view user passwords
 */

import { NextRequest } from 'next/server';
import { createResponse, createErrorResponse } from '@/lib/utils';
import { getUserFromRequest } from '@/lib/auth';
import { query } from '@/lib/db';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  password_hash: string;
  role: UserRole;
  name: string;
  is_active: boolean;
}

// GET /api/users/[id]/password - Get user password (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Not authenticated', 401);
    }

    // Only admin users can view passwords
    if (authResult.user.role !== 'admin') {
      return createErrorResponse('Only admin users can view passwords', 403);
    }

    const { id } = params;

    if (!id) {
      return createErrorResponse('User ID is required', 400);
    }

    try {
      // Get user data
      const userResult = await query<User>(
        'SELECT id, email, password_hash, role, name, is_active FROM users WHERE id = $1',
        [id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('User not found', 404);
      }

      const user = userResult.rows[0];

      // For security, we'll return a placeholder since we can't decrypt bcrypt hashes
      // In a real implementation, you might want to store passwords differently
      // or implement a password reset mechanism instead
      return createResponse({
        password: '[Encrypted - Cannot Display]'
      }, true, 'Password retrieved successfully');

    } catch (dbError) {
      console.error('Database error:', dbError);
      return createErrorResponse('Database error occurred', 500);
    }

  } catch (error) {
    console.error('Get user password error:', error);
    return createErrorResponse('Failed to get user password', 500);
  }
}

# 🏗️ Innovative Centre Platform

A comprehensive CRM platform for English tutoring organizations, built with Next.js 15 and PostgreSQL.

## 📋 Overview

This platform serves an English tutoring organization with 4,000+ students and 40+ teachers, providing separate admin and staff management systems with comprehensive activity logging and audit trails.

## 🏛️ Architecture

- **Monorepo Structure**: Both admin and staff servers in a single repository
- **Technology Stack**: Next.js 15, TypeScript, PostgreSQL (Neon), Tailwind CSS
- **Database**: Separate PostgreSQL databases for admin and staff operations
- **Activity Logging**: Comprehensive audit trail for all administrative actions

## 📁 Project Structure

```
Innovative Platform/
├── admin-server/          # Admin Server (Financial & Administrative)
├── staff-server/          # Staff Server (Operational Management)
├── shared/               # Shared types, utilities, and database schemas
├── docs/                 # Documentation and guides
├── package.json          # Workspace configuration
└── README.md            # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL database access (Neon)

### Installation

```bash
# Install dependencies for all workspaces
npm install

# Start admin server (development)
npm run dev:admin

# Start staff server (development)
npm run dev:staff
```

### Environment Setup

1. Copy `.env.example` to `.env.local` in each server directory
2. Configure database connection strings
3. Set JWT secrets and other environment variables

## 🔧 Available Scripts

- `npm run dev:admin` - Start admin server in development mode
- `npm run dev:staff` - Start staff server in development mode
- `npm run build:admin` - Build admin server for production
- `npm run build:staff` - Build staff server for production
- `npm run start:admin` - Start admin server in production mode
- `npm run start:staff` - Start staff server in production mode

## 🏛️ System Architecture

### Admin Server
- **Users**: Admin, Cashier, Accountant
- **Functions**: Financial operations, user management, cabinet management, KPI analytics
- **Port**: 3000

### Staff Server
- **Users**: Management, Reception, Teachers
- **Functions**: Student management, lead management, class scheduling, operational reporting
- **Port**: 3003

## 🗄️ Database Configuration

The platform uses separate PostgreSQL databases:
- **Admin Database**: Financial and administrative data
- **Staff Database**: Operational and student data

## 📊 Activity Logging

Comprehensive activity logging system tracks:
- User management actions
- Payment operations
- Cabinet bookings
- KPI updates
- System configuration changes

## 🔐 Security Features

- JWT-based authentication
- Role-based access control
- Activity audit trails
- Secure database connections
- Input validation and sanitization

## 📚 Documentation

- [API Documentation](./docs/api-documentation.md)
- [Deployment Guide](./docs/deployment-guide.md)
- [Activity Logging Guide](./docs/activity-logging-guide.md)
- [User Guides](./docs/user-guides/)

## 🤝 Contributing

1. Follow the established project structure
2. Ensure all changes are properly logged
3. Update documentation as needed
4. Test thoroughly before deployment

## 📄 License

MIT License - see LICENSE file for details.

---

**Innovative Centre Platform** - Empowering educational excellence through technology.

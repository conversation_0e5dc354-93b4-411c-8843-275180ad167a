/**
 * Jest Environment Variables
 * Set up environment variables for testing
 */

// Set test environment variables - using real database for integration testing
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = process.env.JWT_SECRET || 'admin-jwt-secret-key-innovative-centre-2024';
// Use real admin database for integration testing
// process.env.DATABASE_URL will be loaded from .env.local
process.env.ENABLE_ACTIVITY_LOGGING = 'true'; // Enable for real testing

// Mock environment variables for testing
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000';
process.env.BCRYPT_ROUNDS = '4'; // Lower rounds for faster tests
process.env.JWT_EXPIRES_IN = '1h';
process.env.REFRESH_TOKEN_EXPIRES_IN = '7d';

// Rate limiting (disabled for tests)
process.env.RATE_LIMIT_WINDOW_MS = '0';
process.env.RATE_LIMIT_MAX_REQUESTS = '1000';

// File upload limits
process.env.MAX_FILE_SIZE = '10485760';
process.env.ALLOWED_FILE_TYPES = 'image/jpeg,image/png,image/gif,application/pdf';

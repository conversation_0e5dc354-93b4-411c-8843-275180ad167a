/**
 * Activity logging implementation for Admin Server
 * Handles comprehensive audit trail for all administrative actions
 */

import { query } from './db';
import { 
  ActivityAction, 
  ResourceType, 
  CreateActivityLogRequest,
  ActivityContext,
  generateActivityDescription,
  sanitizeLogData
} from '../../../shared/types/activity-log';
import { 
  ActivityLogger, 
  LogActivityParams,
  LogUserActionParams,
  LogSystemActionParams,
  ActivityLoggerConfig
} from '../../../shared/utils/activity-logger';

// Admin server specific activity logger configuration
const ADMIN_LOGGER_CONFIG: ActivityLoggerConfig = {
  enabled: process.env.ENABLE_ACTIVITY_LOGGING === 'true',
  sanitizeSensitiveData: true,
  maxLogSize: 1000000, // 1MB
  retentionDays: parseInt(process.env.LOG_RETENTION_DAYS || '365'),
  batchSize: 50,
  flushInterval: 3000 // 3 seconds
};

/**
 * Persist activity log to database
 */
async function persistActivityLog(log: CreateActivityLogRequest): Promise<void> {
  try {
    const sql = `
      INSERT INTO activity_logs (
        user_id, action, resource_type, resource_id,
        old_values, new_values, ip_address, user_agent, description
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;
    
    const params = [
      log.userId,
      log.action,
      log.resourceType,
      log.resourceId || null,
      log.oldValues ? JSON.stringify(log.oldValues) : null,
      log.newValues ? JSON.stringify(log.newValues) : null,
      log.ipAddress || null,
      log.userAgent || null,
      log.description || null
    ];
    
    await query(sql, params);
  } catch (error) {
    console.error('Failed to persist activity log:', error);
    // Don't throw error to avoid breaking the main operation
  }
}

// Create admin server activity logger instance
export const adminActivityLogger = new ActivityLogger(
  persistActivityLog,
  ADMIN_LOGGER_CONFIG
);

/**
 * Helper function to get client IP and user agent from request headers
 */
export function getRequestContext(headers: Headers): {
  ipAddress?: string;
  userAgent?: string;
} {
  const xForwardedFor = headers.get('x-forwarded-for');
  const xRealIP = headers.get('x-real-ip');
  const userAgent = headers.get('user-agent');
  
  let ipAddress: string | undefined;
  
  if (xForwardedFor) {
    ipAddress = xForwardedFor.split(',')[0].trim();
  } else if (xRealIP) {
    ipAddress = xRealIP;
  }
  
  return {
    ipAddress,
    userAgent: userAgent || undefined
  };
}

/**
 * Log user authentication events
 */
export async function logAuthEvent(
  action: 'LOGIN' | 'LOGOUT',
  userId: string,
  context: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  await adminActivityLogger.logUserAction({
    userId,
    action: action === 'LOGIN' ? ActivityAction.LOGIN : ActivityAction.LOGOUT,
    resourceType: ResourceType.USER,
    resourceId: userId,
    description: action === 'LOGIN' ? 'User logged in' : 'User logged out',
    ipAddress: context.ipAddress,
    userAgent: context.userAgent
  });
}

/**
 * Log user management operations
 */
export async function logUserOperation(
  action: ActivityAction,
  performedBy: string,
  targetUser: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  let newValues: any = undefined;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Created user: ${targetUser.email}`;
      newValues = sanitizeLogData(targetUser);
      break;
    case ActivityAction.UPDATE:
      description = `Updated user: ${targetUser.email}`;
      newValues = sanitizeLogData(targetUser);
      break;
    case ActivityAction.DELETE:
      description = `Deactivated user: ${targetUser.email}`;
      break;
    default:
      description = `${action} operation on user: ${targetUser.email}`;
  }
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.USER,
    resourceId: targetUser.id,
    oldValues: oldValues ? sanitizeLogData(oldValues) : undefined,
    newValues,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log payment operations
 */
export async function logPaymentOperation(
  action: ActivityAction,
  performedBy: string,
  payment: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Recorded payment of $${payment.amount} for student ${payment.studentId}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated payment ${payment.id}`;
      break;
    default:
      description = `${action} operation on payment ${payment.id}`;
  }
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.PAYMENT,
    resourceId: payment.id,
    oldValues,
    newValues: payment,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log invoice operations
 */
export async function logInvoiceOperation(
  action: ActivityAction,
  performedBy: string,
  invoice: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Generated invoice for $${invoice.amount} (student: ${invoice.studentId})`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated invoice ${invoice.id}`;
      break;
    default:
      description = `${action} operation on invoice ${invoice.id}`;
  }
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.INVOICE,
    resourceId: invoice.id,
    oldValues,
    newValues: invoice,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log cabinet operations
 */
export async function logCabinetOperation(
  action: ActivityAction,
  performedBy: string,
  cabinet: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Created cabinet: ${cabinet.name}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated cabinet: ${cabinet.name}`;
      break;
    default:
      description = `${action} operation on cabinet: ${cabinet.name}`;
  }
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.CABINET,
    resourceId: cabinet.id,
    oldValues,
    newValues: cabinet,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log cabinet booking operations
 */
export async function logBookingOperation(
  action: ActivityAction,
  performedBy: string,
  booking: any,
  oldValues?: any,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  let description: string;
  
  switch (action) {
    case ActivityAction.CREATE:
      description = `Created booking for cabinet ${booking.cabinetId} on ${booking.date}`;
      break;
    case ActivityAction.UPDATE:
      description = `Updated booking ${booking.id}`;
      break;
    case ActivityAction.DELETE:
      description = `Cancelled booking ${booking.id}`;
      break;
    default:
      description = `${action} operation on booking ${booking.id}`;
  }
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action,
    resourceType: ResourceType.BOOKING,
    resourceId: booking.id,
    oldValues,
    newValues: booking,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Log report generation and export operations
 */
export async function logReportOperation(
  action: 'EXPORT' | 'VIEW',
  performedBy: string,
  reportType: string,
  context?: { ipAddress?: string; userAgent?: string }
): Promise<void> {
  const description = action === 'EXPORT' 
    ? `Exported ${reportType} report` 
    : `Viewed ${reportType} report`;
  
  await adminActivityLogger.logUserAction({
    userId: performedBy,
    action: action === 'EXPORT' ? ActivityAction.EXPORT : ActivityAction.VIEW,
    resourceType: 'REPORT' as ResourceType,
    description,
    ipAddress: context?.ipAddress,
    userAgent: context?.userAgent
  });
}

/**
 * Cleanup old activity logs based on retention policy
 */
export async function cleanupOldLogs(): Promise<void> {
  try {
    const retentionDays = ADMIN_LOGGER_CONFIG.retentionDays;
    const sql = `
      DELETE FROM activity_logs 
      WHERE timestamp < NOW() - INTERVAL '${retentionDays} days'
    `;
    
    const result = await query(sql);
    console.log(`Cleaned up ${result.rowCount} old activity logs`);
  } catch (error) {
    console.error('Failed to cleanup old activity logs:', error);
  }
}

// Export the logger instance for direct use
export { adminActivityLogger as activityLogger };

/**
 * Test Staff Service Real Communication
 * Verifies that the staff service can communicate with the staff server
 */

// Use built-in fetch for Node.js 18+
const fetch = globalThis.fetch || require('node-fetch');

// Configuration from environment
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
const STAFF_SERVER_URL = process.env.STAFF_SERVER_URL || 'http://localhost:3003';
const STAFF_SERVER_API_KEY = process.env.STAFF_SERVER_API_KEY || 'staff-server-api-key-innovative-centre-2024';

console.log('🔧 Testing Staff Service Configuration...');
console.log(`Admin Server URL: ${ADMIN_SERVER_URL}`);
console.log(`Staff Server URL: ${STAFF_SERVER_URL}`);
console.log(`API Key configured: ${STAFF_SERVER_API_KEY ? 'Yes' : 'No'}`);

/**
 * Test if staff server is running and accessible
 */
async function testStaffServerHealth() {
  console.log('\n🏥 Testing Staff Server Health...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/health`, {
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Staff Server is running and healthy');
      console.log('Health data:', data);
      return true;
    } else {
      console.log(`❌ Staff Server health check failed: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Staff Server is not accessible: ${error.message}`);
    console.log('💡 Make sure to start the staff server with: npm run dev:staff');
    return false;
  }
}

/**
 * Test staff service API key authentication
 */
async function testStaffServiceAuth() {
  console.log('\n🔐 Testing Staff Service API Authentication...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/students`, {
      method: 'GET',
      headers: {
        'X-API-Key': STAFF_SERVER_API_KEY,
        'X-Source-Service': 'admin-server',
        'Content-Type': 'application/json'
      },
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Staff Service API authentication successful');
      console.log(`Retrieved ${data.data?.students?.length || 0} students`);
      return true;
    } else {
      console.log(`❌ Staff Service API authentication failed: ${response.status} ${response.statusText}`);
      const errorData = await response.text();
      console.log('Error details:', errorData);
      return false;
    }
  } catch (error) {
    console.log(`❌ Staff Service API request failed: ${error.message}`);
    return false;
  }
}

/**
 * Test admin server staff integration endpoints
 */
async function testAdminStaffIntegration() {
  console.log('\n🔗 Testing Admin Server Staff Integration Endpoints...');
  
  try {
    // Test the staff integration health endpoint
    const response = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/activity-sync`, {
      method: 'POST',
      headers: {
        'X-API-Key': STAFF_SERVER_API_KEY,
        'X-Source-Service': 'staff-server',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userId: 'health-check-user',
        action: 'HEALTH_CHECK',
        resourceType: 'SYSTEM',
        description: 'Staff service health check',
        sourceService: 'staff-server',
        timestamp: new Date().toISOString()
      }),
      timeout: 5000
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Admin Server staff integration endpoints working');
      console.log('Response:', data);
      return true;
    } else {
      console.log(`❌ Admin Server staff integration failed: ${response.status} ${response.statusText}`);
      const errorData = await response.text();
      console.log('Error details:', errorData);
      return false;
    }
  } catch (error) {
    console.log(`❌ Admin Server staff integration request failed: ${error.message}`);
    return false;
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting Staff Service Communication Tests...\n');
  
  const results = {
    staffServerHealth: await testStaffServerHealth(),
    staffServiceAuth: false,
    adminStaffIntegration: false
  };
  
  // Only run other tests if staff server is healthy
  if (results.staffServerHealth) {
    results.staffServiceAuth = await testStaffServiceAuth();
    results.adminStaffIntegration = await testAdminStaffIntegration();
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log(`Staff Server Health: ${results.staffServerHealth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Staff Service Auth: ${results.staffServiceAuth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin Staff Integration: ${results.adminStaffIntegration ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\n🎉 All tests passed! Staff Service communication is working properly.');
    console.log('✅ Real interserver communication is configured and functional.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the configuration and ensure both servers are running.');
    console.log('💡 To start both servers:');
    console.log('   Terminal 1: npm run dev:admin');
    console.log('   Terminal 2: npm run dev:staff');
  }
  
  process.exit(allPassed ? 0 : 1);
}

// Run the tests
runTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});

/**
 * Authentication utilities unit tests
 */

import { 
  hashPassword, 
  verifyPassword, 
  generateTokens, 
  verifyToken, 
  extractTokenFromHeader,
  hasRole,
  hasPermission,
  validatePasswordStrength
} from '../../src/lib/auth';
import { UserRole } from '../../../shared/types/common';

// Mock environment variables
process.env.JWT_SECRET = 'test-secret-key';
process.env.JWT_EXPIRES_IN = '1h';

describe('Authentication Utilities', () => {
  describe('Password Hashing', () => {
    it('should hash password correctly', async () => {
      const password = 'testPassword123';
      const hash = await hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      expect(hash.length).toBeGreaterThan(50);
    });

    it('should verify correct password', async () => {
      const password = 'testPassword123';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(password, hash);
      
      expect(isValid).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'testPassword123';
      const wrongPassword = 'wrongPassword';
      const hash = await hashPassword(password);
      const isValid = await verifyPassword(wrongPassword, hash);
      
      expect(isValid).toBe(false);
    });
  });

  describe('Token Generation and Verification', () => {
    const testUser = {
      id: 'test-user-id',
      email: '<EMAIL>',
      role: UserRole.MANAGEMENT
    };

    it('should generate valid tokens', () => {
      const tokens = generateTokens(testUser);
      
      expect(tokens.token).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(tokens.expiresIn).toBeGreaterThan(0);
    });

    it('should verify valid token', () => {
      const tokens = generateTokens(testUser);
      const payload = verifyToken(tokens.token);
      
      expect(payload).toBeDefined();
      expect(payload?.userId).toBe(testUser.id);
      expect(payload?.email).toBe(testUser.email);
      expect(payload?.role).toBe(testUser.role);
    });

    it('should reject invalid token', () => {
      const invalidToken = 'invalid.token.here';
      const payload = verifyToken(invalidToken);
      
      expect(payload).toBeNull();
    });

    it('should extract token from authorization header', () => {
      const token = 'test-token-123';
      const authHeader = `Bearer ${token}`;
      const extractedToken = extractTokenFromHeader(authHeader);
      
      expect(extractedToken).toBe(token);
    });

    it('should return null for invalid authorization header', () => {
      const invalidHeader = 'Invalid header format';
      const extractedToken = extractTokenFromHeader(invalidHeader);
      
      expect(extractedToken).toBeNull();
    });

    it('should return null for null authorization header', () => {
      const extractedToken = extractTokenFromHeader(null);
      
      expect(extractedToken).toBeNull();
    });
  });

  describe('Role-based Access Control', () => {
    it('should check if user has required role', () => {
      expect(hasRole(UserRole.MANAGEMENT, [UserRole.MANAGEMENT])).toBe(true);
      expect(hasRole(UserRole.RECEPTION, [UserRole.MANAGEMENT, UserRole.RECEPTION])).toBe(true);
      expect(hasRole(UserRole.TEACHER, [UserRole.MANAGEMENT])).toBe(false);
    });

    it('should check permissions for management role', () => {
      expect(hasPermission(UserRole.MANAGEMENT, 'students', 'create')).toBe(true);
      expect(hasPermission(UserRole.MANAGEMENT, 'leads', 'delete')).toBe(true);
      expect(hasPermission(UserRole.MANAGEMENT, 'groups', 'update')).toBe(true);
    });

    it('should check permissions for reception role', () => {
      expect(hasPermission(UserRole.RECEPTION, 'students', 'create')).toBe(true);
      expect(hasPermission(UserRole.RECEPTION, 'leads', 'update')).toBe(true);
      expect(hasPermission(UserRole.RECEPTION, 'groups', 'read')).toBe(true);
      expect(hasPermission(UserRole.RECEPTION, 'groups', 'create')).toBe(false);
    });

    it('should check permissions for teacher role', () => {
      expect(hasPermission(UserRole.TEACHER, 'students', 'read')).toBe(true);
      expect(hasPermission(UserRole.TEACHER, 'groups', 'update')).toBe(true);
      expect(hasPermission(UserRole.TEACHER, 'students', 'create')).toBe(false);
      expect(hasPermission(UserRole.TEACHER, 'leads', 'read')).toBe(false);
    });
  });

  describe('Password Strength Validation', () => {
    it('should validate strong password', () => {
      const result = validatePasswordStrength('StrongPass123!');
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject password that is too short', () => {
      const result = validatePasswordStrength('Short1');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should reject password without lowercase letter', () => {
      const result = validatePasswordStrength('PASSWORD123');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should reject password without uppercase letter', () => {
      const result = validatePasswordStrength('password123');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should reject password without number', () => {
      const result = validatePasswordStrength('PasswordOnly');
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should return multiple errors for weak password', () => {
      const result = validatePasswordStrength('weak');
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(1);
    });
  });
});

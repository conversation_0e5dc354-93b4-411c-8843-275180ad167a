/**
 * Complete end-to-end test of the staff authentication system
 */

const { default: fetch } = require('node-fetch');

const ADMIN_SERVER_URL = 'http://localhost:3000';
const STAFF_SERVER_URL = 'http://localhost:3001';

async function testCompleteFlow() {
  console.log('🚀 Complete End-to-End Staff Authentication Test');
  console.log('='.repeat(60));
  
  // Step 1: <PERSON><PERSON> creates a new staff user
  console.log('\n📝 Step 1: Ad<PERSON> creates a new staff user');
  
  const adminToken = await getAdminToken();
  if (!adminToken) {
    console.log('❌ Failed to get admin token');
    return false;
  }
  
  const newStaffUser = {
    email: `testflow-${Date.now()}@innovativecentre.com`,
    password: 'TestFlow123!',
    role: 'reception',
    name: 'Test Flow User',
    isActive: true
  };
  
  const createResponse = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(newStaffUser)
  });
  
  const createData = await createResponse.json();
  
  if (!createResponse.ok || !createData.success) {
    console.log('❌ Failed to create staff user:', createData.error);
    return false;
  }
  
  console.log('✅ Staff user created successfully');
  console.log(`  - Email: ${createData.data.email}`);
  console.log(`  - Role: ${createData.data.role}`);
  console.log(`  - Server Type: ${createData.data.server_type}`);
  
  // Step 2: New staff user logs in to staff server
  console.log('\n🔐 Step 2: New staff user logs in to staff server');
  
  const loginResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      email: newStaffUser.email,
      password: newStaffUser.password
    })
  });
  
  const loginData = await loginResponse.json();
  
  if (!loginResponse.ok || !loginData.success) {
    console.log('❌ Staff login failed:', loginData.error);
    return false;
  }
  
  console.log('✅ Staff login successful');
  console.log(`  - User: ${loginData.data.user.name}`);
  console.log(`  - Role: ${loginData.data.user.role}`);
  console.log(`  - Token received: ${!!loginData.data.token}`);
  
  // Step 3: Staff user accesses protected endpoint
  console.log('\n🔒 Step 3: Staff user accesses protected endpoint');
  
  const meResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/me`, {
    headers: {
      'Authorization': `Bearer ${loginData.data.token}`
    }
  });
  
  const meData = await meResponse.json();
  
  if (!meResponse.ok || !meData.success) {
    console.log('❌ Protected endpoint access failed:', meData.error);
    return false;
  }
  
  console.log('✅ Protected endpoint access successful');
  console.log(`  - User ID: ${meData.data.id}`);
  console.log(`  - Email: ${meData.data.email}`);
  console.log(`  - Role: ${meData.data.role}`);
  
  // Step 4: Admin can see the new user in the unified user list
  console.log('\n👥 Step 4: Admin verifies user in unified user list');
  
  const staffUsersResponse = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/users`, {
    headers: {
      'Authorization': `Bearer ${adminToken}`
    }
  });
  
  const staffUsersData = await staffUsersResponse.json();
  
  if (!staffUsersResponse.ok || !staffUsersData.success) {
    console.log('❌ Failed to fetch staff users:', staffUsersData.error);
    return false;
  }
  
  const userExists = staffUsersData.data.users.some(user => user.email === newStaffUser.email);
  
  if (!userExists) {
    console.log('❌ New user not found in staff users list');
    return false;
  }
  
  console.log('✅ New user found in admin\'s staff users list');
  console.log(`  - Total staff users: ${staffUsersData.data.users.length}`);
  
  console.log('\n' + '='.repeat(60));
  console.log('🎉 COMPLETE FLOW TEST PASSED! 🎉');
  console.log('✨ The entire staff authentication system is working perfectly:');
  console.log('  1. ✅ Admin can create staff users');
  console.log('  2. ✅ Staff users can login to staff server');
  console.log('  3. ✅ Authentication is handled by admin server');
  console.log('  4. ✅ Staff users can access protected endpoints');
  console.log('  5. ✅ Admin has unified view of all users');
  console.log('='.repeat(60));
  
  return true;
}

async function getAdminToken() {
  try {
    const response = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!'
      })
    });
    
    const data = await response.json();
    return data.success ? data.data.token : null;
  } catch (error) {
    console.error('Admin login error:', error.message);
    return null;
  }
}

testCompleteFlow().catch(error => {
  console.error('❌ Complete flow test failed:', error);
  process.exit(1);
});

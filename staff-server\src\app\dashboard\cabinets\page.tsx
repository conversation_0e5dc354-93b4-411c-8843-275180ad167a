'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';

interface Cabinet {
  id: string;
  name: string;
  capacity: number;
  equipment: string[];
  hourlyRate?: number;
  isAvailable: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Booking {
  id: string;
  cabinetId: string;
  date: string;
  startTime: string;
  endTime: string;
  bookedBy: string;
  purpose?: string;
  status: 'confirmed' | 'pending' | 'cancelled';
  bookedByName?: string;
  createdAt: string;
}

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  bookings: Booking[];
}

export default function CabinetsPage() {
  const { user } = useAuth();
  const [cabinets, setCabinets] = useState<Cabinet[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCabinet, setSelectedCabinet] = useState<Cabinet | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [showBookingModal, setShowBookingModal] = useState(false);

  // Fetch cabinets from admin server
  const fetchCabinets = async () => {
    try {
      const adminServerUrl = process.env.NEXT_PUBLIC_ADMIN_SERVER_URL || 'http://localhost:3000';
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${adminServerUrl}/api/cabinets`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch cabinets');
      }

      const data = await response.json();
      if (data.success) {
        setCabinets(data.data.cabinets || []);
      } else {
        throw new Error(data.message || 'Failed to fetch cabinets');
      }
    } catch (error) {
      console.error('Error fetching cabinets:', error);
      setError('Failed to load cabinets from admin server');
    }
  };

  // Fetch bookings for selected cabinet
  const fetchBookings = async (cabinetId: string) => {
    try {
      const adminServerUrl = process.env.NEXT_PUBLIC_ADMIN_SERVER_URL || 'http://localhost:3000';
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${adminServerUrl}/api/cabinets/${cabinetId}/bookings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch bookings');
      }

      const data = await response.json();
      if (data.success) {
        setBookings(data.data.bookings || []);
      } else {
        throw new Error(data.message || 'Failed to fetch bookings');
      }
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setError('Failed to load bookings');
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await fetchCabinets();
      setLoading(false);
    };

    loadData();
  }, []);

  useEffect(() => {
    if (selectedCabinet) {
      fetchBookings(selectedCabinet.id);
    }
  }, [selectedCabinet]);

  // Generate calendar days
  const generateCalendarDays = (): CalendarDay[] => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days: CalendarDay[] = [];
    const today = new Date();
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const dayBookings = bookings.filter(booking => {
        const bookingDate = new Date(booking.date);
        return bookingDate.toDateString() === date.toDateString();
      });
      
      days.push({
        date,
        isCurrentMonth: date.getMonth() === month,
        isToday: date.toDateString() === today.toDateString(),
        bookings: dayBookings
      });
    }
    
    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cabinets...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const calendarDays = selectedCabinet ? generateCalendarDays() : [];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cabinet Management</h1>
          <p className="text-gray-600">View and manage cabinet bookings from admin server</p>
        </div>
        <div className="flex items-center space-x-4">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('calendar')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'calendar' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              Calendar View
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                viewMode === 'list' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              List View
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Cabinet Selection Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Select Cabinet</h3>
            <div className="space-y-2">
              {cabinets.length === 0 ? (
                <p className="text-gray-500 text-sm">No cabinets available</p>
              ) : (
                cabinets.map((cabinet) => (
                  <button
                    key={cabinet.id}
                    onClick={() => setSelectedCabinet(cabinet)}
                    className={`w-full text-left p-3 rounded-lg border transition-colors ${
                      selectedCabinet?.id === cabinet.id
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="font-medium">{cabinet.name}</div>
                    <div className="text-sm text-gray-600">
                      Capacity: {cabinet.capacity}
                      {cabinet.hourlyRate && ` • $${cabinet.hourlyRate}/hr`}
                    </div>
                    <div className="flex items-center mt-1">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        cabinet.isAvailable 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {cabinet.isAvailable ? 'Available' : 'Unavailable'}
                      </span>
                    </div>
                  </button>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {!selectedCabinet ? (
            <div className="bg-white rounded-lg shadow p-12 text-center">
              <div className="text-gray-400 text-6xl mb-4">🏢</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Cabinet</h3>
              <p className="text-gray-600">Choose a cabinet from the sidebar to view its booking calendar</p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow">
              {/* Cabinet Header */}
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">{selectedCabinet.name}</h3>
                    <p className="text-sm text-gray-600">
                      Capacity: {selectedCabinet.capacity} people
                      {selectedCabinet.hourlyRate && ` • $${selectedCabinet.hourlyRate}/hour`}
                    </p>
                  </div>
                  <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                    selectedCabinet.isAvailable 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedCabinet.isAvailable ? 'Available' : 'Unavailable'}
                  </span>
                </div>
                {selectedCabinet.equipment && selectedCabinet.equipment.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">Equipment:</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {selectedCabinet.equipment.map((item, index) => (
                        <span
                          key={index}
                          className="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                        >
                          {item}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Calendar/List Content */}
              <div className="p-6">
                {viewMode === 'calendar' ? (
                  <div>
                    {/* Calendar Header */}
                    <div className="flex justify-between items-center mb-6">
                      <h4 className="text-lg font-medium text-gray-900">
                        {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                      </h4>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => navigateMonth('prev')}
                          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                          </svg>
                        </button>
                        <button
                          onClick={() => navigateMonth('next')}
                          className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded"
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Calendar Grid */}
                    <div className="grid grid-cols-7 gap-1">
                      {/* Day headers */}
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                        <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                          {day}
                        </div>
                      ))}
                      
                      {/* Calendar days */}
                      {calendarDays.map((day, index) => (
                        <div
                          key={index}
                          className={`min-h-[100px] p-2 border border-gray-200 ${
                            !day.isCurrentMonth ? 'bg-gray-50 text-gray-400' : 'bg-white'
                          } ${day.isToday ? 'bg-blue-50 border-blue-200' : ''}`}
                        >
                          <div className={`text-sm font-medium mb-1 ${
                            day.isToday ? 'text-blue-600' : day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'
                          }`}>
                            {day.date.getDate()}
                          </div>
                          <div className="space-y-1">
                            {day.bookings.slice(0, 2).map((booking) => (
                              <div
                                key={booking.id}
                                className={`text-xs p-1 rounded truncate ${
                                  booking.status === 'confirmed' 
                                    ? 'bg-green-100 text-green-800'
                                    : booking.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-red-100 text-red-800'
                                }`}
                                title={`${formatTime(booking.startTime)} - ${formatTime(booking.endTime)}: ${booking.purpose || 'No purpose'}`}
                              >
                                {formatTime(booking.startTime)}
                              </div>
                            ))}
                            {day.bookings.length > 2 && (
                              <div className="text-xs text-gray-500">
                                +{day.bookings.length - 2} more
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  /* List View */
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Upcoming Bookings</h4>
                    {bookings.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="text-gray-400 text-4xl mb-4">📅</div>
                        <p className="text-gray-500">No bookings found for this cabinet</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {bookings
                          .filter(booking => new Date(booking.date) >= new Date())
                          .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                          .map((booking) => (
                            <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="flex items-center space-x-2 mb-2">
                                    <span className="font-medium text-gray-900">
                                      {new Date(booking.date).toLocaleDateString('en-US', {
                                        weekday: 'long',
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                      })}
                                    </span>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      booking.status === 'confirmed' 
                                        ? 'bg-green-100 text-green-800'
                                        : booking.status === 'pending'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : 'bg-red-100 text-red-800'
                                    }`}>
                                      {booking.status}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-600 mb-1">
                                    Time: {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                                  </p>
                                  {booking.purpose && (
                                    <p className="text-sm text-gray-600 mb-1">Purpose: {booking.purpose}</p>
                                  )}
                                  {booking.bookedByName && (
                                    <p className="text-sm text-gray-600">Booked by: {booking.bookedByName}</p>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

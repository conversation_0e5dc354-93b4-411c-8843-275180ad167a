/**
 * Staff user management endpoint for admin server
 * Handles CRUD operations for staff users from admin interface
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, hashPassword } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidEmail
} from '@/lib/utils';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';
import { buildWhereClause } from '@/lib/db';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  server_type: string;
  created_at: Date;
  updated_at: Date;
}

// GET /api/staff-integration/users - List staff users
export async function GET(request: NextRequest) {
  try {
    // Authenticate admin user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only admin users can manage staff users
    if (authResult.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering staff users only
      let whereClause = 'WHERE server_type = $1';
      const queryParams: any[] = ['staff'];
      let paramIndex = 2;

      if (filters.search) {
        whereClause += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex})`;
        queryParams.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.role && ['management', 'reception', 'teacher'].includes(filters.role)) {
        whereClause += ` AND role = $${paramIndex}`;
        queryParams.push(filters.role);
        paramIndex++;
      }

      if (filters.isActive !== undefined) {
        whereClause += ` AND is_active = $${paramIndex}`;
        queryParams.push(filters.isActive === 'true');
        paramIndex++;
      }

      // Get total count
      const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const countResult = await query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated users
      const offset = (pagination.page - 1) * pagination.limit;
      const usersQuery = `
        SELECT id, email, role, name, is_active, server_type, created_at, updated_at
        FROM users
        ${whereClause}
        ORDER BY ${pagination.sortBy || 'created_at'} ${pagination.sortOrder || 'DESC'}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const usersResult = await query<User>(usersQuery, [...queryParams, pagination.limit, offset]);

      const totalPages = Math.ceil(total / pagination.limit);

      return createResponse({
        users: usersResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages,
          hasNext: pagination.page < totalPages,
          hasPrev: pagination.page > 1
        }
      }, true, 'Staff users retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving staff users:', dbError);
      return createErrorResponse('Failed to retrieve staff users', 500);
    }

  } catch (error) {
    console.error('Get staff users error:', error);
    return createErrorResponse('Failed to retrieve staff users', 500);
  }
}

// POST /api/staff-integration/users - Create new staff user
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only admin users can create staff users
    if (authResult.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { email, password, role, name, isActive = true } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['email', 'password', 'role', 'name']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate staff role
    const staffRoles = ['management', 'reception', 'teacher'];
    if (!staffRoles.includes(role)) {
      return createErrorResponse('Invalid role. Must be management, reception, or teacher', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if user already exists
      const existingUserResult = await query(
        'SELECT id FROM users WHERE email = $1',
        [email.toLowerCase()]
      );

      if (existingUserResult.rows.length > 0) {
        return createErrorResponse('User with this email already exists', 409);
      }

      // Hash password
      const passwordHash = await hashPassword(password);

      // Create staff user
      const userResult = await query<User>(
        `INSERT INTO users (email, password_hash, role, name, is_active, server_type)
         VALUES ($1, $2, $3, $4, $5, 'staff')
         RETURNING id, email, role, name, is_active, server_type, created_at, updated_at`,
        [email.toLowerCase(), passwordHash, role, name, isActive]
      );

      const newUser = userResult.rows[0];

      // Log user creation
      await logUserOperation(
        'CREATE' as any,
        authResult.user.id,
        newUser,
        undefined,
        context
      );

      return createResponse(newUser, true, 'Staff user created successfully');

    } catch (dbError) {
      console.error('Database error creating staff user:', dbError);
      
      if (dbError instanceof Error && dbError.message.includes('duplicate key')) {
        return createErrorResponse('User with this email already exists', 409);
      }
      
      return createErrorResponse('Failed to create staff user', 500);
    }

  } catch (error) {
    console.error('Create staff user error:', error);
    return createErrorResponse('Failed to create staff user', 500);
  }
}

/**
 * Comprehensive Test Scenarios for Innovative Centre Platform
 * Tests different user roles, edge cases, and system functionality
 */

require('dotenv').config({ path: '../staff-server/.env.local' });

const TEST_USERS = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!',
    expectedRole: 'admin',
    server: 'admin'
  },
  cashier: {
    email: '<EMAIL>',
    password: 'Cashier123!',
    expectedRole: 'cashier',
    server: 'admin'
  },
  accountant: {
    email: '<EMAIL>',
    password: 'Accountant123!',
    expectedRole: 'accountant',
    server: 'admin'
  },
  manager: {
    email: '<EMAIL>',
    password: 'Manager123!',
    expectedRole: 'management',
    server: 'staff'
  },
  reception: {
    email: '<EMAIL>',
    password: 'Reception123!',
    expectedRole: 'reception',
    server: 'staff'
  },
  teacher: {
    email: '<EMAIL>',
    password: 'Teacher123!',
    expectedRole: 'teacher',
    server: 'staff'
  }
};

const SERVERS = {
  admin: 'http://localhost:3000',
  staff: 'http://localhost:3003'
};

class ComprehensiveTestSuite {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async runTest(testName, testFunction) {
    console.log(`\n🧪 Running: ${testName}`);
    try {
      await testFunction();
      console.log(`✅ PASSED: ${testName}`);
      this.results.passed++;
      this.results.tests.push({ name: testName, status: 'PASSED' });
    } catch (error) {
      console.log(`❌ FAILED: ${testName} - ${error.message}`);
      this.results.failed++;
      this.results.tests.push({ name: testName, status: 'FAILED', error: error.message });
    }
  }

  async testUserAuthentication(userType) {
    const user = TEST_USERS[userType];
    const serverUrl = SERVERS[user.server];

    const response = await fetch(`${serverUrl}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: user.email, password: user.password })
    });

    if (!response.ok) {
      throw new Error(`Login failed for ${userType}: ${response.status}`);
    }

    const data = await response.json();
    if (data.data?.user?.role !== user.expectedRole) {
      throw new Error(`Role mismatch for ${userType}: expected ${user.expectedRole}, got ${data.data?.user?.role}`);
    }

    return data.data.token;
  }

  async testStudentCreation(userType) {
    const user = TEST_USERS[userType];
    const serverUrl = SERVERS[user.server];
    
    // Only test student creation for staff users
    if (user.server !== 'staff') {
      throw new Error(`${userType} should not be able to create students`);
    }

    const token = await this.testUserAuthentication(userType);
    
    const studentData = {
      firstName: `Test${userType}`,
      lastName: 'Student',
      email: `test.${userType}.${Date.now()}@example.com`,
      phone: '+998901234567',
      dateOfBirth: '2000-01-01',
      enrollmentDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };

    const response = await fetch(`${serverUrl}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(studentData)
    });

    if (!response.ok) {
      throw new Error(`Student creation failed for ${userType}: ${response.status}`);
    }

    const result = await response.json();
    return result.data.id;
  }

  async testActivityLogging(userType, studentId) {
    // Wait for activity to be processed
    await new Promise(resolve => setTimeout(resolve, 2000));

    const adminServerUrl = SERVERS.admin;
    const response = await fetch(`${adminServerUrl}/api/activity-logs?limit=5&action=CREATE&resourceType=STUDENT`);

    if (!response.ok) {
      throw new Error('Failed to fetch activity logs');
    }

    const data = await response.json();
    const logs = data.data?.logs || [];
    
    const recentLog = logs.find(log => 
      log.description && log.description.includes(`Test${userType} Student`)
    );

    if (!recentLog) {
      throw new Error(`Activity log not found for ${userType} student creation`);
    }
  }

  async testInterserverCommunication() {
    const adminServerUrl = SERVERS.admin;
    const staffServerUrl = SERVERS.staff;

    // Test admin server health
    const adminHealth = await fetch(`${adminServerUrl}/api/health`);
    if (!adminHealth.ok) {
      throw new Error('Admin server health check failed');
    }

    // Test staff server health
    const staffHealth = await fetch(`${staffServerUrl}/api/health`);
    if (!staffHealth.ok) {
      throw new Error('Staff server health check failed');
    }

    // Test API key authentication
    const apiKey = process.env.ADMIN_SERVER_API_KEY;
    if (!apiKey) {
      throw new Error('Admin server API key not configured');
    }

    const testSync = await fetch(`${adminServerUrl}/api/staff-integration/activity-sync`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
        'X-Source-Service': 'staff-server'
      },
      body: JSON.stringify({
        userId: 'ef8ad1f1-3c13-46d5-a75b-df41589da456',
        action: 'TEST',
        resourceType: 'SYSTEM',
        description: 'Interserver communication test',
        sourceService: 'staff-server',
        timestamp: new Date().toISOString()
      })
    });

    if (!testSync.ok) {
      throw new Error(`Interserver communication test failed: ${testSync.status}`);
    }
  }

  async testEdgeCases() {
    // Test invalid authentication
    const invalidAuth = await fetch(`${SERVERS.staff}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: '<EMAIL>', password: 'wrong' })
    });

    if (invalidAuth.ok) {
      throw new Error('Invalid authentication should fail');
    }

    // Test missing required fields in student creation
    const token = await this.testUserAuthentication('reception');
    
    const invalidStudent = await fetch(`${SERVERS.staff}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ firstName: 'Test' }) // Missing required fields
    });

    if (invalidStudent.ok) {
      throw new Error('Student creation with missing fields should fail');
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Comprehensive Test Suite for Innovative Centre Platform\n');

    // Test 1: User Authentication for all roles
    for (const userType of Object.keys(TEST_USERS)) {
      await this.runTest(`User Authentication - ${userType}`, () => 
        this.testUserAuthentication(userType)
      );
    }

    // Test 2: Student Creation for staff users
    const staffUsers = ['manager', 'reception', 'teacher'];
    const studentIds = {};
    
    for (const userType of staffUsers) {
      await this.runTest(`Student Creation - ${userType}`, async () => {
        studentIds[userType] = await this.testStudentCreation(userType);
      });
    }

    // Test 3: Activity Logging
    for (const userType of staffUsers) {
      if (studentIds[userType]) {
        await this.runTest(`Activity Logging - ${userType}`, () =>
          this.testActivityLogging(userType, studentIds[userType])
        );
      }
    }

    // Test 4: Interserver Communication
    await this.runTest('Interserver Communication', () =>
      this.testInterserverCommunication()
    );

    // Test 5: Edge Cases
    await this.runTest('Edge Cases and Error Handling', () =>
      this.testEdgeCases()
    );

    // Print results
    console.log('\n📊 Test Results Summary:');
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);

    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.tests
        .filter(test => test.status === 'FAILED')
        .forEach(test => console.log(`  - ${test.name}: ${test.error}`));
    }

    console.log('\n🎉 Comprehensive test suite completed!');
  }
}

// Run the test suite
const testSuite = new ComprehensiveTestSuite();
testSuite.runAllTests().catch(console.error);

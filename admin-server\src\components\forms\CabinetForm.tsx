/**
 * Cabinet Form Component
 * Form for creating and editing cabinets
 */

'use client';

import React, { useState } from 'react';

interface CabinetFormData {
  name: string;
  capacity: string;
  equipment: string[];
  hourlyRate: string;
  isAvailable: boolean;
}

interface CabinetFormProps {
  onSubmit: (data: CabinetFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  initialData?: Partial<CabinetFormData>;
  mode?: 'create' | 'edit';
}

const COMMON_EQUIPMENT = [
  'Projector',
  'Whiteboard',
  'Computer',
  'Audio System',
  'Air Conditioning',
  'WiFi',
  'Microphone',
  'Screen',
  'Chairs',
  'Tables'
];

export default function CabinetForm({
  onSubmit,
  onCancel,
  isLoading = false,
  initialData = {},
  mode = 'create'
}: CabinetFormProps) {
  const [formData, setFormData] = useState<CabinetFormData>({
    name: initialData.name || '',
    capacity: initialData.capacity || '',
    equipment: initialData.equipment || [],
    hourlyRate: initialData.hourlyRate || '',
    isAvailable: initialData.isAvailable !== undefined ? initialData.isAvailable : true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [newEquipment, setNewEquipment] = useState('');

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate name
    if (!formData.name.trim()) {
      newErrors.name = 'Cabinet name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Cabinet name must be at least 2 characters';
    }

    // Validate capacity
    if (!formData.capacity.trim()) {
      newErrors.capacity = 'Capacity is required';
    } else {
      const capacity = parseInt(formData.capacity);
      if (isNaN(capacity) || capacity <= 0) {
        newErrors.capacity = 'Capacity must be a positive number';
      } else if (capacity > 1000) {
        newErrors.capacity = 'Capacity cannot exceed 1000';
      }
    }

    // Validate hourly rate if provided
    if (formData.hourlyRate.trim()) {
      const rate = parseFloat(formData.hourlyRate);
      if (isNaN(rate) || rate < 0) {
        newErrors.hourlyRate = 'Hourly rate must be a non-negative number';
      } else if (rate > 10000) {
        newErrors.hourlyRate = 'Hourly rate cannot exceed $10,000';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting cabinet:', error);
    }
  };

  const handleInputChange = (field: keyof CabinetFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addEquipment = (equipment: string) => {
    if (equipment && !formData.equipment.includes(equipment)) {
      handleInputChange('equipment', [...formData.equipment, equipment]);
    }
  };

  const removeEquipment = (equipment: string) => {
    handleInputChange('equipment', formData.equipment.filter(item => item !== equipment));
  };

  const addCustomEquipment = () => {
    if (newEquipment.trim() && !formData.equipment.includes(newEquipment.trim())) {
      addEquipment(newEquipment.trim());
      setNewEquipment('');
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900">
          {mode === 'create' ? 'Create New Cabinet' : 'Edit Cabinet'}
        </h3>
        <p className="mt-1 text-sm text-gray-600">
          {mode === 'create' 
            ? 'Add a new cabinet to the system with its details and equipment.'
            : 'Update cabinet information and equipment list.'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Cabinet Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Cabinet Name *
          </label>
          <input
            type="text"
            id="name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="e.g., Conference Room A, Lab 101"
            disabled={isLoading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Capacity */}
        <div>
          <label htmlFor="capacity" className="block text-sm font-medium text-gray-700 mb-1">
            Capacity (people) *
          </label>
          <input
            type="number"
            id="capacity"
            min="1"
            max="1000"
            value={formData.capacity}
            onChange={(e) => handleInputChange('capacity', e.target.value)}
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.capacity ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Maximum number of people"
            disabled={isLoading}
          />
          {errors.capacity && (
            <p className="mt-1 text-sm text-red-600">{errors.capacity}</p>
          )}
        </div>

        {/* Hourly Rate */}
        <div>
          <label htmlFor="hourlyRate" className="block text-sm font-medium text-gray-700 mb-1">
            Hourly Rate (UZS)
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 sm:text-sm">сўм</span>
            </div>
            <input
              type="number"
              id="hourlyRate"
              step="1"
              min="0"
              max="10000000"
              value={formData.hourlyRate}
              onChange={(e) => handleInputChange('hourlyRate', e.target.value)}
              className={`w-full pl-7 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.hourlyRate ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0"
              disabled={isLoading}
            />
          </div>
          {errors.hourlyRate && (
            <p className="mt-1 text-sm text-red-600">{errors.hourlyRate}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Optional. Leave empty if no hourly rate applies.
          </p>
        </div>

        {/* Equipment */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Equipment & Amenities
          </label>
          
          {/* Common Equipment Buttons */}
          <div className="mb-3">
            <p className="text-xs text-gray-500 mb-2">Quick add common equipment:</p>
            <div className="flex flex-wrap gap-2">
              {COMMON_EQUIPMENT.map((equipment) => (
                <button
                  key={equipment}
                  type="button"
                  onClick={() => addEquipment(equipment)}
                  disabled={formData.equipment.includes(equipment) || isLoading}
                  className={`px-3 py-1 text-xs rounded-full border ${
                    formData.equipment.includes(equipment)
                      ? 'bg-blue-100 text-blue-800 border-blue-200 cursor-not-allowed'
                      : 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  {equipment}
                  {formData.equipment.includes(equipment) && ' ✓'}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Equipment Input */}
          <div className="flex gap-2 mb-3">
            <input
              type="text"
              value={newEquipment}
              onChange={(e) => setNewEquipment(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addCustomEquipment())}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Add custom equipment..."
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={addCustomEquipment}
              disabled={!newEquipment.trim() || isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Add
            </button>
          </div>

          {/* Selected Equipment List */}
          {formData.equipment.length > 0 && (
            <div className="border border-gray-200 rounded-md p-3">
              <p className="text-sm font-medium text-gray-700 mb-2">Selected Equipment:</p>
              <div className="flex flex-wrap gap-2">
                {formData.equipment.map((equipment) => (
                  <span
                    key={equipment}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                  >
                    {equipment}
                    <button
                      type="button"
                      onClick={() => removeEquipment(equipment)}
                      disabled={isLoading}
                      className="ml-2 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Availability */}
        <div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="isAvailable"
              checked={formData.isAvailable}
              onChange={(e) => handleInputChange('isAvailable', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <label htmlFor="isAvailable" className="ml-2 block text-sm text-gray-700">
              Cabinet is available for booking
            </label>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Uncheck to temporarily disable bookings for this cabinet.
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading}
          >
            {isLoading ? (
              <div className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {mode === 'create' ? 'Creating...' : 'Updating...'}
              </div>
            ) : (
              mode === 'create' ? 'Create Cabinet' : 'Update Cabinet'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}

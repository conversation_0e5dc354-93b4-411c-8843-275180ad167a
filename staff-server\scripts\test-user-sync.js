/**
 * Test user authentication and activity sync with real user data
 */

require('dotenv').config({ path: '.env.local' });

async function testUserSync() {
  console.log('🔍 Testing User Authentication and Activity Sync...');
  
  const adminServerUrl = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
  const apiKey = process.env.ADMIN_SERVER_API_KEY;
  
  console.log('🔗 Admin Server URL:', adminServerUrl);
  console.log('🔑 API Key configured:', !!apiKey);
  
  if (!apiKey) {
    console.error('❌ ADMIN_SERVER_API_KEY not configured');
    return;
  }

  try {
    // First, let's get a list of staff users from admin server
    console.log('\n👥 Getting staff users from admin server...');
    
    const usersResponse = await fetch(`${adminServerUrl}/api/staff-integration/users`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
        'X-Source-Service': 'staff-server',
      },
    });

    if (!usersResponse.ok) {
      console.error('❌ Failed to get staff users:', usersResponse.status, usersResponse.statusText);
      return;
    }

    const usersData = await usersResponse.json();
    console.log('📋 Staff users response:', JSON.stringify(usersData, null, 2));

    if (usersData.success && usersData.data && usersData.data.users && usersData.data.users.length > 0) {
      const staffUser = usersData.data.users[0]; // Use first staff user
      console.log('\n🧪 Testing activity sync with staff user:', staffUser.id, staffUser.email);

      // Test activity sync with real staff user ID
      const testActivityData = {
        userId: staffUser.id,
        action: 'CREATE',
        resourceType: 'STUDENT',
        resourceId: '00000000-0000-0000-0000-000000000002',
        description: 'Test student creation from staff server',
        sourceService: 'staff-server',
        timestamp: new Date().toISOString(),
        newValues: {
          firstName: 'Test',
          lastName: 'Student',
          email: '<EMAIL>'
        }
      };

      console.log('\n📤 Sending test activity with real user ID...');
      
      const activityResponse = await fetch(`${adminServerUrl}/api/staff-integration/activity-sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
          'X-Source-Service': 'staff-server',
        },
        body: JSON.stringify(testActivityData),
      });

      console.log('📡 Activity sync response status:', activityResponse.status);
      const activityResponseText = await activityResponse.text();
      console.log('📡 Activity sync response:', activityResponseText);

      if (activityResponse.ok) {
        console.log('✅ Activity sync with real user ID successful!');
        
        // Check if the activity appears in admin server logs
        console.log('\n🔍 Checking admin server activity logs...');
        
        const logsResponse = await fetch(`${adminServerUrl}/api/activity-logs?limit=5`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': apiKey,
          },
        });

        if (logsResponse.ok) {
          const logsData = await logsResponse.json();
          console.log('📊 Recent activity logs:', JSON.stringify(logsData.data?.logs?.slice(0, 3), null, 2));
        }
      } else {
        console.log('❌ Activity sync with real user ID failed');
      }
    } else {
      console.log('⚠️  No staff users found or invalid response format');
    }

  } catch (error) {
    console.error('❌ Error testing user sync:', error.message);
    console.error('Error details:', error);
  }

  console.log('\n🎉 User sync test completed!');
}

// Run the test
testUserSync();

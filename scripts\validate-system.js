/**
 * Comprehensive system validation script
 * Tests all components of the Innovative Centre Platform
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
const STAFF_SERVER_URL = process.env.STAFF_SERVER_URL || 'http://localhost:3001';
const API_KEY = process.env.STAFF_SERVER_API_KEY || 'test-api-key';

// Test results tracking
const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: []
};

/**
 * Log test result
 */
function logResult(testName, status, message = '', details = null) {
  const result = { name: testName, status, message, details, timestamp: new Date().toISOString() };
  results.tests.push(result);
  
  const icon = status === 'PASSED' ? '✅' : status === 'FAILED' ? '❌' : '⚠️';
  console.log(`${icon} ${testName}: ${message}`);
  
  if (details) {
    console.log(`   Details: ${JSON.stringify(details, null, 2)}`);
  }
  
  if (status === 'PASSED') results.passed++;
  else if (status === 'FAILED') results.failed++;
  else results.warnings++;
}

/**
 * Test server health and connectivity
 */
async function testServerHealth() {
  console.log('\n🏥 Testing Server Health...');
  
  try {
    // Test Admin Server
    const adminResponse = await fetch(`${ADMIN_SERVER_URL}/api/health`, { timeout: 5000 });
    if (adminResponse.ok) {
      const adminData = await adminResponse.json();
      logResult('Admin Server Health', 'PASSED', 'Server is healthy', adminData);
    } else {
      logResult('Admin Server Health', 'FAILED', `HTTP ${adminResponse.status}`);
    }
  } catch (error) {
    logResult('Admin Server Health', 'FAILED', error.message);
  }
  
  try {
    // Test Staff Server
    const staffResponse = await fetch(`${STAFF_SERVER_URL}/api/health`, { timeout: 5000 });
    if (staffResponse.ok) {
      const staffData = await staffResponse.json();
      logResult('Staff Server Health', 'PASSED', 'Server is healthy', staffData);
    } else {
      logResult('Staff Server Health', 'FAILED', `HTTP ${staffResponse.status}`);
    }
  } catch (error) {
    logResult('Staff Server Health', 'FAILED', error.message);
  }
}

/**
 * Test database connectivity
 */
async function testDatabaseConnectivity() {
  console.log('\n🗄️ Testing Database Connectivity...');
  
  try {
    // Test Admin Database
    const adminDbResponse = await fetch(`${ADMIN_SERVER_URL}/api/db-status`);
    if (adminDbResponse.ok) {
      const adminDbData = await adminDbResponse.json();
      logResult('Admin Database', 'PASSED', 'Database connected', adminDbData);
    } else {
      logResult('Admin Database', 'FAILED', `HTTP ${adminDbResponse.status}`);
    }
  } catch (error) {
    logResult('Admin Database', 'FAILED', error.message);
  }
  
  try {
    // Test Staff Database
    const staffDbResponse = await fetch(`${STAFF_SERVER_URL}/api/db-status`);
    if (staffDbResponse.ok) {
      const staffDbData = await staffDbResponse.json();
      logResult('Staff Database', 'PASSED', 'Database connected', staffDbData);
    } else {
      logResult('Staff Database', 'FAILED', `HTTP ${staffDbResponse.status}`);
    }
  } catch (error) {
    logResult('Staff Database', 'FAILED', error.message);
  }
}

/**
 * Test file structure and configuration
 */
async function testFileStructure() {
  console.log('\n📁 Testing File Structure...');
  
  const requiredFiles = [
    'admin-server/package.json',
    'admin-server/.env.example',
    'admin-server/src/app/api/health/route.ts',
    'staff-server/package.json',
    'staff-server/.env.example',
    'staff-server/src/app/api/health/route.ts',
    'shared/types/common.ts',
    'shared/database/admin-schema.sql',
    'shared/database/staff-schema.sql',
    'scripts/test-integration.js',
    'DEPLOYMENT.md'
  ];
  
  for (const file of requiredFiles) {
    try {
      if (fs.existsSync(file)) {
        logResult(`File Structure: ${file}`, 'PASSED', 'File exists');
      } else {
        logResult(`File Structure: ${file}`, 'FAILED', 'File missing');
      }
    } catch (error) {
      logResult(`File Structure: ${file}`, 'FAILED', error.message);
    }
  }
}

/**
 * Test environment configuration
 */
async function testEnvironmentConfig() {
  console.log('\n⚙️ Testing Environment Configuration...');
  
  // Check required environment variables
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'NODE_ENV'
  ];
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      logResult(`Environment: ${envVar}`, 'PASSED', 'Variable is set');
    } else {
      logResult(`Environment: ${envVar}`, 'WARNING', 'Variable not set');
    }
  }
  
  // Check integration configuration
  if (process.env.ADMIN_SERVER_URL && process.env.STAFF_SERVER_API_KEY) {
    logResult('Integration Config', 'PASSED', 'Integration variables set');
  } else {
    logResult('Integration Config', 'WARNING', 'Integration variables missing');
  }
}

/**
 * Test API endpoints
 */
async function testAPIEndpoints() {
  console.log('\n🔌 Testing API Endpoints...');
  
  const endpoints = [
    { server: 'admin', url: `${ADMIN_SERVER_URL}/api/health`, name: 'Admin Health' },
    { server: 'admin', url: `${ADMIN_SERVER_URL}/api/db-status`, name: 'Admin DB Status' },
    { server: 'staff', url: `${STAFF_SERVER_URL}/api/health`, name: 'Staff Health' },
    { server: 'staff', url: `${STAFF_SERVER_URL}/api/db-status`, name: 'Staff DB Status' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint.url, { timeout: 5000 });
      if (response.ok) {
        logResult(`API: ${endpoint.name}`, 'PASSED', `HTTP ${response.status}`);
      } else {
        logResult(`API: ${endpoint.name}`, 'FAILED', `HTTP ${response.status}`);
      }
    } catch (error) {
      logResult(`API: ${endpoint.name}`, 'FAILED', error.message);
    }
  }
}

/**
 * Test integration between servers
 */
async function testIntegration() {
  console.log('\n🔗 Testing Server Integration...');
  
  try {
    // Test integration status from staff server
    const integrationResponse = await fetch(`${STAFF_SERVER_URL}/api/admin-integration/status`, {
      headers: {
        'X-API-Key': API_KEY,
        'X-Source-Service': 'validation-script'
      }
    });
    
    if (integrationResponse.ok) {
      const integrationData = await integrationResponse.json();
      if (integrationData.data?.adminServer?.isConnected) {
        logResult('Server Integration', 'PASSED', 'Servers are connected', integrationData.data);
      } else {
        logResult('Server Integration', 'WARNING', 'Servers not connected', integrationData.data);
      }
    } else {
      logResult('Server Integration', 'FAILED', `HTTP ${integrationResponse.status}`);
    }
  } catch (error) {
    logResult('Server Integration', 'FAILED', error.message);
  }
}

/**
 * Test security configuration
 */
async function testSecurity() {
  console.log('\n🔒 Testing Security Configuration...');
  
  // Test HTTPS enforcement (in production)
  if (process.env.NODE_ENV === 'production') {
    const servers = [
      { name: 'Admin Server', url: ADMIN_SERVER_URL },
      { name: 'Staff Server', url: STAFF_SERVER_URL }
    ];
    
    for (const server of servers) {
      if (server.url.startsWith('https://')) {
        logResult(`Security: ${server.name} HTTPS`, 'PASSED', 'Using HTTPS');
      } else {
        logResult(`Security: ${server.name} HTTPS`, 'WARNING', 'Not using HTTPS in production');
      }
    }
  } else {
    logResult('Security: HTTPS', 'PASSED', 'Development mode - HTTP acceptable');
  }
  
  // Test JWT secret configuration
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length >= 32) {
    logResult('Security: JWT Secret', 'PASSED', 'JWT secret is properly configured');
  } else {
    logResult('Security: JWT Secret', 'WARNING', 'JWT secret should be at least 32 characters');
  }
}

/**
 * Generate validation report
 */
function generateReport() {
  console.log('\n' + '='.repeat(60));
  console.log('SYSTEM VALIDATION REPORT');
  console.log('='.repeat(60));
  console.log(`Generated: ${new Date().toISOString()}`);
  console.log(`Total Tests: ${results.passed + results.failed + results.warnings}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Warnings: ${results.warnings}`);
  console.log('');
  
  // Group results by category
  const categories = {};
  results.tests.forEach(test => {
    const category = test.name.split(':')[0];
    if (!categories[category]) categories[category] = [];
    categories[category].push(test);
  });
  
  Object.keys(categories).forEach(category => {
    console.log(`${category}:`);
    categories[category].forEach(test => {
      const icon = test.status === 'PASSED' ? '✅' : test.status === 'FAILED' ? '❌' : '⚠️';
      console.log(`  ${icon} ${test.name.split(':').slice(1).join(':').trim() || test.name}`);
    });
    console.log('');
  });
  
  // Save detailed report
  const reportPath = path.join(__dirname, '..', 'validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    summary: {
      timestamp: new Date().toISOString(),
      total: results.passed + results.failed + results.warnings,
      passed: results.passed,
      failed: results.failed,
      warnings: results.warnings
    },
    tests: results.tests,
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      adminServerUrl: ADMIN_SERVER_URL,
      staffServerUrl: STAFF_SERVER_URL
    }
  }, null, 2));
  
  console.log(`Detailed report saved to: ${reportPath}`);
  console.log('='.repeat(60));
  
  // Exit with appropriate code
  if (results.failed > 0) {
    console.log('❌ System validation failed. Please address the failed tests.');
    process.exit(1);
  } else if (results.warnings > 0) {
    console.log('⚠️ System validation completed with warnings. Review recommended.');
    process.exit(0);
  } else {
    console.log('✅ System validation passed successfully!');
    process.exit(0);
  }
}

/**
 * Main validation function
 */
async function validateSystem() {
  console.log('🚀 Starting Comprehensive System Validation...');
  console.log(`Admin Server: ${ADMIN_SERVER_URL}`);
  console.log(`Staff Server: ${STAFF_SERVER_URL}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  
  try {
    await testFileStructure();
    await testEnvironmentConfig();
    await testServerHealth();
    await testDatabaseConnectivity();
    await testAPIEndpoints();
    await testIntegration();
    await testSecurity();
  } catch (error) {
    console.error('💥 Validation failed with error:', error);
    logResult('System Validation', 'FAILED', error.message);
  }
  
  generateReport();
}

// Run validation if this script is executed directly
if (require.main === module) {
  validateSystem().catch(error => {
    console.error('💥 System validation crashed:', error);
    process.exit(1);
  });
}

module.exports = { validateSystem };

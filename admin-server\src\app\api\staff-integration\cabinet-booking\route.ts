/**
 * Cabinet booking request endpoint for staff server integration
 * Handles booking requests from the staff server
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { createResponse, createErrorResponse, validateRequiredFields, isValidUUID } from '@/lib/utils';

// Validate API key for inter-service communication
function validateApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('X-API-Key');
  const expectedKey = process.env.STAFF_SERVER_API_KEY;
  
  if (!expectedKey) {
    console.warn('STAFF_SERVER_API_KEY not configured');
    return false;
  }
  
  return apiKey === expectedKey;
}

export async function POST(request: NextRequest) {
  try {
    // Validate API key
    if (!validateApiKey(request)) {
      return createErrorResponse('Invalid API key', 401);
    }

    // Validate source service
    const sourceService = request.headers.get('X-Source-Service');
    if (sourceService !== 'staff-server') {
      return createErrorResponse('Invalid source service', 400);
    }

    const body = await request.json();
    const {
      cabinetId,
      groupId,
      groupName,
      teacherId,
      teacherName,
      date,
      startTime,
      endTime,
      requestedBy
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, [
      'cabinetId', 'groupId', 'groupName', 'teacherId', 'teacherName',
      'date', 'startTime', 'endTime', 'requestedBy'
    ]);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate UUID formats
    if (!isValidUUID(cabinetId) || !isValidUUID(teacherId) || !isValidUUID(requestedBy)) {
      return createErrorResponse('Invalid UUID format for cabinetId, teacherId, or requestedBy', 400);
    }

    // Validate date and time
    const bookingDate = new Date(date);
    if (isNaN(bookingDate.getTime())) {
      return createErrorResponse('Invalid date format', 400);
    }

    // Check if date is not in the past
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (bookingDate < today) {
      return createErrorResponse('Cannot book cabinet for past dates', 400);
    }

    // Validate time format (HH:MM)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
      return createErrorResponse('Invalid time format. Use HH:MM format', 400);
    }

    // Check if start time is before end time
    if (startTime >= endTime) {
      return createErrorResponse('Start time must be before end time', 400);
    }

    try {
      // Check if cabinet exists and is available
      const cabinetResult = await query(
        'SELECT id, name, is_available FROM cabinets WHERE id = $1',
        [cabinetId]
      );

      if (cabinetResult.rows.length === 0) {
        return createErrorResponse('Cabinet not found', 404);
      }

      const cabinet = cabinetResult.rows[0];
      if (!cabinet.is_available) {
        return createErrorResponse('Cabinet is not available for booking', 400);
      }

      // Check for conflicting bookings
      const conflictResult = await query(
        `SELECT id FROM cabinet_bookings 
         WHERE cabinet_id = $1 AND date = $2 
         AND status IN ('confirmed', 'pending')
         AND (
           (start_time <= $3 AND end_time > $3) OR
           (start_time < $4 AND end_time >= $4) OR
           (start_time >= $3 AND end_time <= $4)
         )`,
        [cabinetId, date, startTime, endTime]
      );

      if (conflictResult.rows.length > 0) {
        return createErrorResponse('Cabinet is already booked for the requested time slot', 409);
      }

      // Create the booking
      const bookingResult = await query(
        `INSERT INTO cabinet_bookings (
           cabinet_id, date, start_time, end_time, booked_by, 
           purpose, status, source_service, external_reference
         ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
         RETURNING id, cabinet_id, date, start_time, end_time, booked_by, 
                   purpose, status, created_at, updated_at`,
        [
          cabinetId,
          date,
          startTime,
          endTime,
          requestedBy,
          `Group: ${groupName} (Teacher: ${teacherName})`,
          'confirmed',
          'staff-server',
          JSON.stringify({ groupId, groupName, teacherId, teacherName })
        ]
      );

      const booking = bookingResult.rows[0];

      // Log the booking creation
      await query(
        `INSERT INTO activity_logs (
           user_id, action, resource_type, resource_id,
           description, source_service
         ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          requestedBy,
          'CREATE',
          'CABINET_BOOKING',
          booking.id,
          `Cabinet booking created for group ${groupName} by staff server`,
          'staff-server'
        ]
      );

      return createResponse({
        success: true,
        bookingId: booking.id,
        cabinet: {
          id: cabinet.id,
          name: cabinet.name
        },
        booking: {
          id: booking.id,
          date: booking.date,
          startTime: booking.start_time,
          endTime: booking.end_time,
          purpose: booking.purpose,
          status: booking.status,
          createdAt: booking.created_at
        },
        group: {
          id: groupId,
          name: groupName,
          teacherId,
          teacherName
        }
      }, true, 'Cabinet booking created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating cabinet booking:', dbError);
      return createErrorResponse('Failed to create cabinet booking', 500);
    }

  } catch (error) {
    console.error('Cabinet booking request error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}

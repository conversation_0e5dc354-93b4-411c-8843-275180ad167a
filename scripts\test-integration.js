/**
 * Integration test script for Admin and Staff servers
 * Tests cross-server communication and data synchronization
 */

const fetch = require('node-fetch');

// Configuration
const ADMIN_SERVER_URL = process.env.ADMIN_SERVER_URL || 'http://localhost:3000';
const STAFF_SERVER_URL = process.env.STAFF_SERVER_URL || 'http://localhost:3001';
const API_KEY = process.env.STAFF_SERVER_API_KEY || 'test-api-key';

// Test credentials
const TEST_CREDENTIALS = {
  admin: {
    email: '<EMAIL>',
    password: 'Admin123!'
  },
  staff: {
    email: '<EMAIL>',
    password: 'Management123!'
  }
};

let adminToken = '';
let staffToken = '';

/**
 * Test authentication on both servers
 */
async function testAuthentication() {
  console.log('🔐 Testing authentication...');
  
  try {
    // Test admin server login
    const adminLoginResponse = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(TEST_CREDENTIALS.admin)
    });
    
    if (!adminLoginResponse.ok) {
      throw new Error(`Admin login failed: ${adminLoginResponse.status}`);
    }
    
    const adminLoginData = await adminLoginResponse.json();
    adminToken = adminLoginData.data.token;
    console.log('✅ Admin server authentication successful');
    
    // Test staff server login
    const staffLoginResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(TEST_CREDENTIALS.staff)
    });
    
    if (!staffLoginResponse.ok) {
      throw new Error(`Staff login failed: ${staffLoginResponse.status}`);
    }
    
    const staffLoginData = await staffLoginResponse.json();
    staffToken = staffLoginData.data.token;
    console.log('✅ Staff server authentication successful');
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error.message);
    throw error;
  }
}

/**
 * Test server health endpoints
 */
async function testServerHealth() {
  console.log('🏥 Testing server health...');
  
  try {
    // Test admin server health
    const adminHealthResponse = await fetch(`${ADMIN_SERVER_URL}/api/health`);
    if (!adminHealthResponse.ok) {
      throw new Error(`Admin server health check failed: ${adminHealthResponse.status}`);
    }
    console.log('✅ Admin server is healthy');
    
    // Test staff server health
    const staffHealthResponse = await fetch(`${STAFF_SERVER_URL}/api/health`);
    if (!staffHealthResponse.ok) {
      throw new Error(`Staff server health check failed: ${staffHealthResponse.status}`);
    }
    console.log('✅ Staff server is healthy');
    
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    throw error;
  }
}

/**
 * Test admin integration status from staff server
 */
async function testAdminIntegrationStatus() {
  console.log('🔗 Testing admin integration status...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/admin-integration/status`, {
      headers: { 'Authorization': `Bearer ${staffToken}` }
    });
    
    if (!response.ok) {
      throw new Error(`Integration status check failed: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ Admin integration status:', data.data.adminServer.isConnected ? 'Connected' : 'Disconnected');
    
    return data.data;
  } catch (error) {
    console.error('❌ Integration status test failed:', error.message);
    throw error;
  }
}

/**
 * Test student enrollment and admin notification
 */
async function testStudentEnrollmentSync() {
  console.log('👥 Testing student enrollment synchronization...');
  
  try {
    // Create a test student in staff server
    const studentData = {
      firstName: 'Integration',
      lastName: 'Test',
      email: '<EMAIL>',
      phone: '+1234567890',
      enrollmentDate: new Date().toISOString().split('T')[0],
      status: 'active'
    };
    
    const createResponse = await fetch(`${STAFF_SERVER_URL}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${staffToken}`
      },
      body: JSON.stringify(studentData)
    });
    
    if (!createResponse.ok) {
      throw new Error(`Student creation failed: ${createResponse.status}`);
    }
    
    const createdStudent = await createResponse.json();
    console.log('✅ Student created in staff server:', createdStudent.data.id);
    
    // Wait a moment for synchronization
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check if student record exists in admin server
    const checkResponse = await fetch(`${ADMIN_SERVER_URL}/api/staff-integration/student-financial/${createdStudent.data.id}`, {
      headers: {
        'X-API-Key': API_KEY,
        'X-Source-Service': 'staff-server'
      }
    });
    
    if (checkResponse.ok) {
      console.log('✅ Student record synchronized to admin server');
    } else {
      console.log('⚠️  Student record not found in admin server (may be expected if integration is disabled)');
    }
    
    return createdStudent.data.id;
  } catch (error) {
    console.error('❌ Student enrollment sync test failed:', error.message);
    throw error;
  }
}

/**
 * Test activity log synchronization
 */
async function testActivityLogSync() {
  console.log('📋 Testing activity log synchronization...');
  
  try {
    // Perform an action in staff server that should generate activity log
    const response = await fetch(`${STAFF_SERVER_URL}/api/activity-logs?limit=5`, {
      headers: { 'Authorization': `Bearer ${staffToken}` }
    });
    
    if (!response.ok) {
      throw new Error(`Activity logs fetch failed: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ Activity logs retrieved from staff server:', data.data.logs.length, 'entries');
    
    return data.data.logs.length > 0;
  } catch (error) {
    console.error('❌ Activity log sync test failed:', error.message);
    throw error;
  }
}

/**
 * Test KPI data integration
 */
async function testKPIIntegration() {
  console.log('📊 Testing KPI data integration...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/kpis`, {
      headers: { 'Authorization': `Bearer ${staffToken}` }
    });
    
    if (!response.ok) {
      throw new Error(`KPI fetch failed: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ KPI data retrieved from staff server');
    
    if (data.data.financial) {
      console.log('✅ Financial KPI data integrated from admin server');
    } else {
      console.log('⚠️  Financial KPI data not available (integration may be disabled)');
    }
    
    return data.data;
  } catch (error) {
    console.error('❌ KPI integration test failed:', error.message);
    throw error;
  }
}

/**
 * Test reporting integration
 */
async function testReportingIntegration() {
  console.log('📈 Testing reporting integration...');
  
  try {
    const response = await fetch(`${STAFF_SERVER_URL}/api/reports?reportType=combined`, {
      headers: { 'Authorization': `Bearer ${staffToken}` }
    });
    
    if (!response.ok) {
      throw new Error(`Report generation failed: ${response.status}`);
    }
    
    const data = await response.json();
    console.log('✅ Combined report generated successfully');
    
    if (data.data.data.financial) {
      console.log('✅ Financial data included in report');
    } else {
      console.log('⚠️  Financial data not included (integration may be disabled)');
    }
    
    return data.data;
  } catch (error) {
    console.error('❌ Reporting integration test failed:', error.message);
    throw error;
  }
}

/**
 * Run all integration tests
 */
async function runIntegrationTests() {
  console.log('🚀 Starting integration tests for Admin and Staff servers...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  const tests = [
    { name: 'Server Health', fn: testServerHealth },
    { name: 'Authentication', fn: testAuthentication },
    { name: 'Admin Integration Status', fn: testAdminIntegrationStatus },
    { name: 'Student Enrollment Sync', fn: testStudentEnrollmentSync },
    { name: 'Activity Log Sync', fn: testActivityLogSync },
    { name: 'KPI Integration', fn: testKPIIntegration },
    { name: 'Reporting Integration', fn: testReportingIntegration }
  ];
  
  for (const test of tests) {
    try {
      console.log(`\n--- ${test.name} ---`);
      await test.fn();
      results.passed++;
      results.tests.push({ name: test.name, status: 'PASSED' });
    } catch (error) {
      results.failed++;
      results.tests.push({ name: test.name, status: 'FAILED', error: error.message });
    }
  }
  
  // Print summary
  console.log('\n' + '='.repeat(50));
  console.log('INTEGRATION TEST SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total Tests: ${results.passed + results.failed}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log('');
  
  results.tests.forEach(test => {
    const status = test.status === 'PASSED' ? '✅' : '❌';
    console.log(`${status} ${test.name}`);
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
  });
  
  console.log('\n' + '='.repeat(50));
  
  if (results.failed === 0) {
    console.log('🎉 All integration tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some integration tests failed. Check the logs above.');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runIntegrationTests().catch(error => {
    console.error('💥 Integration test suite failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runIntegrationTests,
  testAuthentication,
  testServerHealth,
  testAdminIntegrationStatus,
  testStudentEnrollmentSync,
  testActivityLogSync,
  testKPIIntegration,
  testReportingIntegration
};

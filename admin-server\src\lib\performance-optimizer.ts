/**
 * Performance Optimization Utilities for Admin Server
 * Implements caching, query optimization, and response compression
 */

import { debugLogger } from './debug-logger';

// Simple in-memory cache implementation
class MemoryCache {
  private cache = new Map<string, { data: any; expiry: number }>();
  private maxSize = 1000;

  set(key: string, data: any, ttlSeconds: number = 300) {
    // Clean up expired entries if cache is getting full
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    const expiry = Date.now() + (ttlSeconds * 1000);
    this.cache.set(key, { data, expiry });
    
    debugLogger.debug('CACHE', `Cache SET: ${key} (TTL: ${ttlSeconds}s)`);
  }

  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      debugLogger.debug('CACHE', `Cache MISS: ${key}`);
      return null;
    }

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      debugLogger.debug('CACHE', `Cache EXPIRED: ${key}`);
      return null;
    }

    debugLogger.debug('CACHE', `Cache HIT: ${key}`);
    return entry.data;
  }

  delete(key: string) {
    const deleted = this.cache.delete(key);
    if (deleted) {
      debugLogger.debug('CACHE', `Cache DELETE: ${key}`);
    }
    return deleted;
  }

  clear() {
    const size = this.cache.size;
    this.cache.clear();
    debugLogger.info('CACHE', `Cache cleared: ${size} entries removed`);
  }

  private cleanup() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    debugLogger.debug('CACHE', `Cache cleanup: ${cleaned} expired entries removed`);
  }

  getStats() {
    const now = Date.now();
    let expired = 0;
    
    for (const entry of this.cache.values()) {
      if (now > entry.expiry) {
        expired++;
      }
    }

    return {
      total: this.cache.size,
      expired,
      active: this.cache.size - expired,
      maxSize: this.maxSize
    };
  }
}

// Global cache instance
export const cache = new MemoryCache();

// Database query optimization utilities
export class QueryOptimizer {
  static buildPaginationQuery(baseQuery: string, page: number = 1, limit: number = 10) {
    const offset = (page - 1) * limit;
    return `${baseQuery} LIMIT ${limit} OFFSET ${offset}`;
  }

  static buildSearchQuery(baseQuery: string, searchTerm: string, searchFields: string[]) {
    if (!searchTerm || searchFields.length === 0) {
      return baseQuery;
    }

    const searchConditions = searchFields
      .map(field => `${field} ILIKE $1`)
      .join(' OR ');

    const whereClause = baseQuery.toLowerCase().includes('where') 
      ? ` AND (${searchConditions})`
      : ` WHERE (${searchConditions})`;

    return baseQuery + whereClause;
  }

  static buildSortQuery(baseQuery: string, sortBy: string, sortOrder: 'ASC' | 'DESC' = 'ASC') {
    // Validate sort field to prevent SQL injection
    const allowedSortFields = ['created_at', 'updated_at', 'name', 'email', 'timestamp'];
    
    if (!allowedSortFields.includes(sortBy)) {
      sortBy = 'created_at';
    }

    return `${baseQuery} ORDER BY ${sortBy} ${sortOrder}`;
  }

  static buildFilterQuery(baseQuery: string, filters: Record<string, any>) {
    let query = baseQuery;
    const conditions: string[] = [];
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        conditions.push(`${key} = $${conditions.length + 1}`);
      }
    });

    if (conditions.length > 0) {
      const whereClause = query.toLowerCase().includes('where') 
        ? ` AND ${conditions.join(' AND ')}`
        : ` WHERE ${conditions.join(' AND ')}`;
      
      query += whereClause;
    }

    return query;
  }
}

// Response compression and optimization
export class ResponseOptimizer {
  static compressResponse(data: any): any {
    // Remove null/undefined values to reduce payload size
    return this.removeEmptyValues(data);
  }

  private static removeEmptyValues(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.removeEmptyValues(item));
    }

    if (obj !== null && typeof obj === 'object') {
      const cleaned: any = {};
      
      Object.entries(obj).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          cleaned[key] = this.removeEmptyValues(value);
        }
      });

      return cleaned;
    }

    return obj;
  }

  static paginateResults<T>(
    items: T[], 
    page: number = 1, 
    limit: number = 10
  ): { items: T[]; pagination: any } {
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = items.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      pagination: {
        page,
        limit,
        total: items.length,
        totalPages: Math.ceil(items.length / limit),
        hasNext: endIndex < items.length,
        hasPrev: page > 1
      }
    };
  }
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static metrics = new Map<string, { count: number; totalTime: number; avgTime: number }>();

  static startTimer(operation: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration);
      debugLogger.debug('PERFORMANCE', `${operation} completed in ${duration}ms`);
    };
  }

  private static recordMetric(operation: string, duration: number) {
    const existing = this.metrics.get(operation) || { count: 0, totalTime: 0, avgTime: 0 };
    
    existing.count++;
    existing.totalTime += duration;
    existing.avgTime = existing.totalTime / existing.count;
    
    this.metrics.set(operation, existing);
  }

  static getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((metric, operation) => {
      result[operation] = {
        count: metric.count,
        totalTime: metric.totalTime,
        avgTime: Math.round(metric.avgTime * 100) / 100,
        minTime: metric.avgTime, // Simplified for now
        maxTime: metric.avgTime  // Simplified for now
      };
    });

    return result;
  }

  static clearMetrics() {
    this.metrics.clear();
    debugLogger.info('PERFORMANCE', 'Performance metrics cleared');
  }
}

// Middleware for performance monitoring
export function createPerformanceMiddleware() {
  return (req: any, res: any, next: any) => {
    const stopTimer = PerformanceMonitor.startTimer(`${req.method} ${req.path}`);
    
    const originalSend = res.send;
    res.send = function(data: any) {
      stopTimer();
      return originalSend.call(this, data);
    };

    next();
  };
}

// Database connection pooling utilities
export class ConnectionPool {
  private static connections = new Map<string, any>();
  private static maxConnections = 10;

  static async getConnection(connectionString: string): Promise<any> {
    // This is a simplified implementation
    // In a real application, you'd use a proper connection pool library
    
    if (!this.connections.has(connectionString)) {
      debugLogger.info('DATABASE', `Creating new connection pool for ${connectionString}`);
      // Create connection pool here
      this.connections.set(connectionString, { active: true, created: Date.now() });
    }

    return this.connections.get(connectionString);
  }

  static closeAllConnections() {
    debugLogger.info('DATABASE', `Closing ${this.connections.size} connection pools`);
    this.connections.clear();
  }

  static getConnectionStats() {
    return {
      totalPools: this.connections.size,
      maxConnections: this.maxConnections,
      activeConnections: Array.from(this.connections.values()).filter(conn => conn.active).length
    };
  }
}

// API rate limiting
export class RateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>();
  private static maxRequests = 100; // per minute
  private static windowMs = 60 * 1000; // 1 minute

  static checkLimit(identifier: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const windowStart = now - this.windowMs;
    
    // Clean up old entries
    for (const [key, data] of this.requests.entries()) {
      if (data.resetTime < now) {
        this.requests.delete(key);
      }
    }

    const current = this.requests.get(identifier) || { count: 0, resetTime: now + this.windowMs };
    
    if (current.resetTime < now) {
      // Reset window
      current.count = 0;
      current.resetTime = now + this.windowMs;
    }

    current.count++;
    this.requests.set(identifier, current);

    const allowed = current.count <= this.maxRequests;
    const remaining = Math.max(0, this.maxRequests - current.count);

    if (!allowed) {
      debugLogger.warn('RATE_LIMIT', `Rate limit exceeded for ${identifier}: ${current.count}/${this.maxRequests}`);
    }

    return {
      allowed,
      remaining,
      resetTime: current.resetTime
    };
  }
}

// Export all utilities
export default {
  cache,
  QueryOptimizer,
  ResponseOptimizer,
  PerformanceMonitor,
  ConnectionPool,
  RateLimiter,
  createPerformanceMiddleware
};

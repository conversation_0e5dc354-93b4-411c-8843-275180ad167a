#!/usr/bin/env node

/**
 * Test script to verify activity logging integration between staff and admin servers
 */

const fetch = require('node-fetch');

const ADMIN_SERVER_URL = 'http://localhost:3000';
const STAFF_SERVER_URL = 'http://localhost:3003';

async function testActivityLogging() {
  console.log('🧪 Testing Activity Logging Integration...\n');

  try {
    // 1. Login to admin server
    console.log('1. Logging into admin server...');
    const adminLoginResponse = await fetch(`${ADMIN_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!adminLoginResponse.ok) {
      throw new Error(`Admin login failed: ${adminLoginResponse.status}`);
    }

    const adminLoginData = await adminLoginResponse.json();
    const adminToken = adminLoginData.token;
    console.log('✅ Admin login successful');

    // 2. Login to staff server
    console.log('\n2. Logging into staff server...');
    const staffLoginResponse = await fetch(`${STAFF_SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'management123'
      })
    });

    if (!staffLoginResponse.ok) {
      throw new Error(`Staff login failed: ${staffLoginResponse.status}`);
    }

    const staffLoginData = await staffLoginResponse.json();
    const staffToken = staffLoginData.token;
    console.log('✅ Staff login successful');

    // 3. Create a student in staff server
    console.log('\n3. Creating student in staff server...');
    const studentData = {
      firstName: 'Test',
      lastName: 'Student',
      email: '<EMAIL>',
      phone: '+1234567890',
      enrollmentDate: new Date().toISOString().split('T')[0]
    };

    const createStudentResponse = await fetch(`${STAFF_SERVER_URL}/api/students`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${staffToken}`
      },
      body: JSON.stringify(studentData)
    });

    if (!createStudentResponse.ok) {
      const errorData = await createStudentResponse.json();
      throw new Error(`Student creation failed: ${createStudentResponse.status} - ${errorData.message}`);
    }

    const studentResult = await createStudentResponse.json();
    console.log('✅ Student created successfully:', studentResult.data.id);

    // 4. Wait a moment for activity sync
    console.log('\n4. Waiting for activity sync...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 5. Check activity logs in admin server
    console.log('\n5. Checking activity logs in admin server...');
    const activityLogsResponse = await fetch(`${ADMIN_SERVER_URL}/api/activity-logs?limit=10`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });

    if (!activityLogsResponse.ok) {
      throw new Error(`Activity logs fetch failed: ${activityLogsResponse.status}`);
    }

    const activityLogsData = await activityLogsResponse.json();
    console.log('✅ Activity logs retrieved');

    // 6. Look for student creation activity
    const studentCreationLogs = activityLogsData.data.filter(log => 
      log.action === 'CREATE' && 
      log.resourceType === 'STUDENT' &&
      log.description && log.description.includes('Test Student')
    );

    if (studentCreationLogs.length > 0) {
      console.log('✅ Student creation activity found in admin logs!');
      console.log('   Description:', studentCreationLogs[0].description);
      console.log('   Timestamp:', studentCreationLogs[0].timestamp);
    } else {
      console.log('❌ Student creation activity NOT found in admin logs');
      console.log('   Recent activities:');
      activityLogsData.data.slice(0, 5).forEach(log => {
        console.log(`   - ${log.action} ${log.resourceType}: ${log.description}`);
      });
    }

    console.log('\n🎉 Activity logging test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testActivityLogging();

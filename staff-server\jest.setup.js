import '@testing-library/jest-dom'

// Add missing global objects for Node.js environment
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder
global.Request = Request
global.Response = Response

// Use real environment variables from .env.local
// No need to override DATABASE_URL - use real staff database
process.env.JWT_SECRET = process.env.JWT_SECRET || 'admin-jwt-secret-key-innovative-centre-2024'
process.env.NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET || 'staff-nextauth-secret-innovative-centre-2024'
process.env.NEXT_PUBLIC_APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3003'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return '/'
  },
}))

// Mock fetch globally
global.fetch = jest.fn()

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks()
})
